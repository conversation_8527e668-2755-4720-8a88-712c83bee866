import { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react'
import { MockWebSocket, mockApiService } from '../services/mockApi'

const WebSocketContext = createContext()

export function useWebSocket() {
  const context = useContext(WebSocketContext)
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider')
  }
  return context
}

export function WebSocketProvider({ children }) {
  // Generate a persistent student ID
  const studentIdRef = useRef(`student-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`)
  
  // State management
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [messages, setMessages] = useState([])
  const [error, setError] = useState(null)
  const [typingAgents, setTypingAgents] = useState(new Set())
  const [isDemoMode, setIsDemoMode] = useState(false)
  
  // WebSocket reference
  const ws = useRef(null)
  const reconnectTimeoutRef = useRef(null)
  const reconnectAttemptsRef = useRef(0)
  const maxReconnectAttempts = 5
  const reconnectDelay = 3000 // 3 seconds
  
  // WebSocket URL
  const wsUrl = `${import.meta.env.VITE_WS_URL || 'ws://localhost:8000'}/ws/${studentIdRef.current}`
  
  // Check if backend is ready before connecting
  const checkBackendHealth = useCallback(async () => {
    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:8000'}/health`, {
        timeout: 2000
      })
      return response.ok
    } catch (error) {
      console.log('Backend health check failed, switching to demo mode:', error.message)
      return false
    }
  }, [])
  
  // Switch to demo mode
  const switchToDemoMode = useCallback(() => {
    console.log('Switching to demo mode - backend not available')
    setIsDemoMode(true)
    setError('Demo Mode: Backend not available, using mock responses')
    
    // Connect using mock WebSocket
    if (ws.current) {
      ws.current.close()
    }
    
    ws.current = new MockWebSocket(wsUrl)
    ws.current.onopen = () => {
      console.log('Mock WebSocket connection established')
      setIsConnected(true)
      setIsConnecting(false)
      setError('Demo Mode Active')
      reconnectAttemptsRef.current = 0
    }
    
    ws.current.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        
        switch (data.type) {
          case 'welcome':
            console.log('Welcome message:', data.message)
            setMessages(prev => [...prev, {
              id: Date.now(),
              type: 'system',
              content: data.message,
              timestamp: new Date().toISOString()
            }])
            break
            
          case 'agent_response':
            setMessages(prev => [...prev, {
              id: Date.now(),
              type: 'agent',
              agent: data.agent,
              content: data.content,
              timestamp: data.timestamp,
              data: data.data // Include structured data for consistency
            }])
            break
            
          case 'typing':
            if (data.is_typing) {
              setTypingAgents(prev => new Set([...prev, data.agent]))
            } else {
              setTypingAgents(prev => {
                const newSet = new Set(prev)
                newSet.delete(data.agent)
                return newSet
              })
            }
            break
            
          default:
            console.log('Unknown message type:', data.type)
        }
      } catch (error) {
        console.error('Message parse error:', error)
      }
    }
    
    ws.current.onerror = () => {
      console.error('Mock WebSocket error')
    }
    
    ws.current.onclose = () => {
      console.log('Mock WebSocket closed')
      setIsConnected(false)
      setIsConnecting(false)
    }
  }, [wsUrl])
  
  // Streaming message reference
  const streamingMessageRef = useRef(null)
  
  // Handle stream start
  const handleStreamStart = useCallback((data) => {
    const { agent, stream_id } = data
    
    streamingMessageRef.current = {
      id: Date.now(),
      stream_id,
      type: 'agent',
      agent,
      content: '',
      timestamp: new Date().toISOString(),
      isStreaming: true
    }
    
    setMessages(prev => [...prev, streamingMessageRef.current])
  }, [])
  
  // Handle stream chunk
  const handleStreamChunk = useCallback((data) => {
    const { chunk, stream_id } = data
    
    if (streamingMessageRef.current && streamingMessageRef.current.stream_id === stream_id) {
      streamingMessageRef.current.content += chunk
      
      setMessages(prev => prev.map(msg => 
        msg.id === streamingMessageRef.current?.id 
          ? { ...msg, content: streamingMessageRef.current.content }
          : msg
      ))
    }
  }, [])
  
  // Handle stream end
  const handleStreamEnd = useCallback((data) => {
    const { stream_id } = data
    
    if (streamingMessageRef.current && streamingMessageRef.current.stream_id === stream_id) {
      setMessages(prev => prev.map(msg => 
        msg.id === streamingMessageRef.current?.id 
          ? { ...msg, isStreaming: false }
          : msg
      ))
      
      streamingMessageRef.current = null
    }
  }, [])
  
  // Send message function
  const sendMessage = useCallback((message, requestType = null) => {
    if (!ws.current || ws.current.readyState !== WebSocket.OPEN) {
      console.error('WebSocket connection is not open')
      return false
    }
    
    try {
      const data = {
        type: 'chat',
        content: message,
        request_type: requestType,
        timestamp: new Date().toISOString()
      }
      
      ws.current.send(JSON.stringify(data))
      
      // Add user message to list
      setMessages(prev => [...prev, {
        id: Date.now(),
        type: 'user',
        content: message,
        timestamp: new Date().toISOString()
      }])
      
      return true
    } catch (error) {
      console.error('Failed to send message:', error)
      setError('Failed to send message')
      return false
    }
  }, [])
  
  // Connect to WebSocket
  const connect = useCallback(async () => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected')
      return
    }
    
    // Check if backend is healthy before attempting connection
    console.log('Checking backend health before WebSocket connection...')
    const isHealthy = await checkBackendHealth()
    if (!isHealthy) {
      console.log('Backend not healthy, switching to demo mode...')
      switchToDemoMode()
      return
    }
    
    console.log('Backend is healthy, proceeding with WebSocket connection')
    
    // Close existing connection if any
    if (ws.current) {
      ws.current.close()
    }
    
    setIsConnecting(true)
    setError(null)
    
    try {
      ws.current = new WebSocket(wsUrl)
      
      ws.current.onopen = () => {
        console.log('WebSocket connection established')
        setIsConnected(true)
        setIsConnecting(false)
        setError(null)
        reconnectAttemptsRef.current = 0
      }
      
      ws.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          
          switch (data.type) {
            case 'agent_response':
              setMessages(prev => [...prev, {
                id: Date.now(),
                type: 'agent',
                agent: data.agent,
                content: data.content,
                timestamp: data.timestamp || new Date().toISOString(),
                metadata: data.metadata,
                data: data.data // Include structured data for practice questions
              }])
              break
              
            case 'typing':
              if (data.is_typing) {
                setTypingAgents(prev => new Set([...prev, data.agent || 'YKS Genius']))
              } else {
                setTypingAgents(prev => {
                  const newSet = new Set(prev)
                  newSet.delete(data.agent || 'YKS Genius')
                  return newSet
                })
              }
              break
              
            case 'typing_start':
              setTypingAgents(prev => new Set([...prev, data.agent]))
              break
              
            case 'typing_stop':
              setTypingAgents(prev => {
                const newSet = new Set(prev)
                newSet.delete(data.agent)
                return newSet
              })
              break
              
            case 'error':
              console.error('Server error:', data.message)
              setError(data.message)
              break
              
            case 'connection_established':
              console.log('Connection confirmed:', data.message)
              break
              
            case 'welcome':
              console.log('Welcome message:', data.message)
              setMessages(prev => [...prev, {
                id: Date.now(),
                type: 'system',
                content: data.message || 'Welcome to YKS Genius!',
                timestamp: new Date().toISOString()
              }])
              break
              
            case 'stream_start':
              handleStreamStart(data)
              break
              
            case 'stream_chunk':
              handleStreamChunk(data)
              break
              
            case 'stream_end':
              handleStreamEnd(data)
              break
              
            default:
              console.log('Unknown message type:', data.type)
          }
        } catch (error) {
          console.error('Message parse error:', error)
        }
      }
      
      ws.current.onerror = (error) => {
        console.error('WebSocket error:', error)
        setError('Connection error occurred')
      }
      
      ws.current.onclose = (event) => {
        console.log('WebSocket connection closed:', event.code, event.reason)
        setIsConnected(false)
        setIsConnecting(false)
        
        // Auto reconnect
        if (reconnectAttemptsRef.current < maxReconnectAttempts && !event.wasClean) {
          reconnectAttemptsRef.current++
          console.log(`Reconnection attempt ${reconnectAttemptsRef.current}/${maxReconnectAttempts}`)
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect()
          }, reconnectDelay * reconnectAttemptsRef.current)
        } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
          setError('Could not establish connection. Please refresh the page.')
        }
      }
      
    } catch (error) {
      console.error('WebSocket connection error:', error)
      setIsConnecting(false)
      setError('Could not establish connection')
    }
  }, [wsUrl, handleStreamStart, handleStreamChunk, handleStreamEnd, checkBackendHealth, switchToDemoMode])
  
  // Disconnect
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }
    
    if (ws.current) {
      ws.current.close(1000, 'User disconnected')
      ws.current = null
    }
    
    setIsConnected(false)
    setIsConnecting(false)
    setTypingAgents(new Set())
  }, [])
  
  // Clear messages
  const clearMessages = useCallback(() => {
    setMessages([])
  }, [])
  
  // Connect on mount
  useEffect(() => {
    connect()
    
    return () => {
      disconnect()
    }
  }, [connect, disconnect])
  
  // Handle visibility change
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !isConnected && !isConnecting) {
        console.log('Page became visible, reconnecting...')
        connect()
      }
    }
    
    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [isConnected, isConnecting, connect])
  
  const value = {
    // State
    isConnected,
    isConnecting,
    messages,
    error,
    typingAgents: Array.from(typingAgents),
    isDemoMode,
    
    // Actions
    sendMessage,
    connect,
    disconnect,
    clearMessages,
    
    // Helper info
    reconnectAttempts: reconnectAttemptsRef.current,
    maxReconnectAttempts
  }
  
  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  )
}