/**
 * Mock API service for demo purposes when backend is not available
 */

// Mock responses for different user inputs
const mockResponses = {
  'plan': {
    agent: 'Strateji Uzmanı',
    content: `**Strateji Uzmanı**: Sizin için kişiselleştirilmiş bir çalışma planı hazırlıyorum!

**Haftalık Çalışma Programı:**
• **Pazartesi-Çarşamba-Cuma**: Matematik (2 saat)
• **Salı-Perşembe**: Fizik (1.5 saat) 
• **Cumartesi**: Kimya (2 saat)
• **Pazar**: Türkçe & Edebiyat (1.5 saat)

**Önerilerim:**
• Her gün 30 dk konu tekrarı
• Haftada 2 deneme sınavı
• Zor konular için extra pratik

Bu planı sizin seviyenize göre ayarlayabiliriz!`
  },
  'anlat': {
    agent: 'Konu Anlatım Uzmanı',
    content: `**Konu Anlatım Uzmanı**: <PERSON>it konusunu size anlatayım!

**Limit Kavramı:**
Limit, bir fonksiyonun belirli bir noktaya yaklaşırken aldığı değerdir.

**Temel Limit Kuralları:**
1. **Sabit Fonksiyon**: lim(c) = c
2. **Toplam Kuralı**: lim(f+g) = lim(f) + lim(g)  
3. **Çarpım Kuralı**: lim(f×g) = lim(f) × lim(g)

**Örnek Problem:**
lim(x→2) (x² + 3x - 1) = 4 + 6 - 1 = 9

Hangi konuda daha detay istiyorsunuz?`
  },
  'soru': {
    agent: 'Pratik Uzmanı',
    content: `**Pratik Uzmanı**: Matematik dersi için sorularınızı hazırladım!`,
    data: {
      quiz_mode: true,
      subject: 'matematik',
      total: 2,
      questions: [
        {
          question: 'lim(x→3) (x² - 9)/(x - 3) ifadesinin değeri nedir?',
          options: {
            A: '0',
            B: '3', 
            C: '6',
            D: '9',
            E: '∞'
          },
          correct_answer: 'C',
          explanation: 'Pay ve paydayı faktörlere ayırın: (x²-9)/(x-3) = (x+3)(x-3)/(x-3) = x+3. Limit x→3 için x+3 = 6',
          topic: 'Limit',
          difficulty: 'orta'
        },
        {
          question: 'f(x) = 2x + 1 fonksiyonunun x = 5 noktasındaki türevi nedir?',
          options: {
            A: '1',
            B: '2',
            C: '5', 
            D: '10',
            E: '11'
          },
          correct_answer: 'B',
          explanation: 'f(x) = 2x + 1 fonksiyonunun türevi f\'(x) = 2 olur. Türev sabit olduğu için her noktada 2\'dir.',
          topic: 'Türev',
          difficulty: 'kolay'
        }
      ]
    }
  },
  'motivasyon': {
    agent: 'Motivasyon Koçu',
    content: `**Motivasyon Koçu**: Sen harikasın! YKS yolculuğunda her gün ilerliyorsun.

**Unutma:**
• Her başarılı insan, bir zamanlar başlangıç seviyesindeydi
• Tutarlılık, mükemmellikten daha değerlidir
• Her gün %1 gelişim = yılda %37 kat daha iyi

**Bugünkü Motivasyon:**
"Bugün çözemediğin soru, yarın en kolay soruna dönüşebilir!"

Sen bu sınavı geçeceksin! Ben sana inanıyorum!`
  },
  'analiz': {
    agent: 'Analiz Uzmanı',
    content: `**Analiz Uzmanı**: Performans raporunuz hazır!

**Bu Hafta:**
• Çözülen soru sayısı: 127
• Doğru cevap oranı: %73
• Çalışma süresi: 12.5 saat

**Güçlü Olduğunuz Konular:**
• Matematik - Fonksiyonlar (%89)
• Fizik - Kuvvet (%81)

**Gelişim Alanları:**
• Matematik - Türev (%62)
• Kimya - Mol Kavramı (%45)

**Öneri:** Bu hafta türev konusuna odaklanın!`
  }
};

// Mock WebSocket class
export class MockWebSocket {
  constructor(url) {
    this.url = url;
    this.readyState = WebSocket.CONNECTING;
    this.onopen = null;
    this.onmessage = null;
    this.onerror = null;
    this.onclose = null;
    
    // Simulate connection
    setTimeout(() => {
      this.readyState = WebSocket.OPEN;
      if (this.onopen) {
        this.onopen({ type: 'open' });
      }
      
      // Send welcome message
      setTimeout(() => {
        if (this.onmessage) {
          this.onmessage({
            data: JSON.stringify({
              type: 'welcome',
              message: 'YKS Genius\'a hoş geldiniz! (Demo Mode)',
              student_id: 'demo_student',
              agents: [
                {id: 'strategy', name: 'Strateji Uzmanı', status: 'active'},
                {id: 'content', name: 'Konu Anlatım Uzmanı', status: 'active'},
                {id: 'practice', name: 'Pratik Uzmanı', status: 'active'},
                {id: 'analytics', name: 'Analiz Uzmanı', status: 'active'},
                {id: 'mentor', name: 'Motivasyon Koçu', status: 'active'}
              ]
            })
          });
        }
      }, 500);
    }, 100);
  }
  
  send(data) {
    if (this.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket not open');
      return;
    }
    
    try {
      const message = JSON.parse(data);
      if (message.type === 'chat') {
        this.handleChatMessage(message.content);
      }
    } catch (error) {
      console.error('Invalid message format:', error);
    }
  }
  
  handleChatMessage(content) {
    const contentLower = content.toLowerCase();
    
    // Determine response type
    let responseKey = 'default';
    if (contentLower.includes('plan') || contentLower.includes('çalışma')) {
      responseKey = 'plan';
    } else if (contentLower.includes('anlat') || contentLower.includes('konu') || contentLower.includes('limit')) {
      responseKey = 'anlat';
    } else if (contentLower.includes('soru') || contentLower.includes('test')) {
      responseKey = 'soru';
    } else if (contentLower.includes('motivasyon') || contentLower.includes('moral')) {
      responseKey = 'motivasyon';
    } else if (contentLower.includes('analiz') || contentLower.includes('performans')) {
      responseKey = 'analiz';
    }
    
    const response = mockResponses[responseKey] || {
      agent: 'YKS Genius',
      content: `**YKS Genius**: Merhaba! Size nasıl yardımcı olabilirim?

**Yapabileceklerim:**
• Kişiselleştirilmiş çalışma planı oluşturma
• Konuları basit ve anlaşılır şekilde anlatma  
• Seviyenize uygun sorular üretme
• Performansınızı analiz etme
• Motivasyon ve destek sağlama

**Örnek sorular:**
• "Bana matematik çalışma planı yap"
• "Limit konusunu anlat"
• "5 matematik sorusu sor"
• "Performansımı göster"
• "Motivasyona ihtiyacım var"

Ne öğrenmek istiyorsunuz?`
    };
    
    // Send typing indicator
    setTimeout(() => {
      if (this.onmessage) {
        this.onmessage({
          data: JSON.stringify({
            type: 'typing',
            is_typing: true,
            agent: response.agent
          })
        });
      }
    }, 200);
    
    // Send response with streaming effect
    setTimeout(() => {
      if (this.onmessage) {
        this.onmessage({
          data: JSON.stringify({
            type: 'typing',
            is_typing: false,
            agent: response.agent
          })
        });
        
        // Send actual response
        this.onmessage({
          data: JSON.stringify({
            type: 'agent_response',
            agent: response.agent,
            content: response.content,
            data: response.data || {},
            timestamp: new Date().toISOString()
          })
        });
      }
    }, 1500);
  }
  
  close() {
    this.readyState = WebSocket.CLOSED;
    if (this.onclose) {
      this.onclose({ code: 1000, reason: 'Normal closure' });
    }
  }
}

// Mock API service
export const mockApiService = {
  async checkHealth() {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          ok: true,
          json: () => Promise.resolve({
            status: 'healthy',
            mode: 'demo',
            timestamp: new Date().toISOString()
          })
        });
      }, 100);
    });
  },
  
  async sendChatMessage(message) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: 'Demo mode response',
          agent: 'demo',
          timestamp: new Date().toISOString()
        });
      }, 500);
    });
  }
};

export default mockApiService;