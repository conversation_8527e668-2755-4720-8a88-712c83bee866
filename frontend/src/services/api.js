/**
 * YKS Genius API Service
 * Centralized API communication with backend
 */

import axios from 'axios';

// API Base Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://127.0.0.1:8000';
const WS_BASE_URL = import.meta.env.VITE_WS_URL || 'ws://127.0.0.1:8000';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor - add auth headers if needed
api.interceptors.request.use(
  (config) => {
    // Demo mode - add demo headers
    config.headers['X-Demo-Mode'] = 'true';
    
    // Add student ID for demo
    const studentId = localStorage.getItem('demo_student_id') || 'demo_student';
    config.headers['X-Student-ID'] = studentId;
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor - handle errors globally
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    
    if (error.response?.status === 401) {
      // Handle authentication errors
      console.log('Authentication required');
    } else if (error.response?.status >= 500) {
      // Handle server errors
      console.error('Server error:', error.response.data?.detail || error.message);
    }
    
    return Promise.reject(error);
  }
);

// API Service Functions
export const apiService = {
  // Health & System
  async getHealth() {
    const response = await api.get('/health');
    return response.data;
  },

  async getAPIInfo() {
    const response = await api.get('/api/v1/info');
    return response.data;
  },

  async getWelcome() {
    const response = await api.get('/api/v1/welcome');
    return response.data;
  },

  async testSystem() {
    const response = await api.get('/api/v1/test');
    return response.data;
  },

  // Chat & AI Agents
  async sendChatMessage(message, agentType = null, context = {}) {
    const response = await api.post('/api/v1/chat', {
      message,
      agent_type: agentType,
      context,
      student_id: 'demo_student'
    });
    return response.data;
  },

  // Student Management (REST API)
  async createStudent(studentData) {
    const response = await api.post('/api/v1/students', studentData);
    return response.data;
  },

  async getStudent(studentId) {
    const response = await api.get(`/api/v1/students/${studentId}`, {
      params: { demo_mode: true }
    });
    return response.data;
  },

  async updateStudent(studentId, updates) {
    const response = await api.put(`/api/v1/students/${studentId}`, updates);
    return response.data;
  },

  // Study Plans
  async getStudyPlan(studentId) {
    const response = await api.get(`/api/v1/students/${studentId}/study-plan`, {
      params: { demo_mode: true }
    });
    return response.data;
  },

  async generateStudyPlan(studentId, planRequest) {
    const response = await api.post(`/api/v1/students/${studentId}/study-plan/generate`, planRequest);
    return response.data;
  },

  // Questions & Practice
  async generateQuestions(questionRequest) {
    const response = await api.post('/api/v1/questions/generate', questionRequest);
    return response.data;
  },

  async submitAnswer(questionId, answerData) {
    const response = await api.post(`/api/v1/questions/${questionId}/answer`, answerData);
    return response.data;
  },

  // Analytics
  async getAnalytics(studentId, period = 'week') {
    const response = await api.get(`/api/v1/students/${studentId}/analytics`, {
      params: { period, demo_mode: true }
    });
    return response.data;
  },

  // Content Search
  async searchContent(query, filters = {}) {
    const params = {
      q: query,
      demo_mode: true,
      ...filters
    };
    const response = await api.get('/api/v1/content/search', { params });
    return response.data;
  },

  // Topics
  async getTopics(examType = 'TYT', subject = null) {
    const params = { exam_type: examType };
    if (subject) params.subject = subject;
    
    const response = await api.get('/api/v1/topics', { params });
    return response.data;
  },

  // Sessions
  async getActiveSessions() {
    const response = await api.get('/api/v1/sessions/active', {
      params: { demo_mode: true }
    });
    return response.data;
  },

  // Agents Info
  async getAgents() {
    const response = await api.get('/api/v1/agents');
    return response.data;
  },

  // Admin (for demo)
  async getSystemMetrics() {
    const response = await api.get('/api/v1/admin/metrics', {
      headers: { 'X-Admin-Key': 'demo-admin-key' }
    });
    return response.data;
  }
};

// WebSocket Service
export class WebSocketService {
  constructor() {
    this.ws = null;
    this.connected = false;
    this.listeners = new Map();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
  }

  connect(studentId = 'demo_student') {
    const wsUrl = `${WS_BASE_URL}/ws/${studentId}`;
    
    try {
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = () => {
        console.log('✅ WebSocket connected');
        this.connected = true;
        this.reconnectAttempts = 0;
        this.emit('connected');
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.emit('message', data);
          
          // Handle specific message types
          if (data.type) {
            this.emit(data.type, data);
          }
        } catch (error) {
          console.error('WebSocket message parse error:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.connected = false;
        this.emit('disconnected');
        
        // Auto-reconnect
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          setTimeout(() => {
            this.reconnectAttempts++;
            this.connect(studentId);
          }, 2000 * this.reconnectAttempts);
        }
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.emit('error', error);
      };

    } catch (error) {
      console.error('WebSocket connection failed:', error);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
      this.connected = false;
    }
  }

  send(data) {
    if (this.connected && this.ws) {
      this.ws.send(JSON.stringify(data));
    } else {
      console.warn('WebSocket not connected, message not sent:', data);
    }
  }

  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in WebSocket listener for ${event}:`, error);
        }
      });
    }
  }
}

// Create singleton WebSocket instance
export const wsService = new WebSocketService();

// API Configuration
export const API_CONFIG = {
  BASE_URL: API_BASE_URL,
  WS_URL: WS_BASE_URL,
  TIMEOUT: 10000,
  DEMO_MODE: true
};

export default api;