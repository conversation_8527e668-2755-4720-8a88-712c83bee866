/**
 * Profil sayfası
 * Kullanıcı ayarları ve tercihler
 */
export default function ProfilePage() {
  return (
    <div className="p-6">
      <h1 className="text-2xl font-semibold mb-6">Profil</h1>
      
      <div className="max-w-2xl">
        <div className="card mb-6">
          <h2 className="text-lg font-medium mb-4">Kişisel Bilgiler</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Ad Soyad</label>
              <input type="text" className="input" defaultValue="Demo Öğrenci" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">E-posta</label>
              <input type="email" className="input" defaultValue="<EMAIL>" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Sınıf</label>
              <select className="input">
                <option>11. Sınıf</option>
                <option selected>12. Sınıf</option>
                <option>Mezun</option>
              </select>
            </div>
          </div>
        </div>
        
        <div className="card mb-6">
          <h2 className="text-lg font-medium mb-4">Hedefler</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Hedef Üniversite</label>
              <input type="text" className="input" defaultValue="Boğaziçi Üniversitesi" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Hedef Bölüm</label>
              <input type="text" className="input" defaultValue="Bilgisayar Mühendisliği" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Hedef Sıralama</label>
              <input type="text" className="input" placeholder="Örn: İlk 10.000" />
            </div>
          </div>
        </div>
        
        <div className="card mb-6">
          <h2 className="text-lg font-medium mb-4">Çalışma Tercihleri</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Günlük Çalışma Hedefi</label>
              <input type="range" min="1" max="12" defaultValue="6" className="w-full" />
              <p className="text-sm text-gray-500 mt-1">6 saat</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Zorluk Seviyesi</label>
              <div className="flex space-x-4">
                <label className="flex items-center">
                  <input type="radio" name="difficulty" className="mr-2" />
                  <span>Kolay</span>
                </label>
                <label className="flex items-center">
                  <input type="radio" name="difficulty" className="mr-2" defaultChecked />
                  <span>Orta</span>
                </label>
                <label className="flex items-center">
                  <input type="radio" name="difficulty" className="mr-2" />
                  <span>Zor</span>
                </label>
              </div>
            </div>
          </div>
        </div>
        
        <div className="flex space-x-4">
          <button className="btn btn-primary">Kaydet</button>
          <button className="btn btn-outline">İptal</button>
        </div>
      </div>
    </div>
  )
}