import { useNavigate } from 'react-router-dom'
import { useState } from 'react'

/**
 * Ana sayfa - <PERSON><PERSON><PERSON> ya<PERSON>am<PERSON> kullanıcılar için
 * YKS Genius tanıtımı ve hızlı başlangıç
 */
export default function HomePage() {
  const navigate = useNavigate()
  const [hoveredAgent, setHoveredAgent] = useState(null)
  
  const agents = [
    {
      id: 'strategy',
      name: 'Strateji Uzmanı',
      icon: '🎯',
      color: 'purple',
      description: 'Hedeflerine uygun kişiselleştirilmiş çalışma planı oluşturur',
      features: ['Haftalık program', 'Konu önceliklendirme', 'Zaman yönetimi']
    },
    {
      id: 'content',
      name: '<PERSON>nu Anlatım Uzmanı',
      icon: '📚',
      color: 'blue',
      description: 'YKS müfredatındaki tüm konuları anlaşılır şekilde açıklar',
      features: ['Detaylı anlatım', '<PERSON><PERSON><PERSON> çözümler', '<PERSON><PERSON><PERSON> kartları']
    },
    {
      id: 'practice',
      name: '<PERSON><PERSON><PERSON>',
      icon: '✏️',
      color: 'green',
      description: 'Seviyene uygun sorular üretir ve çözüm stratejileri öğretir',
      features: ['Adaptif sorular', 'Adım adım çözüm', 'Hata analizi']
    },
    {
      id: 'analytics',
      name: 'Analiz Uzmanı',
      icon: '📊',
      color: 'orange',
      description: 'Performansını takip eder ve gelişim önerileri sunar',
      features: ['İlerleme takibi', 'Güçlü/zayıf yanlar', 'Başarı tahmini']
    },
    {
      id: 'mentor',
      name: 'Motivasyon Koçu',
      icon: '💪',
      color: 'pink',
      description: 'Motivasyonunu yüksek tutar ve stres yönetimi konusunda destek olur',
      features: ['Moral desteği', 'Stres yönetimi', 'Başarı hikayeleri']
    }
  ]
  
  const stats = [
    { value: '2.25M+', label: 'YKS Adayı' },
    { value: '5', label: 'AI Uzman Ajan' },
    { value: '7/24', label: 'Destek' },
    { value: 'Ücretsiz', label: 'Kullanım' }
  ]
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-primary-50 to-white">
      {/* Header */}
      <header className="container mx-auto px-4 py-6">
        <nav className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-3xl">🎯</span>
            <h1 className="text-2xl font-bold text-primary-600">YKS Genius</h1>
          </div>
          <button
            onClick={() => navigate('/app')}
            className="btn btn-primary"
          >
            Hemen Başla
          </button>
        </nav>
      </header>
      
      {/* Hero Section */}
      <section className="container mx-auto px-4 py-16 text-center">
        <h2 className="text-5xl font-bold text-gray-900 mb-6">
          YKS'de Başarıya Giden Yolda<br />
          <span className="text-primary-600">AI Destekli</span> Rehberin
        </h2>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
          5 uzman AI ajan ile kişiselleştirilmiş çalışma planı, adaptif sorular ve 
          7/24 mentorluk desteği. Hayalindeki üniversiteye bir adım daha yaklaş!
        </p>
        <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
          <button
            onClick={() => navigate('/app')}
            className="btn btn-primary text-lg px-8 py-3"
          >
            Ücretsiz Başla
          </button>
          <button className="btn btn-outline text-lg px-8 py-3">
            Nasıl Çalışır?
          </button>
        </div>
      </section>
      
      {/* İstatistikler */}
      <section className="bg-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <p className="text-3xl font-bold text-primary-600">{stat.value}</p>
                <p className="text-gray-600 mt-1">{stat.label}</p>
              </div>
            ))}
          </div>
        </div>
      </section>
      
      {/* Ajanlar */}
      <section className="container mx-auto px-4 py-16">
        <h3 className="text-3xl font-bold text-center mb-12">
          5 AI Uzman Ajan, Tek Bir Hedef: <span className="text-primary-600">Senin Başarın</span>
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {agents.map((agent) => (
            <div
              key={agent.id}
              className={`card hover:shadow-xl transition-all duration-300 cursor-pointer agent-${agent.id}`}
              onMouseEnter={() => setHoveredAgent(agent.id)}
              onMouseLeave={() => setHoveredAgent(null)}
            >
              <div className="text-center">
                <span className="text-5xl">{agent.icon}</span>
                <h4 className="text-xl font-semibold mt-4 mb-2">{agent.name}</h4>
                <p className="text-gray-600 mb-4">{agent.description}</p>
                {hoveredAgent === agent.id && (
                  <ul className="text-sm text-left space-y-1 animate-fade-in">
                    {agent.features.map((feature, i) => (
                      <li key={i} className="flex items-center space-x-2">
                        <span className="text-primary-500">✓</span>
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </div>
          ))}
        </div>
      </section>
      
      {/* Özellikler */}
      <section className="bg-gray-50 py-16">
        <div className="container mx-auto px-4">
          <h3 className="text-3xl font-bold text-center mb-12">
            Neden YKS Genius?
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🤖</span>
              </div>
              <h4 className="font-semibold mb-2">Çok Ajanlı AI Sistemi</h4>
              <p className="text-gray-600">Her biri kendi alanında uzman 5 AI ajan sürekli senin için çalışıyor</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📈</span>
              </div>
              <h4 className="font-semibold mb-2">Adaptif Öğrenme</h4>
              <p className="text-gray-600">Seviyene ve öğrenme hızına göre kendini ayarlayan akıllı sistem</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">💬</span>
              </div>
              <h4 className="font-semibold mb-2">Gerçek Zamanlı Destek</h4>
              <p className="text-gray-600">Takıldığın her an soru sorabilir, anında yanıt alabilirsin</p>
            </div>
          </div>
        </div>
      </section>
      
      {/* CTA */}
      <section className="container mx-auto px-4 py-16 text-center">
        <div className="bg-primary-600 rounded-2xl p-12 text-white">
          <h3 className="text-3xl font-bold mb-4">
            Hayalindeki Üniversiteye Bir Adım Daha Yaklaş!
          </h3>
          <p className="text-xl mb-8 opacity-90">
            Hemen başla, AI ajanların seninle tanışmayı bekliyor
          </p>
          <button
            onClick={() => navigate('/app')}
            className="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
          >
            Ücretsiz Denemeye Başla
          </button>
        </div>
      </section>
      
      {/* Footer */}
      <footer className="bg-gray-900 text-white py-8 mt-16">
        <div className="container mx-auto px-4 text-center">
          <p className="mb-2">© 2025 YKS Genius - BTK Akademi AI Hackathon Projesi</p>
          <p className="text-gray-400 text-sm">
            2.25 milyon YKS adayının eğitimde fırsat eşitliği için AI destekli çözüm
          </p>
        </div>
      </footer>
    </div>
  )
}