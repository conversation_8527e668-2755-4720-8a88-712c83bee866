import { useState, useRef, useEffect } from 'react'
import { useWebSocket } from '../contexts/WebSocketContext'
import QuestionManager from '../components/common/QuestionManager'
import PlanModeModal from '../components/common/PlanModeModal'
import ReactMarkdown from 'react-markdown'

/**
 * <PERSON>hbet sayfası - Ajanlarla gerçek zamanlı iletişim
 */
export default function ChatPage() {
  const [message, setMessage] = useState('')
  const [selectedAgent, setSelectedAgent] = useState(null)
  const [activeQuestions, setActiveQuestions] = useState(null)
  const [questionSessionActive, setQuestionSessionActive] = useState(false)
  const [studentAnswers, setStudentAnswers] = useState({})
  const [planModeActive, setPlanModeActive] = useState(false)
  const [activePlan, setActivePlan] = useState(null)
  const messagesEndRef = useRef(null)
  const inputRef = useRef(null)

  const {
    messages,
    sendMessage,
    isConnected,
    isConnecting,
    typingAgents,
    error,
    reconnectAttempts,
    maxReconnectAttempts,
    isDemoMode
  } = useWebSocket()

  // Mesaj gönderme
  const handleSendMessage = (e) => {
    e.preventDefault()
    if (message.trim() && isConnected) {
      sendMessage(message, selectedAgent)
      setMessage('')
      inputRef.current?.focus()
    }
  }

  // Otomatik scroll
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages, typingAgents])

  // Ajan bilgileri
  const agentInfo = {
    strategy: { name: 'Strateji Uzmanı', color: 'purple', icon: 'target' },
    content: { name: 'Konu Uzmanı', color: 'blue', icon: 'book' },
    practice: { name: 'Pratik Koçu', color: 'green', icon: 'edit' },
    analytics: { name: 'Analiz Uzmanı', color: 'orange', icon: 'chart' },
    mentor: { name: 'Motivasyon Koçu', color: 'pink', icon: 'heart' }
  }

  // Hızlı aksiyonlar
  const quickActions = [
    'Bana haftalık çalışma planı oluştur',
    'Matematik limit konusunu anlat',
    'Bana 5 fizik sorusu sor',
    'Son performansımı göster',
    'Motivasyona ihtiyacım var'
  ]

  // Handle starting interactive question session
  const handleStartInteractiveSession = (questions, messageData) => {
    setActiveQuestions(questions.map(q => ({
      id: q.id || Math.random().toString(36),
      question: q.question,
      options: q.options,
      correct_answer: q.correct_answer,
      explanation: q.explanation,
      topic: q.topic,
      difficulty: q.difficulty,
      tips: q.tips,
      estimated_time: q.estimated_time || 120
    })))
    setQuestionSessionActive(true)
  }

  // Handle answer submission
  const handleAnswerSubmit = async (answerData) => {
    try {
      setStudentAnswers(prev => ({
        ...prev,
        [answerData.questionId]: answerData
      }))

      // You can add additional processing here
      console.log('Answer submitted:', answerData)
    } catch (error) {
      console.error('Error handling answer:', error)
    }
  }

  // Handle session completion
  const handleSessionComplete = (sessionData) => {
    console.log('Session completed:', sessionData)
    setQuestionSessionActive(false)
    setActiveQuestions(null)

    // Add success message to chat
    const completionMessage = {
      id: Date.now().toString(),
      type: 'system',
      content: `Soru seansı tamamlandı! ${sessionData.stats.correctCount}/${sessionData.stats.answeredCount} doğru cevap. Başarı oranınız: %${Math.round(sessionData.stats.accuracy)}`,
      timestamp: Date.now(),
      agent: 'practice'
    }

    // This would normally be handled by the WebSocket context
    // For now, we'll just log it
    console.log('Session completion message:', completionMessage)
  }

  // Close interactive session
  const handleCloseSession = () => {
    setQuestionSessionActive(false)
    setActiveQuestions(null)
  }

  // Handle quiz question answer submission
  const handleQuizAnswerSubmit = (questionId, questionIndex) => {
    const selectedRadio = document.querySelector(`input[name="question_${questionId || questionIndex}"]:checked`)
    
    if (!selectedRadio) {
      alert('Lütfen önce bir seçenek seçin!')
      return
    }

    const userAnswer = selectedRadio.value
    const questionElement = selectedRadio.closest('.bg-white')
    
    // Show immediate feedback
    const submitButton = questionElement.querySelector('button')
    submitButton.textContent = 'Cevaplandı'
    submitButton.disabled = true
    submitButton.className = 'px-4 py-2 bg-green-600 text-white rounded-lg cursor-not-allowed'
    
    // Highlight selected answer
    const selectedLabel = selectedRadio.closest('label')
    selectedLabel.className += ' bg-blue-100 border border-blue-300'
    
    console.log(`Quiz cevabı gönderildi: Soru ${questionId}, Cevap: ${userAnswer}`)
    
    // Gerektiğinde API çağrısı eklenebilir
    // await apiService.submitAnswer(questionId, { student_answer: userAnswer, ... })
  }

  // Handle single question answer submission
  const handleSingleAnswerSubmit = (questionId, question) => {
    const selectedRadio = document.querySelector(`input[name="single_question_${questionId}"]:checked`)
    
    if (!selectedRadio) {
      alert('Lütfen önce bir seçenek seçin!')
      return
    }

    const userAnswer = selectedRadio.value
    const isCorrect = userAnswer === question.correct_answer
    
    // Show immediate feedback with correct answer
    const questionElement = selectedRadio.closest('.bg-white')
    const submitButton = questionElement.querySelector('button')
    
    if (isCorrect) {
      submitButton.textContent = 'Doğru!'
      submitButton.className = 'px-4 py-2 bg-green-600 text-white rounded-lg cursor-not-allowed'
    } else {
      submitButton.textContent = `Yanlış (Doğru: ${question.correct_answer})`
      submitButton.className = 'px-4 py-2 bg-red-600 text-white rounded-lg cursor-not-allowed'
    }
    
    submitButton.disabled = true
    
    // Highlight selected answer
    const selectedLabel = selectedRadio.closest('label')
    if (isCorrect) {
      selectedLabel.className += ' bg-green-100 border border-green-300'
    } else {
      selectedLabel.className += ' bg-red-100 border border-red-300'
      
      // Also highlight correct answer
      const correctRadio = document.querySelector(`input[name="single_question_${questionId}"][value="${question.correct_answer}"]`)
      if (correctRadio) {
        const correctLabel = correctRadio.closest('label')
        correctLabel.className += ' bg-green-100 border border-green-300'
      }
    }
    
    console.log(`Tek soru cevabı gönderildi: Soru ${questionId}, Cevap: ${userAnswer}, Doğru: ${isCorrect}`)
  }

  // Handle text answer submission
  const handleTextAnswerSubmit = (questionId, isQuiz = false) => {
    const textArea = document.querySelector(`textarea[data-question="${questionId}"]`)
    
    if (!textArea || !textArea.value.trim()) {
      alert('Lütfen önce cevabınızı yazın!')
      return
    }

    const userAnswer = textArea.value.trim()
    const questionElement = textArea.closest('.bg-white')
    const submitButton = questionElement.querySelector('button')
    
    // Show feedback
    submitButton.textContent = 'Gönderildi'
    submitButton.disabled = true
    submitButton.className = 'px-4 py-2 bg-green-600 text-white rounded-lg cursor-not-allowed'
    
    // Make textarea readonly
    textArea.readOnly = true
    textArea.className += ' bg-gray-100'
    
    console.log(`Metin cevabı gönderildi: Soru ${questionId}, Cevap: ${userAnswer}`)
  }

  // Handle plan mode activation
  const handleActivatePlanMode = (planData) => {
    setActivePlan(planData)
    setPlanModeActive(true)
  }

  // Handle plan acceptance
  const handleAcceptPlan = (finalPlan) => {
    console.log('Plan kabul edildi:', finalPlan)
    // TODO: Planı backend/yerel depolamaya kaydet
    setPlanModeActive(false)
    setActivePlan(null)
    
    // Sohbete başarı mesajı ekle
    const successMessage = {
      id: Date.now().toString(),
      type: 'system',
      content: `Çalışma planınız başarıyla kaydedildi! Artık programınızı takip edebilirsiniz.`,
      timestamp: Date.now(),
      agent: 'strategy'
    }
    // Bu normalde WebSocket context tarafından işlenecek
    console.log('Plan kabul mesajı:', successMessage)
  }

  // Handle plan update
  const handleUpdatePlan = (updatedPlan) => {
    console.log('Plan güncellendi:', updatedPlan)
    setActivePlan(updatedPlan)
    // TODO: Güncellenmiş planı backend'e kaydet
  }

  // Close plan mode
  const handleClosePlanMode = () => {
    setPlanModeActive(false)
    setActivePlan(null)
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold">AI Ajanlar ile Sohbet</h1>
            <p className="text-gray-600 text-sm mt-1">
              Sorularını sor, ajanlar sana yardımcı olsun
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {isDemoMode && (
              <span className="text-blue-600 text-sm bg-blue-100 px-2 py-1 rounded">
                Demo Mode
              </span>
            )}
            {error && !isDemoMode && (
              <span className="text-red-600 text-sm">{error}</span>
            )}
            {isConnecting && (
              <span className="text-yellow-600 text-sm animate-pulse">
                Bağlanıyor... ({reconnectAttempts}/{maxReconnectAttempts})
              </span>
            )}
            <div className={`w-3 h-3 rounded-full ${isConnected ? (isDemoMode ? 'bg-blue-500' : 'bg-green-500') : 'bg-red-500'} animate-pulse`}></div>
          </div>
        </div>
      </div>

      {/* Mesajlar */}
      <div className="flex-1 overflow-y-auto px-6 py-4 bg-gray-50">
        {messages.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 mb-6">Henüz mesaj yok. Ajanlarla sohbete başla!</p>
            <div className="flex flex-wrap justify-center gap-2">
              {quickActions.map((action, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setMessage(action)
                    inputRef.current?.focus()
                  }}
                  className="px-3 py-1 bg-white border border-gray-300 rounded-full text-sm hover:bg-gray-50 transition-colors"
                >
                  {action}
                </button>
              ))}
            </div>
          </div>
        ) : (
          <div className="space-y-4 max-w-4xl mx-auto">
            {messages.map((msg) => (
              <div
                key={msg.id}
                className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                {msg.type === 'agent' && (
                  <div className="mr-3 flex-shrink-0">
                    <div className={`w-10 h-10 rounded-full bg-${agentInfo[msg.agent]?.color || 'gray'}-100 flex items-center justify-center`}>
                      <div className="w-5 h-5 bg-gray-600 rounded"></div>
                    </div>
                  </div>
                )}
                <div className={`max-w-2xl ${msg.type === 'user' ? 'order-1' : ''}`}>
                  {msg.type === 'agent' && (
                    <p className="text-xs text-gray-500 mb-1">
                      {agentInfo[msg.agent]?.name || 'Sistem'}
                    </p>
                  )}
                  <div className={`
                    px-4 py-2 rounded-lg
                    ${msg.type === 'user'
                      ? 'bg-primary-600 text-white'
                      : 'bg-white border border-gray-200'
                    }
                  `}>
                    {msg.type === 'agent' ? (
                      <div className="prose prose-sm max-w-none">
                        <ReactMarkdown 
                          components={{
                            // Custom component styling for better chat appearance
                            h1: ({children}) => <h1 className="text-lg font-bold text-gray-800 mb-2">{children}</h1>,
                            h2: ({children}) => <h2 className="text-base font-semibold text-gray-800 mb-2">{children}</h2>,
                            h3: ({children}) => <h3 className="text-sm font-semibold text-gray-800 mb-1">{children}</h3>,
                            p: ({children}) => <p className="text-gray-700 mb-2 last:mb-0">{children}</p>,
                            ul: ({children}) => <ul className="list-disc list-inside text-gray-700 mb-2 space-y-1">{children}</ul>,
                            ol: ({children}) => <ol className="list-decimal list-inside text-gray-700 mb-2 space-y-1">{children}</ol>,
                            li: ({children}) => <li className="text-gray-700">{children}</li>,
                            strong: ({children}) => <strong className="font-semibold text-gray-900">{children}</strong>,
                            em: ({children}) => <em className="italic text-gray-800">{children}</em>,
                            code: ({children}) => <code className="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono text-gray-800">{children}</code>,
                          }}
                        >
                          {msg.content}
                        </ReactMarkdown>
                      </div>
                    ) : (
                      <p className="whitespace-pre-wrap">{msg.content}</p>
                    )}

                    {/* Interactive Questions - When agent provides questions */}
                    {msg.type === 'agent' && msg.data && msg.data.quiz_mode && msg.data.questions && (
                      <div className="mt-4">
                        <div className="bg-blue-50 p-4 rounded-lg mb-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-semibold text-blue-800 mb-1">
                                {msg.data.subject?.charAt(0).toUpperCase() + msg.data.subject?.slice(1)} Soruları
                              </h4>
                              <p className="text-sm text-blue-600">
                                {msg.data.total} soru hazır • İnteraktif çözüm
                              </p>
                            </div>
                            <button
                              onClick={() => handleStartInteractiveSession(msg.data.questions, msg.data)}
                              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
                            >
                              <span>Tam Ekran Çöz</span>
                            </button>
                          </div>
                        </div>

                        {/* Always show interactive questions inline */}
                        <div className="space-y-4">
                          {msg.data.questions.map((question, index) => (
                            <div key={question.id || index} className="bg-white border-2 border-gray-200 rounded-lg p-4">
                              <div className="flex items-center justify-between mb-3">
                                <h5 className="font-medium text-gray-900">
                                  Soru {index + 1}
                                </h5>
                                <div className="flex items-center space-x-2">
                                  {question.difficulty && (
                                    <span className={`text-xs px-2 py-1 rounded ${
                                      question.difficulty === 'kolay' ? 'bg-green-100 text-green-800' :
                                      question.difficulty === 'orta' ? 'bg-yellow-100 text-yellow-800' :
                                      'bg-red-100 text-red-800'
                                    }`}>
                                      {question.difficulty.charAt(0).toUpperCase() + question.difficulty.slice(1)}
                                    </span>
                                  )}
                                  {question.topic && (
                                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                      {question.topic}
                                    </span>
                                  )}
                                </div>
                              </div>
                              
                              <p className="text-gray-800 font-medium mb-4">
                                {question.question}
                              </p>
                              
                              {/* Multiple choice options */}
                              {question.options && (
                                <div className="space-y-2 mb-4">
                                  {Object.entries(question.options).map(([key, value]) => (
                                    <label key={key} className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer">
                                      <input
                                        type="radio"
                                        name={`question_${question.id || index}`}
                                        value={key}
                                        className="w-4 h-4 text-blue-600"
                                        onChange={() => {
                                          // Handle answer selection
                                          console.log(`Soru ${question.id} için ${key} seçildi`)
                                        }}
                                      />
                                      <span className="flex-1">
                                        <span className="font-medium text-gray-700">{key})</span>{' '}
                                        <span className="text-gray-800">{value}</span>
                                      </span>
                                    </label>
                                  ))}
                                </div>
                              )}
                              
                              {/* Text answer input for open-ended questions */}
                              {!question.options && (
                                <div className="mb-4">
                                  <textarea
                                    placeholder="Cevabınızı buraya yazın..."
                                    data-question={question.id || index}
                                    className="w-full p-3 border border-gray-300 rounded-lg resize-none"
                                    rows="3"
                                  />
                                </div>
                              )}
                              
                              {/* Submit button and explanation */}
                              <div className="flex items-center justify-between">
                                <button 
                                  onClick={() => question.options 
                                    ? handleQuizAnswerSubmit(question.id, index)
                                    : handleTextAnswerSubmit(question.id || index, true)
                                  }
                                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                                  Cevapla
                                </button>
                                
                                {question.explanation && (
                                  <details className="max-w-md">
                                    <summary className="text-sm text-blue-600 cursor-pointer hover:text-blue-800">
                                      Açıklamayı göster
                                    </summary>
                                    <p className="text-sm text-gray-600 mt-2 p-3 bg-blue-50 rounded border-l-4 border-blue-400">
                                      <strong>Çözüm:</strong> {question.explanation}
                                      {question.correct_answer && (
                                        <span className="block mt-2 text-green-700">
                                          <strong>Doğru Cevap:</strong> {question.correct_answer}
                                        </span>
                                      )}
                                    </p>
                                  </details>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Single Question Mode - When agent provides a single question */}
                    {msg.type === 'agent' && msg.data && msg.data.single_mode && msg.data.question && (
                      <div className="mt-4">
                        <div className="bg-green-50 p-4 rounded-lg mb-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-semibold text-green-800 mb-1">
                                {msg.data.subject?.charAt(0).toUpperCase() + msg.data.subject?.slice(1)} - {msg.data.topic}
                              </h4>
                              <p className="text-sm text-green-600">
                                Tek soru • Klasik çözüm
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Single question display */}
                        <div className="bg-white border-2 border-green-200 rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <h5 className="font-medium text-gray-900">
                              Soru
                            </h5>
                            <div className="flex items-center space-x-2">
                              {msg.data.question.difficulty && (
                                <span className={`text-xs px-2 py-1 rounded ${
                                  msg.data.question.difficulty === 'kolay' ? 'bg-green-100 text-green-800' :
                                  msg.data.question.difficulty === 'orta' ? 'bg-yellow-100 text-yellow-800' :
                                  'bg-red-100 text-red-800'
                                }`}>
                                  {msg.data.question.difficulty.charAt(0).toUpperCase() + msg.data.question.difficulty.slice(1)}
                                </span>
                              )}
                            </div>
                          </div>
                          
                          <p className="text-gray-800 font-medium mb-4">
                            {msg.data.question.question}
                          </p>
                          
                          {/* Multiple choice options for single question */}
                          {msg.data.question.options && (
                            <div className="space-y-2 mb-4">
                              {Object.entries(msg.data.question.options).map(([key, value]) => (
                                <label key={key} className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded cursor-pointer">
                                  <input
                                    type="radio"
                                    name={`single_question_${msg.data.question.id}`}
                                    value={key}
                                    className="w-4 h-4 text-green-600"
                                    onChange={() => {
                                      console.log(`Tek soru ${msg.data.question.id} için ${key} seçildi`)
                                    }}
                                  />
                                  <span className="flex-1">
                                    <span className="font-medium text-gray-700">{key})</span>{' '}
                                    <span className="text-gray-800">{value}</span>
                                  </span>
                                </label>
                              ))}
                            </div>
                          )}
                          
                          {/* Text answer input for open-ended single questions */}
                          {!msg.data.question.options && (
                            <div className="mb-4">
                              <textarea
                                placeholder="Cevabınızı buraya yazın..."
                                data-question={msg.data.question.id}
                                className="w-full p-3 border border-gray-300 rounded-lg resize-none"
                                rows="3"
                              />
                            </div>
                          )}
                          
                          {/* Submit button and explanation for single question */}
                          <div className="flex items-center justify-between">
                            <button 
                              onClick={() => msg.data.question.options 
                                ? handleSingleAnswerSubmit(msg.data.question.id, msg.data.question)
                                : handleTextAnswerSubmit(msg.data.question.id, false)
                              }
                              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                              Cevapla
                            </button>
                            
                            {msg.data.question.explanation && (
                              <details className="max-w-md">
                                <summary className="text-sm text-green-600 cursor-pointer hover:text-green-800">
                                  Çözümü Göster
                                </summary>
                                <div className="mt-2 p-3 bg-green-50 rounded text-sm text-green-800">
                                  <strong>Açıklama:</strong> {msg.data.question.explanation}
                                  {msg.data.question.correct_answer && (
                                    <div className="mt-2">
                                      <strong>Doğru Cevap:</strong> {msg.data.question.correct_answer}
                                    </div>
                                  )}
                                </div>
                              </details>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Study Plan Mode - When agent provides a study plan */}
                    {msg.type === 'agent' && msg.agent === 'strategy' && msg.data && (
                      msg.data.weekly_schedule || msg.data.monthly_milestones || msg.data.smart_tips
                    ) && (
                      <div className="mt-4">
                        <div className="bg-purple-50 p-4 rounded-lg border border-purple-200">
                          <div className="flex items-center justify-between">
                            <div>
                              <h4 className="font-semibold text-purple-800 mb-1">
                                Çalışma Planınız Hazır!
                              </h4>
                              <p className="text-sm text-purple-600">
                                Planınızı görüntülemek, düzenlemek ve kaydetmek için planlayıcıyı açın
                              </p>
                            </div>
                            <button
                              onClick={() => handleActivatePlanMode(msg.data)}
                              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors flex items-center space-x-2"
                            >
                              <span>Planı Düzenle</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-gray-400 mt-1">
                    {new Date(msg.timestamp).toLocaleTimeString('tr-TR')}
                  </p>
                </div>
              </div>
            ))}

            {/* Yazıyor göstergesi */}
            {typingAgents.length > 0 && (
              <div className="flex items-center space-x-2 text-gray-500">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                </div>
                <span className="text-sm">
                  {typingAgents.map(agent => agentInfo[agent]?.name || agent).join(', ')} yazıyor...
                </span>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        )}
      </div>

      {/* Mesaj gönderme */}
      {!questionSessionActive && (
        <div className="bg-white border-t px-6 py-4">
          <form onSubmit={handleSendMessage} className="max-w-4xl mx-auto">
            <div className="flex space-x-4">
              <input
                ref={inputRef}
                type="text"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder={isConnected ? "Mesajını yaz..." : "Bağlantı bekleniyor..."}
                disabled={!isConnected}
                className="flex-1 input"
              />
              <button
                type="submit"
                disabled={!isConnected || !message.trim()}
                className="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Gönder
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Interactive Question Session Overlay */}
      {questionSessionActive && activeQuestions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg w-full max-w-6xl max-h-full overflow-hidden flex flex-col">
            {/* Session Header */}
            <div className="bg-gradient-to-r from-blue-600 to-green-600 text-white p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-semibold">İnteraktif Soru Çözme Seansı</h2>
                  <p className="text-blue-100 text-sm">
                    {activeQuestions.length} soru • Her soruya dokunun ve cevapları kaydedin
                  </p>
                </div>
                <button
                  onClick={handleCloseSession}
                  className="text-white hover:text-gray-200 p-2 rounded-lg hover:bg-black hover:bg-opacity-20 transition-colors"
                  title="Seansı Kapat"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Question Manager */}
            <div className="flex-1 overflow-auto p-6 bg-gray-50">
              <QuestionManager
                questions={activeQuestions}
                onAnswerSubmit={handleAnswerSubmit}
                onAllQuestionsCompleted={handleSessionComplete}
                allowReview={true}
                autoAdvance={false}
                studentId="demo-student" // In real app, get from auth context
              />
            </div>

            {/* Session Footer */}
            <div className="bg-white border-t p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <span className="w-3 h-3 bg-green-500 rounded-full"></span>
                    <span>Cevaplandı</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="w-3 h-3 bg-blue-500 rounded-full"></span>
                    <span>Aktif</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <span className="w-3 h-3 bg-gray-300 rounded-full"></span>
                    <span>Bekliyor</span>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <div className="text-sm text-gray-600">
                    İpucu: Her soruyu dikkatlice okuyun ve zaman limitine dikkat edin
                  </div>
                  <button
                    onClick={handleCloseSession}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Seansı Sonlandır
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Plan Mode Modal */}
      <PlanModeModal
        isOpen={planModeActive}
        onClose={handleClosePlanMode}
        studyPlan={activePlan}
        onAcceptPlan={handleAcceptPlan}
        onUpdatePlan={handleUpdatePlan}
      />
    </div>
  )
}
