import { useState, useEffect, useCallback, useRef } from 'react'

/**
 * YKS Genius WebSocket Hook
 * Gerçek zamanlı ajan iletişimi için özel hook
 */
export function useWebSocket(studentId = null) {
  // Generate unique student ID if not provided
  const actualStudentId = studentId || `student-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  // Durum yönetimi
  const [isConnected, setIsConnected] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)
  const [messages, setMessages] = useState([])
  const [error, setError] = useState(null)
  const [typingAgents, setTypingAgents] = useState(new Set())
  
  // WebSocket referansı
  const ws = useRef(null)
  const reconnectTimeoutRef = useRef(null)
  const reconnectAttemptsRef = useRef(0)
  const maxReconnectAttempts = 5
  const reconnectDelay = 3000 // 3 saniye
  
  // WebSocket URL'i oluştur
  const wsUrl = `${import.meta.env.VITE_WS_URL || 'ws://localhost:8000'}/ws/${actualStudentId}`
  
  // Streaming mesajları için geçici depolama
  const streamingMessageRef = useRef(null)
  
  // Streaming başlangıcını işle
  const handleStreamStart = useCallback((data) => {
    const { agent, stream_id } = data
    
    // Yeni streaming mesajı başlat
    streamingMessageRef.current = {
      id: Date.now(),
      stream_id,
      type: 'agent',
      agent,
      content: '',
      timestamp: new Date().toISOString(),
      isStreaming: true
    }
    
    setMessages(prev => [...prev, streamingMessageRef.current])
  }, [])
  
  // Streaming mesaj parçasını işle
  const handleStreamChunk = useCallback((data) => {
    const { chunk, stream_id } = data
    
    if (streamingMessageRef.current && streamingMessageRef.current.stream_id === stream_id) {
      // Mevcut mesaja ekle
      streamingMessageRef.current.content += chunk
      
      setMessages(prev => prev.map(msg => 
        msg.id === streamingMessageRef.current?.id 
          ? { ...msg, content: streamingMessageRef.current.content }
          : msg
      ))
    }
  }, [])
  
  // Streaming bitişini işle
  const handleStreamEnd = useCallback((data) => {
    const { stream_id } = data
    
    if (streamingMessageRef.current && streamingMessageRef.current.stream_id === stream_id) {
      // Streaming bayrağını kaldır
      setMessages(prev => prev.map(msg => 
        msg.id === streamingMessageRef.current?.id 
          ? { ...msg, isStreaming: false }
          : msg
      ))
      
      streamingMessageRef.current = null
    }
  }, [])
  
  // Mesaj gönderme fonksiyonu
  const sendMessage = useCallback((message, requestType = null) => {
    if (!ws.current || ws.current.readyState !== WebSocket.OPEN) {
      console.error('WebSocket bağlantısı açık değil')
      return false
    }
    
    try {
      const data = {
        type: 'chat',
        content: message,
        request_type: requestType,
        timestamp: new Date().toISOString()
      }
      
      ws.current.send(JSON.stringify(data))
      
      // Kullanıcı mesajını listeye ekle
      setMessages(prev => [...prev, {
        id: Date.now(),
        type: 'user',
        content: message,
        timestamp: new Date().toISOString()
      }])
      
      return true
    } catch (error) {
      console.error('Mesaj gönderilemedi:', error)
      setError('Mesaj gönderilemedi')
      return false
    }
  }, [])
  
  // WebSocket bağlantısını başlat
  const connect = useCallback(() => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      console.log('WebSocket zaten bağlı')
      return
    }
    
    setIsConnecting(true)
    setError(null)
    
    try {
      ws.current = new WebSocket(wsUrl)
      
      // Bağlantı açıldığında
      ws.current.onopen = () => {
        console.log('WebSocket bağlantısı kuruldu')
        setIsConnected(true)
        setIsConnecting(false)
        setError(null)
        reconnectAttemptsRef.current = 0
      }
      
      // Mesaj alındığında
      ws.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          
          switch (data.type) {
            case 'agent_response':
              setMessages(prev => [...prev, {
                id: Date.now(),
                type: 'agent',
                agent: data.agent,
                content: data.content,
                timestamp: data.timestamp || new Date().toISOString(),
                metadata: data.metadata
              }])
              break
              
            case 'typing':
              // Handle typing indicator
              if (data.is_typing) {
                setTypingAgents(prev => new Set([...prev, data.agent || 'YKS Genius']))
              } else {
                setTypingAgents(prev => {
                  const newSet = new Set(prev)
                  newSet.delete(data.agent || 'YKS Genius')
                  return newSet
                })
              }
              break
              
            case 'typing_start':
              setTypingAgents(prev => new Set([...prev, data.agent]))
              break
              
            case 'typing_stop':
              setTypingAgents(prev => {
                const newSet = new Set(prev)
                newSet.delete(data.agent)
                return newSet
              })
              break
              
            case 'error':
              console.error('Sunucu hatası:', data.message)
              setError(data.message)
              break
              
            case 'connection_established':
              console.log('Bağlantı onaylandı:', data.message)
              break
              
            case 'welcome':
              console.log('Hoşgeldin mesajı:', data.message)
              setMessages(prev => [...prev, {
                id: Date.now(),
                type: 'system',
                content: data.message || 'YKS Genius\'a hoş geldiniz!',
                timestamp: new Date().toISOString()
              }])
              break
              
            case 'stream_start':
              // Streaming başlangıcı
              handleStreamStart(data)
              break
              
            case 'stream_chunk':
              // Streaming mesaj parçası - mevcut mesajı güncelle
              handleStreamChunk(data)
              break
              
            case 'stream_end':
              // Streaming sonu
              handleStreamEnd(data)
              break
              
            default:
              console.log('Bilinmeyen mesaj tipi:', data.type)
          }
        } catch (error) {
          console.error('Mesaj parse hatası:', error)
        }
      }
      
      // Hata durumunda
      ws.current.onerror = (error) => {
        console.error('WebSocket hatası:', error)
        setError('Bağlantı hatası oluştu')
      }
      
      // Bağlantı kapandığında
      ws.current.onclose = (event) => {
        console.log('WebSocket bağlantısı kapandı:', event.code, event.reason)
        setIsConnected(false)
        setIsConnecting(false)
        
        // Otomatik yeniden bağlanma
        if (reconnectAttemptsRef.current < maxReconnectAttempts && !event.wasClean) {
          reconnectAttemptsRef.current++
          console.log(`Yeniden bağlanma denemesi ${reconnectAttemptsRef.current}/${maxReconnectAttempts}`)
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect()
          }, reconnectDelay * reconnectAttemptsRef.current)
        } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
          setError('Bağlantı kurulamadı. Lütfen sayfayı yenileyin.')
        }
      }
      
    } catch (error) {
      console.error('WebSocket bağlantı hatası:', error)
      setIsConnecting(false)
      setError('Bağlantı kurulamadı')
    }
  }, [wsUrl, handleStreamStart, handleStreamChunk, handleStreamEnd])
  
  // Bağlantıyı kapat
  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }
    
    if (ws.current) {
      ws.current.close(1000, 'Kullanıcı bağlantıyı kapattı')
      ws.current = null
    }
    
    setIsConnected(false)
    setIsConnecting(false)
    setTypingAgents(new Set())
  }, [])
  
  // Mesaj geçmişini temizle
  const clearMessages = useCallback(() => {
    setMessages([])
  }, [])
  
  // Component mount olduğunda bağlan
  useEffect(() => {
    connect()
    
    // Cleanup
    return () => {
      disconnect()
    }
  }, [connect, disconnect])
  
  // Sayfa görünürlüğü değiştiğinde yeniden bağlan
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && !isConnected && !isConnecting) {
        console.log('Sayfa görünür oldu, yeniden bağlanıyor...')
        connect()
      }
    }
    
    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [isConnected, isConnecting, connect])
  
  return {
    // Durum
    isConnected,
    isConnecting,
    messages,
    error,
    typingAgents: Array.from(typingAgents),
    
    // Aksiyonlar
    sendMessage,
    connect,
    disconnect,
    clearMessages,
    
    // Yardımcı bilgiler
    reconnectAttempts: reconnectAttemptsRef.current,
    maxReconnectAttempts
  }
}