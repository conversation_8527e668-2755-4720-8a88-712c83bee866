import { Component } from 'react'

/**
 * Hata sınırı bileşeni
 * React uygulamasında oluşan hataları yakalar ve kullanıcı dostu mesaj gösterir
 */
class ErrorBoundary extends Component {
  constructor(props) {
    super(props)
    this.state = { 
      hasError: false,
      error: null,
      errorInfo: null
    }
  }

  static getDerivedStateFromError(error) {
    // Hata durumunu güncelle
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    // Hata detaylarını kaydet
    console.error('Hata yakalandı:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo
    })
    
    // Hata takip servisi (Sentry vb.) varsa buraya eklenebilir
    if (import.meta.env.VITE_SENTRY_DSN) {
      // window.Sentry?.captureException(error)
    }
  }

  handleReset = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    })
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
          <div className="max-w-md w-full">
            <div className="card text-center">
              <div className="text-6xl mb-4">😔</div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                Bir şeyler yanlış gitti
              </h1>
              <p className="text-gray-600 mb-6">
                Beklenmeyen bir hata oluştu. Lütfen sayfayı yenileyerek tekrar deneyin.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <button
                  onClick={() => window.location.reload()}
                  className="btn btn-primary"
                >
                  Sayfayı Yenile
                </button>
                <button
                  onClick={() => window.location.href = '/'}
                  className="btn btn-outline"
                >
                  Ana Sayfaya Dön
                </button>
              </div>
              
              {/* Debug modunda hata detayları */}
              {import.meta.env.VITE_DEBUG === 'true' && this.state.error && (
                <details className="mt-6 text-left">
                  <summary className="cursor-pointer text-sm text-gray-500 hover:text-gray-700">
                    Hata detayları (Geliştirici için)
                  </summary>
                  <div className="mt-2 p-4 bg-red-50 rounded-lg">
                    <p className="font-mono text-xs text-red-800 mb-2">
                      {this.state.error.toString()}
                    </p>
                    {this.state.errorInfo && (
                      <pre className="font-mono text-xs text-red-700 overflow-auto">
                        {this.state.errorInfo.componentStack}
                      </pre>
                    )}
                  </div>
                </details>
              )}
            </div>
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary