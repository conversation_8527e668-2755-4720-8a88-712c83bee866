import { useState, useEffect } from 'react'
import InteractiveQuestion from './InteractiveQuestion'
import { apiService } from '../../services/api'

/**
 * Question Manager Component
 * Manages multiple questions, handles answer submission, and tracks statistics
 */
export default function QuestionManager({
  questions = [],
  onAllQuestionsCompleted,
  onAnswerSubmit,
  allowReview = true,
  autoAdvance = false,
  studentId
}) {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const [answers, setAnswers] = useState({})
  const [results, setResults] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [sessionStats, setSessionStats] = useState({
    totalQuestions: questions.length,
    answeredCount: 0,
    correctCount: 0,
    totalTime: 0,
    startTime: Date.now()
  })

  useEffect(() => {
    setSessionStats(prev => ({
      ...prev,
      totalQuestions: questions.length
    }))
  }, [questions.length])

  const currentQuestion = questions[currentQuestionIndex]
  const isLastQuestion = currentQuestionIndex === questions.length - 1
  const hasAnswered = currentQuestion?.id && answers[currentQuestion.id]
  const showResult = hasAnswered && results[currentQuestion.id]

  const handleAnswerSubmit = async (answerData) => {
    if (!currentQuestion) return

    setIsSubmitting(true)

    try {
      // Save answer locally
      setAnswers(prev => ({
        ...prev,
        [currentQuestion.id]: answerData
      }))

      // Submit to backend for evaluation using API service
      const result = await apiService.submitAnswer(currentQuestion.id, {
        student_answer: answerData.answer,
        time_spent: answerData.timeSpent,
        student_id: studentId,
        timestamp: answerData.timestamp
      })

      if (result) {

        // Save result
        setResults(prev => ({
          ...prev,
          [currentQuestion.id]: result
        }))

        // Update session stats
        setSessionStats(prev => ({
          ...prev,
          answeredCount: prev.answeredCount + 1,
          correctCount: prev.correctCount + (result.is_correct ? 1 : 0),
          totalTime: prev.totalTime + answerData.timeSpent
        }))

        // Call parent callback
        if (onAnswerSubmit) {
          onAnswerSubmit({
            questionId: currentQuestion.id,
            answer: answerData.answer,
            result: result,
            timeSpent: answerData.timeSpent
          })
        }

        // Auto advance if enabled
        if (autoAdvance && !isLastQuestion) {
          setTimeout(() => {
            setCurrentQuestionIndex(prev => prev + 1)
          }, 2000)
        }

      } else {
        throw new Error('Cevap değerlendirilemedi')
      }
    } catch (error) {
      console.error('Error submitting answer:', error)
      // Show error to user
      alert('Cevap gönderilirken hata oluştu. Lütfen tekrar deneyin.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const goToNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1)
    }
  }

  const goToPreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1)
    }
  }

  const goToQuestion = (index) => {
    if (index >= 0 && index < questions.length) {
      setCurrentQuestionIndex(index)
    }
  }

  const finishSession = () => {
    const finalStats = {
      ...sessionStats,
      accuracy: sessionStats.answeredCount > 0 ? (sessionStats.correctCount / sessionStats.answeredCount) * 100 : 0,
      averageTime: sessionStats.answeredCount > 0 ? sessionStats.totalTime / sessionStats.answeredCount : 0,
      sessionDuration: Date.now() - sessionStats.startTime,
      completionRate: (sessionStats.answeredCount / sessionStats.totalQuestions) * 100
    }

    if (onAllQuestionsCompleted) {
      onAllQuestionsCompleted({
        answers,
        results,
        stats: finalStats
      })
    }
  }

  if (!questions.length) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Henüz soru yüklenmedi.</p>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Progress Header */}
      <div className="mb-6 bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-gray-900">
            Soru Çözme Seansı
          </h3>
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <span>⏱️ {Math.floor((Date.now() - sessionStats.startTime) / 60000)}dk</span>
            <span>✅ {sessionStats.correctCount}/{sessionStats.answeredCount}</span>
            <span>📊 {sessionStats.answeredCount > 0 ? Math.round((sessionStats.correctCount / sessionStats.answeredCount) * 100) : 0}%</span>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2 mb-3">
          <div
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(sessionStats.answeredCount / sessionStats.totalQuestions) * 100}%` }}
          ></div>
        </div>

        {/* Question Navigator */}
        <div className="flex items-center justify-between">
          <div className="flex flex-wrap gap-1">
            {questions.map((_, index) => (
              <button
                key={index}
                onClick={() => goToQuestion(index)}
                className={`w-8 h-8 rounded text-xs font-medium transition-colors ${
                  index === currentQuestionIndex
                    ? 'bg-blue-600 text-white'
                    : answers[questions[index]?.id]
                    ? results[questions[index]?.id]?.is_correct
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {index + 1}
              </button>
            ))}
          </div>

          <div className="text-sm text-gray-600">
            {currentQuestionIndex + 1} / {questions.length}
          </div>
        </div>
      </div>

      {/* Current Question */}
      {currentQuestion && (
        <div className="mb-6">
          <InteractiveQuestion
            question={currentQuestion}
            onAnswerSubmit={handleAnswerSubmit}
            isSubmitting={isSubmitting}
            showResult={showResult}
            result={results[currentQuestion.id]}
            questionIndex={currentQuestionIndex}
            totalQuestions={questions.length}
          />
        </div>
      )}

      {/* Navigation Controls */}
      <div className="flex items-center justify-between bg-white rounded-lg border border-gray-200 p-4">
        <button
          onClick={goToPreviousQuestion}
          disabled={currentQuestionIndex === 0}
          className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span>←</span>
          <span>Önceki</span>
        </button>

        <div className="flex items-center space-x-3">
          {hasAnswered && !isLastQuestion && (
            <button
              onClick={goToNextQuestion}
              className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <span>Sonraki</span>
              <span>→</span>
            </button>
          )}

          {hasAnswered && isLastQuestion && (
            <button
              onClick={finishSession}
              className="flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              <span>🏁</span>
              <span>Seansı Bitir</span>
            </button>
          )}

          {!hasAnswered && (
            <div className="text-sm text-gray-500">
              Devam etmek için soruyu yanıtlayın
            </div>
          )}
        </div>

        <button
          onClick={goToNextQuestion}
          disabled={isLastQuestion || (!allowReview && !hasAnswered)}
          className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span>Sonraki</span>
          <span>→</span>
        </button>
      </div>

      {/* Session Summary (shown when all questions answered) */}
      {sessionStats.answeredCount === questions.length && (
        <div className="mt-6 bg-green-50 border border-green-200 rounded-lg p-6">
          <div className="text-center">
            <h3 className="text-xl font-semibold text-green-800 mb-4">
              🎉 Tebrikler! Tüm soruları tamamladınız!
            </h3>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-white rounded-lg p-3">
                <div className="text-2xl font-bold text-blue-600">
                  {sessionStats.answeredCount}
                </div>
                <div className="text-sm text-gray-600">Toplam Soru</div>
              </div>

              <div className="bg-white rounded-lg p-3">
                <div className="text-2xl font-bold text-green-600">
                  {sessionStats.correctCount}
                </div>
                <div className="text-sm text-gray-600">Doğru Cevap</div>
              </div>

              <div className="bg-white rounded-lg p-3">
                <div className="text-2xl font-bold text-purple-600">
                  {Math.round((sessionStats.correctCount / sessionStats.answeredCount) * 100)}%
                </div>
                <div className="text-sm text-gray-600">Başarı Oranı</div>
              </div>

              <div className="bg-white rounded-lg p-3">
                <div className="text-2xl font-bold text-orange-600">
                  {Math.floor(sessionStats.totalTime / 60)}dk
                </div>
                <div className="text-sm text-gray-600">Toplam Süre</div>
              </div>
            </div>

            <button
              onClick={finishSession}
              className="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
            >
              Seansı Sonlandır ve Kaydet
            </button>
          </div>
        </div>
      )}
    </div>
  )
}
