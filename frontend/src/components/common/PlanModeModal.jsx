import { useState, useEffect } from 'react'

/**
 * Interactive Plan Mode Modal - Shows calendar interface for study plan editing
 */
export default function PlanModeModal({ 
  isOpen, 
  onClose, 
  studyPlan, 
  onAcceptPlan, 
  onUpdatePlan 
}) {
  const [planData, setPlanData] = useState(studyPlan)
  const [hasChanges, setHasChanges] = useState(false)
  const [isEditing, setIsEditing] = useState(false)

  const days = ['Pazartesi', 'Salı', 'Çarşamba', 'Perşembe', 'Cuma', 'Cumartesi', 'Pazar']
  const timeSlots = ['08:00', '10:00', '14:00', '16:00', '19:00']
  
  const subjects = [
    { name: '<PERSON><PERSON><PERSON><PERSON>', color: 'bg-blue-100 text-blue-700', border: 'border-blue-300' },
    { name: '<PERSON>zik', color: 'bg-green-100 text-green-700', border: 'border-green-300' },
    { name: '<PERSON><PERSON>', color: 'bg-purple-100 text-purple-700', border: 'border-purple-300' },
    { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', color: 'bg-pink-100 text-pink-700', border: 'border-pink-300' },
    { name: 'T<PERSON>rkçe', color: 'bg-orange-100 text-orange-700', border: 'border-orange-300' },
    { name: 'Tarih', color: 'bg-yellow-100 text-yellow-700', border: 'border-yellow-300' },
    { name: 'Coğrafya', color: 'bg-indigo-100 text-indigo-700', border: 'border-indigo-300' },
  ]

  useEffect(() => {
    if (studyPlan) {
      setPlanData(studyPlan)
      setHasChanges(false)
    }
  }, [studyPlan])

  // Parse weekly schedule from plan data
  const parseWeeklySchedule = () => {
    if (!planData?.weekly_schedule) return {}
    
    // Convert the schedule data to calendar format
    const schedule = {}
    Object.entries(planData.weekly_schedule).forEach(([day, daySubjects]) => {
      schedule[day] = Array.isArray(daySubjects) ? daySubjects : [daySubjects]
    })
    return schedule
  }

  const [weeklySchedule, setWeeklySchedule] = useState(parseWeeklySchedule())

  const handleCellClick = (day, timeSlot) => {
    if (!isEditing) return

    const key = `${day}-${timeSlot}`
    const currentSubject = weeklySchedule[key]
    
    // Cycle through subjects or remove
    const currentIndex = subjects.findIndex(s => s.name === currentSubject)
    const nextIndex = currentIndex >= 0 ? (currentIndex + 1) % (subjects.length + 1) : 0
    
    const newSchedule = { ...weeklySchedule }
    if (nextIndex === subjects.length) {
      // Remove subject
      delete newSchedule[key]
    } else {
      // Set new subject
      newSchedule[key] = subjects[nextIndex].name
    }
    
    setWeeklySchedule(newSchedule)
    setHasChanges(true)
  }

  const handleAcceptPlan = () => {
    const updatedPlan = {
      ...planData,
      weekly_schedule: weeklySchedule,
      accepted_at: new Date().toISOString()
    }
    onAcceptPlan(updatedPlan)
    onClose()
  }

  const handleUpdatePlan = () => {
    const updatedPlan = {
      ...planData,
      weekly_schedule: weeklySchedule,
      updated_at: new Date().toISOString()
    }
    onUpdatePlan(updatedPlan)
    setHasChanges(false)
  }

  const getCellContent = (day, timeSlot) => {
    const key = `${day}-${timeSlot}`
    const subject = weeklySchedule[key]
    if (!subject) return null

    const subjectData = subjects.find(s => s.name === subject)
    return subjectData
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg w-full max-w-7xl max-h-full overflow-hidden flex flex-col">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-green-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Çalışma Planı Düzenleyici</h2>
              <p className="text-blue-100 text-sm mt-1">
                Planınızı gözden geçirin, düzenleyin ve kaydedin
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 p-2 rounded-lg hover:bg-black hover:bg-opacity-20"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-6">
          {/* Plan Summary */}
          <div className="bg-blue-50 p-4 rounded-lg mb-6">
            <h3 className="font-semibold text-blue-800 mb-2">Plan Özeti</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              {planData?.monthly_milestones && (
                <div>
                  <span className="font-medium">Aylık Hedefler:</span>
                  <p className="text-blue-700">{planData.monthly_milestones.length} hedef</p>
                </div>
              )}
              {planData?.success_prediction && (
                <div>
                  <span className="font-medium">Başarı Tahmini:</span>
                  <p className="text-blue-700">{planData.success_prediction}</p>
                </div>
              )}
              <div>
                <span className="font-medium">Durum:</span>
                <p className="text-blue-700">{hasChanges ? 'Değişiklikler var' : 'Kaydedildi'}</p>
              </div>
            </div>
          </div>

          {/* Calendar Interface */}
          <div className="bg-white border rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Haftalık Program</h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className={`px-3 py-1 rounded-lg transition-colors ${
                    isEditing 
                      ? 'bg-orange-100 text-orange-700 border border-orange-300' 
                      : 'bg-gray-100 text-gray-700 border border-gray-300'
                  }`}
                >
                  {isEditing ? 'Düzenleme Modundasın' : 'Düzenle'}
                </button>
              </div>
            </div>

            {/* Subject Legend */}
            <div className="flex flex-wrap gap-2 mb-4 p-3 bg-gray-50 rounded-lg">
              <span className="text-sm font-medium text-gray-700">Dersler:</span>
              {subjects.map((subject, index) => (
                <span
                  key={index}
                  className={`text-xs px-2 py-1 rounded ${subject.color} ${subject.border} border`}
                >
                  {subject.name}
                </span>
              ))}
              {isEditing && (
                <span className="text-xs text-gray-500 italic">
                  İpucu: Hücrelere tıklayarak ders ekleyin/çıkarın
                </span>
              )}
            </div>

            {/* Calendar Grid */}
            <div className="overflow-x-auto">
              <div className="grid grid-cols-8 gap-2 min-w-[900px]">
                {/* Header Row */}
                <div className="text-center font-medium text-gray-500 text-sm py-2">Saat</div>
                {days.map(day => (
                  <div key={day} className="text-center font-medium text-gray-700 py-2 text-sm">
                    {day}
                  </div>
                ))}
                
                {/* Time Slots */}
                {timeSlots.map(timeSlot => (
                  <>
                    <div key={timeSlot} className="text-sm text-gray-500 text-right pr-2 py-4">
                      {timeSlot}
                    </div>
                    {days.map(day => {
                      const cellContent = getCellContent(day, timeSlot)
                      return (
                        <div
                          key={`${timeSlot}-${day}`}
                          className={`h-20 rounded-lg p-2 border-2 transition-all cursor-pointer ${
                            cellContent 
                              ? `${cellContent.color} ${cellContent.border}` 
                              : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                          } ${isEditing ? 'hover:shadow-md' : ''}`}
                          onClick={() => handleCellClick(day, timeSlot)}
                        >
                          {cellContent && (
                            <div className="text-xs font-medium h-full flex items-center justify-center text-center">
                              {cellContent.name}
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="bg-gray-50 px-6 py-4 border-t">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {hasChanges && (
                <span className="text-orange-600 font-medium">
                  Uyarı: Kaydedilmemiş değişiklikler var
                </span>
              )}
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                İptal
              </button>
              
              {hasChanges && (
                <button
                  onClick={handleUpdatePlan}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  Değişiklikleri Kaydet
                </button>
              )}
              
              <button
                onClick={handleAcceptPlan}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 font-medium"
              >
                Planı Kabul Et
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}