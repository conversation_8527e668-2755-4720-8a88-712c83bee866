import { useState, useEffect } from 'react'

/**
 * Interactive Question Component
 * Handles both multiple choice and text-based questions with answer submission
 */
export default function InteractiveQuestion({
  question,
  onAnswerSubmit,
  isSubmitting = false,
  showResult = false,
  result = null,
  questionIndex = 0,
  totalQuestions = 1
}) {
  const [selectedAnswer, setSelectedAnswer] = useState('')
  const [textAnswer, setTextAnswer] = useState('')
  const [timeSpent, setTimeSpent] = useState(0)
  const [startTime, setStartTime] = useState(Date.now())

  // Timer for tracking time spent
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeSpent(Math.floor((Date.now() - startTime) / 1000))
    }, 1000)

    return () => clearInterval(timer)
  }, [startTime])

  // Reset state when question changes
  useEffect(() => {
    setSelectedAnswer('')
    setTextAnswer('')
    setTimeSpent(0)
    setStartTime(Date.now())
  }, [question.id])

  const handleSubmit = () => {
    const answer = question.options ? selectedAnswer : textAnswer.trim()

    if (!answer) {
      return // Don't submit empty answers
    }

    onAnswerSubmit({
      questionId: question.id,
      answer: answer,
      timeSpent: timeSpent,
      timestamp: Date.now()
    })
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getAnswerColor = (optionKey) => {
    if (!showResult || !result) return ''

    if (optionKey === result.correct_answer) {
      return 'bg-green-100 border-green-500 text-green-800'
    } else if (optionKey === selectedAnswer && !result.is_correct) {
      return 'bg-red-100 border-red-500 text-red-800'
    }
    return ''
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
      {/* Question Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <span className="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">
            Soru {questionIndex + 1}/{totalQuestions}
          </span>
          {question.difficulty && (
            <span className={`text-xs font-medium px-2 py-1 rounded ${
              question.difficulty === 'kolay' ? 'bg-green-100 text-green-800' :
              question.difficulty === 'orta' ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            }`}>
              {question.difficulty.charAt(0).toUpperCase() + question.difficulty.slice(1)}
            </span>
          )}
          {question.topic && (
            <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
              📚 {question.topic}
            </span>
          )}
        </div>

        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <span>⏱️ {formatTime(timeSpent)}</span>
          {question.estimated_time && (
            <span className="text-xs text-gray-400">
              (Tahmini: {Math.floor(question.estimated_time / 60)}dk)
            </span>
          )}
        </div>
      </div>

      {/* Question Content */}
      <div className="mb-6">
        <p className="text-gray-900 font-medium text-lg leading-relaxed">
          {question.question || question.content}
        </p>
      </div>

      {/* Answer Input Section */}
      <div className="mb-6">
        {question.options ? (
          // Multiple Choice Question
          <div className="space-y-3">
            <p className="text-sm font-medium text-gray-700 mb-3">Cevabınızı seçin:</p>
            {Object.entries(question.options).map(([key, value]) => (
              <label
                key={key}
                className={`flex items-start space-x-3 p-3 border rounded-lg cursor-pointer transition-all hover:bg-gray-50 ${
                  selectedAnswer === key ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                } ${getAnswerColor(key)} ${showResult ? 'cursor-default' : ''}`}
              >
                <input
                  type="radio"
                  name={`question-${question.id}`}
                  value={key}
                  checked={selectedAnswer === key}
                  onChange={(e) => setSelectedAnswer(e.target.value)}
                  disabled={showResult || isSubmitting}
                  className="mt-1 w-4 h-4 text-blue-600 focus:ring-blue-500"
                />
                <div className="flex-1">
                  <span className="inline-flex items-center justify-center w-6 h-6 bg-gray-200 rounded-full text-sm font-medium mr-2">
                    {key}
                  </span>
                  <span className="text-gray-700">{value}</span>
                </div>
                {showResult && key === result?.correct_answer && (
                  <span className="text-green-600">✅</span>
                )}
                {showResult && key === selectedAnswer && key !== result?.correct_answer && (
                  <span className="text-red-600">❌</span>
                )}
              </label>
            ))}
          </div>
        ) : (
          // Text-based Question
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700">
              Cevabınızı yazın:
            </label>
            <textarea
              value={textAnswer}
              onChange={(e) => setTextAnswer(e.target.value)}
              disabled={showResult || isSubmitting}
              placeholder="Cevabınızı buraya yazınız..."
              rows={4}
              className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none ${
                showResult ? 'bg-gray-50' : ''
              }`}
            />
            <p className="text-xs text-gray-500">
              {textAnswer.length}/500 karakter
            </p>
          </div>
        )}
      </div>

      {/* Action Buttons */}
      {!showResult && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            {question.options ?
              (selectedAnswer ? `Seçiminiz: ${selectedAnswer}` : 'Bir seçenek belirleyin') :
              (textAnswer.trim() ? `${textAnswer.length} karakter` : 'Cevabınızı yazın')
            }
          </div>
          <button
            onClick={handleSubmit}
            disabled={
              isSubmitting ||
              (question.options ? !selectedAnswer : !textAnswer.trim())
            }
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSubmitting ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Gönderiliyor...</span>
              </div>
            ) : (
              'Cevabı Gönder'
            )}
          </button>
        </div>
      )}

      {/* Result Display */}
      {showResult && result && (
        <div className={`mt-6 p-4 rounded-lg border-l-4 ${
          result.is_correct ? 'bg-green-50 border-green-500' : 'bg-red-50 border-red-500'
        }`}>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-2">
                <span className={`text-lg ${result.is_correct ? 'text-green-600' : 'text-red-600'}`}>
                  {result.is_correct ? '✅ Doğru!' : '❌ Yanlış'}
                </span>
                <span className="text-sm text-gray-600">
                  ({formatTime(timeSpent)} sürede)
                </span>
              </div>

              {result.explanation && (
                <div className="mb-3">
                  <p className="text-sm font-medium text-gray-700 mb-1">Açıklama:</p>
                  <p className="text-sm text-gray-600">{result.explanation}</p>
                </div>
              )}

              {result.feedback && (
                <p className="text-sm text-gray-600 italic">{result.feedback}</p>
              )}
            </div>

            <div className="text-right">
              <div className={`text-2xl font-bold ${result.is_correct ? 'text-green-600' : 'text-red-600'}`}>
                {result.score || 0}
              </div>
              <div className="text-xs text-gray-500">puan</div>
            </div>
          </div>

          {/* Performance Feedback */}
          {result.time_performance && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Süre Performansı:</span>
                <span className={`font-medium ${
                  result.time_performance.status === 'ideal' ? 'text-green-600' :
                  result.time_performance.status === 'hızlı' ? 'text-blue-600' : 'text-orange-600'
                }`}>
                  {result.time_performance.status === 'ideal' ? '🎯 İdeal' :
                   result.time_performance.status === 'hızlı' ? '⚡ Hızlı' : '🐌 Yavaş'}
                  {result.time_performance.efficiency &&
                    ` (${Math.round(result.time_performance.efficiency)}% verimli)`
                  }
                </span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Question Tips */}
      {question.tips && (
        <details className="mt-4">
          <summary className="text-sm text-blue-600 cursor-pointer hover:text-blue-800 font-medium">
            💡 İpucu ve Çözüm Teknikleri
          </summary>
          <div className="mt-2 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">{question.tips}</p>
          </div>
        </details>
      )}
    </div>
  )
}
