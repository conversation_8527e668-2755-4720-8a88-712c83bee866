#!/bin/sh
# YKS Genius için Redis Entrypoint Script'i

set -e

# Mesajları loglamak için fonksiyon
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1"
}

# Redis şifresi ayarlı mı kontrol et
if [ -z "$REDIS_PASSWORD" ]; then
    export REDIS_PASSWORD="yks_redis_password"
    log "Varsayılan Redis şifresi kullanılıyor"
else
    log "Özel Redis şifresi kullanılıyor"
fi

# redis.conf'u ortam değişkenleriyle güncelle
if [ -n "$REDIS_PASSWORD" ]; then
    sed -i "s/requirepass yks_redis_password/requirepass $REDIS_PASSWORD/" /etc/redis/redis.conf
fi

# Belirtilmişse maksimum belleği ayarla
if [ -n "$REDIS_MAX_MEMORY" ]; then
    sed -i "s/maxmemory 256mb/maxmemory $REDIS_MAX_MEMORY/" /etc/redis/redis.conf
    log "Maksimum bellek $REDIS_MAX_MEMORY olarak ayarlandı"
fi

# Veri dizini yoksa oluştur
mkdir -p /data
chown redis:redis /data

# Başlatmayı logla
log "YKS Genius için Redis sunucusu başlatılıyor..."
log "Yapılandırma: /etc/redis/redis.conf"
log "Veri dizini: /data"
log "Maksimum bellek: $(grep '^maxmemory' /etc/redis/redis.conf | cut -d' ' -f2)"

# Redis sunucusunu başlat
exec "$@"