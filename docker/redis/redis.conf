# YKS Genius için Redis Yapılandırması
# AI ajan önbellekleme ve oturum yönetimi için optimize edilmiş

# Ağ ve Bağlantı Ayarları
bind 0.0.0.0
protected-mode yes
port 6379
timeout 300
tcp-keepalive 300

# Genel Ayarlar
daemonize no
supervised no
pidfile /var/run/redis.pid
loglevel notice
logfile ""

# Veritabanı Ayarları
databases 16
save 900 1    # 900 saniyede en az 1 anahtar değiştiyse kaydet
save 300 10   # 300 saniyede en az 10 anahtar değiştiyse kaydet
save 60 10000 # 60 saniyede en az 10000 anahtar değiştiyse kaydet
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Güvenlik
requirepass yks_redis_password
# rename-command FLUSHDB ""
# rename-command FLUSHALL ""

# Bellek Yönetimi (AI önbellekleme için kritik)
maxmemory 256mb
maxmemory-policy allkeys-lru  # En az kullanılan anahtarları kaldır
maxmemory-samples 5

# Kalıcılık (Daha iyi dayanıklılık için AOF)
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Yavaş Log (izleme için)
slowlog-log-slower-than 10000  # 10ms'den yavaş sorguları logla
slowlog-max-len 128

# Gelişmiş Yapılandırma
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000

# Aktif yeniden karma
activerehashing yes

# İstemci çıktı tamponu limitleri
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Hz ayarı
hz 10

# Dinamik HZ
dynamic-hz yes

# RDB dosya sıkıştırması
rdb-save-incremental-fsync yes

# LUA komut dosyası zaman limiti (milisaniye)
lua-time-limit 5000

# Redis gecikmesini izleme
latency-monitor-threshold 0

# Olay bildirimi
notify-keyspace-events ""

# Gelişmiş yapılandırma
list-max-ziplist-entries 512
list-max-ziplist-value 64
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# YKS Genius özel ayarları
# AI yanıtları için önbellek TTL'leri genellikle uygulama tarafından ayarlanır
# Oturum verileri için varsayılan geçerlilik süresi: 30 dakika (1800 saniye)