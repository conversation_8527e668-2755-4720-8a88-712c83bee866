# YKS Genius için Redis Önbellek
FROM redis:7-alpine

# İzleme için ek araçları yükle
RUN apk add --no-cache curl

# Özel yapılandırmayı kopyala
COPY redis.conf /etc/redis/redis.conf
COPY redis-entrypoint.sh /usr/local/bin/

# Entrypoint'i çalıştırılabilir yap
RUN chmod +x /usr/local/bin/redis-entrypoint.sh

# Redis kullanıcısı oluştur ve izinleri ayarla
RUN addgroup -g 999 redis && \
    adduser -D -u 999 -G redis redis && \
    mkdir -p /data && \
    chown -R redis:redis /data && \
    chown -R redis:redis /etc/redis/

# Sağlık kontrolü
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD redis-cli --raw incr ping || exit 1

# Redis kullanıcısına geç
USER redis

# Portu aç
EXPOSE 6379

# Veri dizinini ayarla
VOLUME ["/data"]

# Özel entrypoint kullan
ENTRYPOINT ["/usr/local/bin/redis-entrypoint.sh"]
CMD ["redis-server", "/etc/redis/redis.conf"]