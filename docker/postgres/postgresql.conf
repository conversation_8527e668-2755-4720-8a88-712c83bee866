# YKS Genius için PostgreSQL Yapılandırması
# Sık okuma ve orta düzey yazma işlemleri olan AI/ML iş yükü için optimize edilmiş

# Bağlantı Ayarları
listen_addresses = '*'
port = 5432
max_connections = 100
superuser_reserved_connections = 3

# Bellek Ayarları (mevcut RAM'e göre ayarlayın)
shared_buffers = 256MB          # Küçük sunucular için RAM'in %25'i
effective_cache_size = 1GB      # Küçük sunucular için RAM'in %75'i
work_mem = 4MB                  # İşlem başına bellek
maintenance_work_mem = 64MB     # Bakım işlemleri için

# Checkpoint Ayarları
checkpoint_completion_target = 0.7
checkpoint_timeout = 10min
max_wal_size = 1GB
min_wal_size = 256MB

# Sorgu Planlayıcı Ayarları
random_page_cost = 1.1          # SSD optimize
effective_io_concurrency = 200  # SSD eşzamanlı I/O destekler

# Write-Ahead Loglama
wal_level = replica
wal_compression = on
archive_mode = off              # Tek başına kurulum için devre dışı

# Performans ve İzleme
log_statement = 'none'          # Üretimde tüm ifadeleri loglama
log_duration = off
log_min_duration_statement = 1000  # Yavaş sorguları logla (>1s)

# Paylaşılan ön yükleme kütüphaneleri
shared_preload_libraries = 'pg_stat_statements'

# Otomatik VACUUM Ayarları (AI iş yükleri için önemli)
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min

# Yerel Ayarlar
lc_messages = 'C'
lc_monetary = 'C'
lc_numeric = 'C'
lc_time = 'C'
default_text_search_config = 'pg_catalog.english'

# Saat Dilimi
timezone = 'UTC'

# Uygulamaya özel ayarlar
# Bunlar uygulama tarafından geçersiz kılınabilir
# statement_timeout = 30000      # 30 saniye
# lock_timeout = 10000           # 10 saniye
# idle_in_transaction_session_timeout = 300000  # 5 dakika

# Güvenlik Ayarları (temel)
ssl = off                       # Üretimde sertifikalarla etkinleştirin
password_encryption = scram-sha-256

# Geliştirme/hata ayıklama için loglama (üretim için ayarlayın)
logging_collector = on
log_directory = 'pg_log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_truncate_on_rotation = on