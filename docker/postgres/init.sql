-- YKS Genius Veritabanı Başlatma Scripti
-- <PERSON>ere<PERSON><PERSON> u<PERSON>tı<PERSON> ve başlang<PERSON>ç tablolarını oluşturur

-- Uzantıları etkinleştir
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- Veritabanı kullanıcısını oluştur (yoksa)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'yks_user') THEN
        CREATE ROLE yks_user LOGIN PASSWORD 'yks_password';
    END IF;
END
$$;

-- Yetkileri ver
GRANT ALL PRIVILEGES ON DATABASE yks_genius TO yks_user;
GRANT ALL ON SCHEMA public TO yks_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO yks_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO yks_user;

-- Gelecekteki nesneler için varsayılan yetkileri ayarla
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO yks_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO yks_user;

-- Performans optimizasyonları
-- Daha iyi performans için shared buffer'ları artır
-- Bunlar postgresql.conf üzerinden uygulanacak

-- Yaygın sorgular için indeksler oluştur (SQLAlchemy migration'ları tarafından oluşturulacak)
-- Burada sadece veritabanı ortamını hazırlıyoruz

-- Başarılı başlatmayı logla
INSERT INTO pg_stat_statements_reset();

COMMENT ON DATABASE yks_genius IS 'YKS Genius Çok Ajanlı Sınav Hazırlık Sistemi Veritabanı';

-- Uygulamaya özel yapılandırmaları oluştur
CREATE TABLE IF NOT EXISTS app_config (
    id SERIAL PRIMARY KEY,
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Varsayılan yapılandırmaları ekle
INSERT INTO app_config (key, value, description) VALUES 
    ('db_version', '1.0.0', 'Veritabanı şema versiyonu'),
    ('init_date', CURRENT_TIMESTAMP::TEXT, 'Veritabanı başlatma tarihi'),
    ('timezone', 'UTC', 'Uygulama saat dilimi')
ON CONFLICT (key) DO NOTHING;

-- updated_at zaman damgaları için trigger oluştur
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Başarı mesajı
DO $$
BEGIN
    RAISE NOTICE 'YKS Genius veritabanı başarıyla başlatıldı!';
END
$$;