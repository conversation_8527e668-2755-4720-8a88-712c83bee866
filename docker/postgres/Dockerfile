# YKS Genius için PostgreSQL Veritabanı
FROM postgres:15-alpine

# Ek uzantıları yükle
RUN apk add --no-cache postgresql-contrib

# Ortam değişkenlerini ayarla
ENV POSTGRES_DB=yks_genius
ENV POSTGRES_USER=yks_user
ENV POSTGRES_PASSWORD=yks_password
ENV POSTGRES_INITDB_ARGS="--encoding=UTF8 --locale=C"

# Başlatma scriptlerini kopyala
COPY init.sql /docker-entrypoint-initdb.d/
COPY postgresql.conf /etc/postgresql/postgresql.conf

# PostgreSQL'i performans için yapılandır
RUN echo "host all all 0.0.0.0/0 md5" >> /var/lib/postgresql/data/pg_hba.conf || true

# Sağlık kontrolü
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD pg_isready -U yks_user -d yks_genius

# Portu aç
EXPOSE 5432

# Veri dizinini ayarla
VOLUME ["/var/lib/postgresql/data"]