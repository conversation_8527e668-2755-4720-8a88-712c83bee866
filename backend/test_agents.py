#!/usr/bin/env python3
"""
Test script to verify the multi-agent system works
Run this after setting up your environment
"""

import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Verify Gemini API key
if not os.getenv("GEMINI_API_KEY"):
    print("❌ Error: GEMINI_API_KEY not found in .env file")
    print("Please add your Gemini API key to the .env file")
    print("Get one from: https://makersuite.google.com/app/apikey")
    exit(1)

async def test_agents():
    """Test the multi-agent system"""
    print("🧪 Testing YKS Genius Multi-Agent System")
    print("=" * 50)
    
    try:
        # Import required modules
        from app.core.gemini_client import GeminiOptimizer
        from app.agents import YKSAgentOrchestrator
        
        # Initialize Gemini client
        print("\n1️⃣ Initializing Gemini client...")
        gemini_client = GeminiOptimizer()
        print("✅ Gemini client initialized")
        
        # Initialize orchestrator
        print("\n2️⃣ Initializing agent orchestrator...")
        orchestrator = YKSAgentOrchestrator(gemini_client=gemini_client)
        print("✅ Orchestrator initialized with 5 agents")
        
        # Test different agent interactions
        test_cases = [
            {
                "name": "Strategy Agent Test",
                "message": "Bana YKS için haftalık çalışma planı oluştur",
                "expected_agent": "strategy"
            },
            {
                "name": "Content Agent Test", 
                "message": "Matematik'te limit konusunu anlat",
                "expected_agent": "content"
            },
            {
                "name": "Practice Agent Test",
                "message": "Bana matematik'ten 5 soru sor",
                "expected_agent": "practice"
            },
            {
                "name": "Analytics Agent Test",
                "message": "Son 1 haftadaki ilerlememi göster",
                "expected_agent": "analytics"
            },
            {
                "name": "Mentor Agent Test",
                "message": "Çok strese girdim, motivasyona ihtiyacım var",
                "expected_agent": "mentor"
            }
        ]
        
        print("\n3️⃣ Testing agent interactions...")
        for i, test in enumerate(test_cases, 1):
            print(f"\n--- Test {i}: {test['name']} ---")
            print(f"Message: {test['message']}")
            
            try:
                # Process request
                response = await orchestrator.process_student_request(
                    student_id="test_student",
                    message=test['message']
                )
                
                # Check response
                if response['success']:
                    print(f"✅ Response from: {response.get('agent', 'unknown')}")
                    print(f"Message preview: {response['message'][:100]}...")
                    
                    if response.get('agent') == test['expected_agent']:
                        print(f"✅ Correct agent handled the request")
                    else:
                        print(f"⚠️  Expected {test['expected_agent']}, got {response.get('agent')}")
                else:
                    print(f"❌ Error: {response.get('message')}")
                    
            except Exception as e:
                print(f"❌ Test failed: {str(e)}")
        
        # Test inter-agent collaboration
        print("\n\n4️⃣ Testing inter-agent collaboration...")
        print("Message: 'Zayıf olduğum konular için çalışma planı oluştur'")
        
        response = await orchestrator.process_student_request(
            student_id="test_student",
            message="Zayıf olduğum konular için çalışma planı oluştur",
            context={"requires_collaboration": True}
        )
        
        if response['success']:
            print(f"✅ Collaboration successful")
            print(f"Agents involved: {response.get('agent_history', [])}")
        
        # Show orchestrator stats
        print("\n\n5️⃣ Agent Statistics:")
        stats = orchestrator.get_agent_stats()
        print(f"Total agents: {stats['total_agents']}")
        print(f"Active agents: {', '.join(stats['active_agents'])}")
        print(f"Gemini usage: {stats['gemini_usage']}")
        
        print("\n\n✅ All tests completed successfully!")
        print("\n💡 Next steps:")
        print("1. Start the full application with: python run.py")
        print("2. Access the API docs at: http://localhost:8000/docs")
        print("3. Test WebSocket at: ws://localhost:8000/ws/test_student")
        
    except ImportError as e:
        print(f"\n❌ Import error: {str(e)}")
        print("Make sure you're in the backend directory and virtual environment is activated")
        print("Run: source venv/bin/activate (Linux/Mac) or venv\\Scripts\\activate (Windows)")
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")
        print("Check your .env file and make sure all dependencies are installed")

if __name__ == "__main__":
    # Run the test
    asyncio.run(test_agents())