#!/usr/bin/env python3
"""
Kötü çevirileri düzelten script
İngilizce-Türkçe karışımlarını temizler
"""

import os
import re
from pathlib import Path

def fix_bad_translations(file_path: Path):
    """Dosyadaki kötü çevirileri düzelt"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # Yaygın kötü çeviri kalıplarını düzelt
        fixes = [
            # Basit kelime çevirileri
            (r'\bfor\b', r'için'),
            (r'\bwith\b', r'ile'),
            (r'\band\b', r've'),
            (r'\bto\b', r''),
            (r'\bfrom\b', r'den'),
            (r'\bof\b', r'nin'),
            (r'\bin\b', r'de'),
            (r'\bon\b', r'üzerinde'),
            (r'\bat\b', r'de'),
            (r'\bby\b', r'tarafından'),
            (r'\busing\b', r'kullanarak'),
            (r'\bthe\b', r''),
            (r'\ba\b', r'bir'),
            (r'\ban\b', r'bir'),

            # Belirli kalıplar
            (r'Get\s+(\w+)\s+for\s+(\w+)', r'\2 için \1 al'),
            (r'Check\s+eğer', r'Eğer kontrol et'),
            (r'Create\s+new\s+(\w+)', r'Yeni \1 oluştur'),
            (r'Update\s+(\w+)\s+(profile|data)', r'\1 \2\'sini güncelle'),
            (r'Failed\s+to\s+(\w+)', r'\1 başarısız'),
            (r'Error\s+(\w+)\s+(\w+)', r'\1 \2 hatası'),
            (r'Add\s+(\w+)\s+to\s+(\w+)', r'\1\'i \2\'ye ekle'),
            (r'Search\s+for\s+(\w+)', r'\1 ara'),
            (r'List\s+of\s+(\w+)', r'\1 listesi'),
            (r'Number\s+of\s+(\w+)', r'\1 sayısı'),

            # Türkçe + İngilizce karışımları
            (r'(\w+)\s+ile\s+(\w+)', lambda m: f'{m.group(1)} ile {translate_word(m.group(2))}'),
            (r'(\w+)\s+ve\s+(\w+)', lambda m: f'{m.group(1)} ve {translate_word(m.group(2))}'),
            (r'(\w+)\s+için\s+(\w+)', lambda m: f'{m.group(2)} için {m.group(1)}'),
            (r'(\w+)\s+den\s+(\w+)', lambda m: f'{m.group(2)}\'den {m.group(1)}'),
            (r'(\w+)\s+e\s+(\w+)', lambda m: f'{m.group(1)}\'i {m.group(2)}\'ye'),

            # Özel durumlar
            (r'Invalidate\s+Önbellek', r'Önbelleği geçersiz kıl'),
            (r'Rate\s+Sınır', r'Hız sınırı'),
            (r'API\s+Anahtar', r'API anahtarı'),
            (r'Demo\s+mode', r'Demo modu'),
            (r'Performans\s+Analitik', r'Performans analitikleri'),
            (r'Study\s+Oturum', r'Çalışma oturumu'),
            (r'Ajan\s+interaction', r'Ajan etkileşimi'),
            (r'Müfredat-based\s+İçerik', r'Müfredat tabanlı içerik'),
            (r'İçerik-related\s+requests', r'İçerikle ilgili istekler'),
            (r'Öğrenci\s+management', r'Öğrenci yönetimi'),
            (r'Soru\s+generation', r'Soru üretimi'),
            (r'Multi-choice\s+questions\s+ile', r'Açıklamalı çoktan seçmeli sorular'),
            (r'Subject\s+ve\s+topic', r'Ders ve konu'),
            (r'Difficulty\s+ve\s+YKS', r'Zorluk ve YKS'),
            (r'Semantic\s+search\s+ve', r'Semantik arama ve'),
            (r'İçerik\s+similarity', r'İçerik benzerliği'),
            (r'Öğrenci\s+query', r'Öğrenci sorgusu'),
            (r'Knowledge\s+base', r'Bilgi tabanı'),
            (r'Vektör\s+Deposu\s+for', r'Vektör deposu'),
            (r'using\s+ChromaDB', r'ChromaDB kullanarak'),
            (r'Smart\s+Model\s+selection', r'Akıllı model seçimi'),
            (r'Aggressive\s+caching', r'Agresif önbellekleme'),
            (r'Batch\s+processing', r'Toplu işleme'),
            (r'Demo\s+mode\s+fallbacks', r'Demo modu yedekleri'),
            (r'Shared\s+state\s+arasında', r'Ajanlar arasında paylaşılan durum'),
            (r'bu\s+state\s+is\s+passed', r'Bu durum geçirilir'),
            (r'Decision\s+types\s+for\s+routing', r'Yönlendirme için karar türleri'),
            (r'requests\s+e\s+agents', r'istekleri ajanlara'),

            # Dosya başlıkları
            (r'for\s+YKS\s+Genius', r'YKS Genius için'),
            (r'Model\s+for\s+YKS', r'YKS için model'),
            (r'API\s+endpoints\s+for', r'API uç noktaları'),
            (r'Tracks\s+(\w+)\s+ve\s+(\w+)', r'\1 ve \2\'yi takip eder'),

            # Yorum satırları
            (r'#\s*(\w+)\s+for\s+(\w+)', r'# \2 için \1'),
            (r'#\s*(\w+)\s+ile\s+(\w+)', r'# \1 ile \2'),
            (r'#\s*(\w+)\s+ve\s+(\w+)', r'# \1 ve \2'),

            # Fonksiyon açıklamaları
            (r'"""(\w+)\s+(\w+)\s+for\s+(\w+)"""', r'"""\3 için \1 \2"""'),
            (r'"""Get\s+(\w+)\s+for\s+(\w+)"""', r'"""\2 için \1 al"""'),
            (r'"""(\w+)\s+(\w+)\s+ile\s+(\w+)"""', r'"""\1 \2 ile \3"""'),
        ]

        # Düzeltmeleri uygula
        for pattern, replacement in fixes:
            if callable(replacement):
                content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
            else:
                content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)

        # Değişiklik varsa dosyayı güncelle
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Düzeltildi: {file_path}")
            return True
        else:
            return False

    except Exception as e:
        print(f"❌ Hata {file_path}: {e}")
        return False

def translate_word(word):
    """Basit kelime çevirisi"""
    translations = {
        'caching': 'önbellekleme',
        'management': 'yönetimi',
        'generation': 'üretimi',
        'analytics': 'analitikleri',
        'tracking': 'takibi',
        'interaction': 'etkileşimi',
        'content': 'içerik',
        'similarity': 'benzerliği',
        'query': 'sorgusu',
        'base': 'tabanı',
        'selection': 'seçimi',
        'processing': 'işleme',
        'fallbacks': 'yedekleri',
        'routing': 'yönlendirme',
        'agents': 'ajanlar',
        'endpoints': 'uç noktaları',
        'explanations': 'açıklamalar',
        'educational': 'eğitsel',
        'patterns': 'kalıpları',
        'intents': 'niyetleri',
        'guides': 'kılavuzları',
        'formulas': 'formülleri',
        'reference': 'referans',
        'materials': 'materyalleri'
    }
    return translations.get(word.lower(), word)

def main():
    """Ana fonksiyon"""
    app_dir = Path(__file__).parent / "app"

    # Python dosyalarını bul
    python_files = []
    for root, dirs, files in os.walk(app_dir):
        for file in files:
            if file.endswith('.py'):
                python_files.append(Path(root) / file)

    print(f"🔍 {len(python_files)} Python dosyası düzeltiliyor...")

    fixed_count = 0

    # Her dosyayı düzelt
    for file_path in python_files:
        if fix_bad_translations(file_path):
            fixed_count += 1

    print(f"\n✅ {fixed_count} dosya düzeltildi!")

if __name__ == "__main__":
    main()
