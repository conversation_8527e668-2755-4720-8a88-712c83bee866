#!/usr/bin/env python3
"""
Türkçe Dokümantasyon Çevirisi Scripti
Kodbase'deki İngilizce dokümantasyonu Türkçe'ye çevirir
"""

import os
import re
from pathlib import Path

# İngilizce -> Türkçe çeviri sözlüğü
TRANSLATIONS = {
    # Genel terimler
    "Student": "Öğrenci",
    "Question": "Soru",
    "Answer": "Cevap",
    "Session": "Oturum",
    "Progress": "İlerleme",
    "Agent": "Ajan",
    "Analytics": "Analitik",
    "Performance": "Performans",
    "Metrics": "Metrikler",
    "Statistics": "İstatistikler",
    "Database": "Veritabanı",
    "Model": "Model",
    "Schema": "Şema",
    "API": "API",
    "Response": "Yanıt",
    "Request": "İstek",
    "Configuration": "Ya<PERSON><PERSON><PERSON>ırma",
    "Settings": "<PERSON>yarlar",
    "Cache": "<PERSON>nbellek",
    "Vector Store": "Vektör Deposu",
    "Curriculum": "Müfredat",
    "Content": "İçerik",
    "Practice": "Pratik",
    "Strategy": "Strateji",
    "Mentor": "Mentor",
    "Orchestrator": "Orkestratör",

    # Metodlar ve fonksiyonlar
    "Initialize": "Başlat",
    "Load": "Yükle",
    "Save": "Kaydet",
    "Update": "Güncelle",
    "Delete": "Sil",
    "Create": "Oluştur",
    "Generate": "Üret",
    "Calculate": "Hesapla",
    "Validate": "Doğrula",
    "Process": "İşle",
    "Execute": "Çalıştır",
    "Handle": "İşle",
    "Manage": "Yönet",
    "Configure": "Yapılandır",
    "Setup": "Kur",
    "Connect": "Bağlan",
    "Disconnect": "Bağlantıyı kes",
    "Start": "Başlat",
    "Stop": "Durdur",
    "Pause": "Duraklat",
    "Resume": "Devam et",
    "Reset": "Sıfırla",
    "Clear": "Temizle",
    "Refresh": "Yenile",
    "Reload": "Yeniden yükle",
    "Export": "Dışa aktar",
    "Import": "İçe aktar",
    "Backup": "Yedekle",
    "Restore": "Geri yükle",
    "Sync": "Senkronize et",
    "Fetch": "Getir",
    "Send": "Gönder",
    "Receive": "Al",
    "Parse": "Ayrıştır",
    "Format": "Biçimlendir",
    "Transform": "Dönüştür",
    "Convert": "Çevir",
    "Encode": "Kodla",
    "Decode": "Çöz",
    "Encrypt": "Şifrele",
    "Decrypt": "Şifre çöz",
    "Authenticate": "Kimlik doğrula",
    "Authorize": "Yetkilendir",
    "Login": "Giriş yap",
    "Logout": "Çıkış yap",
    "Register": "Kayıt ol",
    "Subscribe": "Abone ol",
    "Unsubscribe": "Abonelikten çık",

    # Özellikler ve durumlar
    "Active": "Aktif",
    "Inactive": "Pasif",
    "Enabled": "Etkin",
    "Disabled": "Devre dışı",
    "Available": "Mevcut",
    "Unavailable": "Mevcut değil",
    "Online": "Çevrimiçi",
    "Offline": "Çevrimdışı",
    "Connected": "Bağlı",
    "Disconnected": "Bağlı değil",
    "Ready": "Hazır",
    "Busy": "Meşgul",
    "Idle": "Boşta",
    "Running": "Çalışıyor",
    "Stopped": "Durduruldu",
    "Paused": "Duraklatıldı",
    "Completed": "Tamamlandı",
    "Failed": "Başarısız",
    "Success": "Başarılı",
    "Error": "Hata",
    "Warning": "Uyarı",
    "Info": "Bilgi",
    "Debug": "Hata ayıklama",
    "Valid": "Geçerli",
    "Invalid": "Geçersiz",
    "Required": "Gerekli",
    "Optional": "İsteğe bağlı",
    "Default": "Varsayılan",
    "Custom": "Özel",
    "Auto": "Otomatik",
    "Manual": "Manuel",
    "Public": "Genel",
    "Private": "Özel",
    "Protected": "Korumalı",
    "Secure": "Güvenli",
    "Insecure": "Güvensiz",

    # Veri türleri
    "String": "Metin",
    "Integer": "Tam sayı",
    "Float": "Ondalık sayı",
    "Boolean": "Mantıksal",
    "Array": "Dizi",
    "List": "Liste",
    "Dictionary": "Sözlük",
    "Object": "Nesne",
    "Date": "Tarih",
    "Time": "Zaman",
    "DateTime": "Tarih-Zaman",
    "Timestamp": "Zaman damgası",
    "UUID": "Benzersiz tanımlayıcı",
    "ID": "Kimlik",
    "Key": "Anahtar",
    "Value": "Değer",
    "Name": "Ad",
    "Title": "Başlık",
    "Description": "Açıklama",
    "Content": "İçerik",
    "Text": "Metin",
    "Number": "Sayı",
    "Count": "Sayım",
    "Total": "Toplam",
    "Sum": "Toplam",
    "Average": "Ortalama",
    "Minimum": "Minimum",
    "Maximum": "Maksimum",
    "Range": "Aralık",
    "Limit": "Sınır",
    "Offset": "Ofset",
    "Page": "Sayfa",
    "Size": "Boyut",
    "Length": "Uzunluk",
    "Width": "Genişlik",
    "Height": "Yükseklik",
    "Depth": "Derinlik",
    "Weight": "Ağırlık",
    "Volume": "Hacim",
    "Area": "Alan",
    "Perimeter": "Çevre",
    "Radius": "Yarıçap",
    "Diameter": "Çap",
    "Angle": "Açı",
    "Distance": "Mesafe",
    "Speed": "Hız",
    "Velocity": "Hız",
    "Acceleration": "İvme",
    "Force": "Kuvvet",
    "Energy": "Enerji",
    "Power": "Güç",
    "Pressure": "Basınç",
    "Temperature": "Sıcaklık",
    "Humidity": "Nem",
    "Density": "Yoğunluk",
    "Mass": "Kütle",
    "Frequency": "Frekans",
    "Amplitude": "Genlik",
    "Wavelength": "Dalga boyu",
    "Period": "Periyot",
    "Phase": "Faz",
    "Voltage": "Voltaj",
    "Current": "Akım",
    "Resistance": "Direnç",
    "Capacitance": "Kapasitans",
    "Inductance": "Endüktans",
    "Impedance": "Empedans",
    "Reactance": "Reaktans",
    "Conductance": "İletkenlik",
    "Conductivity": "İletkenlik",
    "Resistivity": "Direnç",
    "Permittivity": "Geçirgenlik",
    "Permeability": "Geçirgenlik",
    "Susceptibility": "Duyarlılık",
    "Polarization": "Polarizasyon",
    "Magnetization": "Mıknatıslanma",
    "Flux": "Akı",
    "Field": "Alan",
    "Potential": "Potansiyel",
    "Gradient": "Gradyan",
    "Divergence": "Diverjans",
    "Curl": "Curl",
    "Laplacian": "Laplasiyen"
}

def translate_docstring(text: str) -> str:
    """Docstring'i Türkçe'ye çevir"""
    # Basit çeviriler
    for eng, tr in TRANSLATIONS.items():
        # Tam kelime eşleşmesi için word boundary kullan
        pattern = r'\b' + re.escape(eng) + r'\b'
        text = re.sub(pattern, tr, text, flags=re.IGNORECASE)

    # Yaygın İngilizce ifadeleri çevir
    replacements = [
        (r'\bfor\s+(\w+)\s+in\s+', r'\1 için '),
        (r'\bwith\s+', r'ile '),
        (r'\band\s+', r've '),
        (r'\bor\s+', r'veya '),
        (r'\bif\s+', r'eğer '),
        (r'\bthen\s+', r'o zaman '),
        (r'\belse\s+', r'aksi takdirde '),
        (r'\bwhen\s+', r'ne zaman '),
        (r'\bwhere\s+', r'nerede '),
        (r'\bhow\s+', r'nasıl '),
        (r'\bwhat\s+', r'ne '),
        (r'\bwhy\s+', r'neden '),
        (r'\bwhich\s+', r'hangi '),
        (r'\bwho\s+', r'kim '),
        (r'\bwhose\s+', r'kimin '),
        (r'\bwhom\s+', r'kime '),
        (r'\bthat\s+', r'o '),
        (r'\bthis\s+', r'bu '),
        (r'\bthese\s+', r'bunlar '),
        (r'\bthose\s+', r'onlar '),
        (r'\bhere\s+', r'burada '),
        (r'\bthere\s+', r'orada '),
        (r'\bnow\s+', r'şimdi '),
        (r'\bthen\s+', r'sonra '),
        (r'\bbefore\s+', r'önce '),
        (r'\bafter\s+', r'sonra '),
        (r'\bduring\s+', r'sırasında '),
        (r'\bwhile\s+', r'iken '),
        (r'\buntil\s+', r'kadar '),
        (r'\bsince\s+', r'beri '),
        (r'\bfrom\s+', r'den '),
        (r'\bto\s+', r'e '),
        (r'\bin\s+', r'de '),
        (r'\bon\s+', r'üzerinde '),
        (r'\bat\s+', r'de '),
        (r'\bby\s+', r'tarafından '),
        (r'\bof\s+', r'nin '),
        (r'\babout\s+', r'hakkında '),
        (r'\bover\s+', r'üzerinde '),
        (r'\bunder\s+', r'altında '),
        (r'\bnear\s+', r'yakın '),
        (r'\bfar\s+', r'uzak '),
        (r'\binside\s+', r'içinde '),
        (r'\boutside\s+', r'dışında '),
        (r'\bbetween\s+', r'arasında '),
        (r'\bamong\s+', r'arasında '),
        (r'\bthrough\s+', r'boyunca '),
        (r'\bacross\s+', r'karşısında '),
        (r'\balong\s+', r'boyunca '),
        (r'\baround\s+', r'etrafında '),
        (r'\bbehind\s+', r'arkasında '),
        (r'\bin front of\s+', r'önünde '),
        (r'\bnext to\s+', r'yanında '),
        (r'\bbeside\s+', r'yanında '),
        (r'\babove\s+', r'üstünde '),
        (r'\bbelow\s+', r'altında '),
        (r'\bup\s+', r'yukarı '),
        (r'\bdown\s+', r'aşağı '),
        (r'\bleft\s+', r'sol '),
        (r'\bright\s+', r'sağ '),
        (r'\bforward\s+', r'ileri '),
        (r'\bbackward\s+', r'geri '),
        (r'\binward\s+', r'içeri '),
        (r'\boutward\s+', r'dışarı '),
        (r'\bupward\s+', r'yukarı '),
        (r'\bdownward\s+', r'aşağı '),
        (r'\bnorthward\s+', r'kuzeye '),
        (r'\bsouthward\s+', r'güneye '),
        (r'\beastward\s+', r'doğuya '),
        (r'\bwestward\s+', r'batıya ')
    ]

    for pattern, replacement in replacements:
        text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)

    return text

def process_file(file_path: Path):
    """Dosyadaki İngilizce dokümantasyonu çevir"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Docstring'leri bul ve çevir
        # Triple quote docstring'ler
        def replace_docstring(match):
            quotes = match.group(1)
            docstring = match.group(2)
            translated = translate_docstring(docstring)
            return f'{quotes}{translated}{quotes}'

        # """...""" formatındaki docstring'ler
        content = re.sub(r'(""")(.*?)(""")', replace_docstring, content, flags=re.DOTALL)

        # '''...''' formatındaki docstring'ler
        content = re.sub(r"(''')(.*?)(''')", replace_docstring, content, flags=re.DOTALL)

        # Tek satırlık comment'ler
        def replace_comment(match):
            comment = match.group(1)
            translated = translate_docstring(comment)
            return f'# {translated}'

        content = re.sub(r'#\s*(.+)', replace_comment, content)

        # Dosyayı güncelle
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"✅ Çevrildi: {file_path}")

    except Exception as e:
        print(f"❌ Hata {file_path}: {e}")

def main():
    """Ana fonksiyon"""
    app_dir = Path(__file__).parent / "app"

    # Python dosyalarını bul
    python_files = []
    for root, dirs, files in os.walk(app_dir):
        for file in files:
            if file.endswith('.py'):
                python_files.append(Path(root) / file)

    print(f"🔍 {len(python_files)} Python dosyası bulundu")

    # Her dosyayı işle
    for file_path in python_files:
        process_file(file_path)

    print("✅ Tüm dosyalar işlendi!")

if __name__ == "__main__":
    main()