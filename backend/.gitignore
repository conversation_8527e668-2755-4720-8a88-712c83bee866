# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
ENV/
.venv

# Environment variables
.env
.env.local
.env.*.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.hypothesis/

# Database
*.db
*.sqlite
*.sqlite3
yks_genius.db

# ChromaDB
chroma_db/

# Logs
logs/
*.log

# OS
.DS_Store
Thumbs.db

# Package files
dist/
build/
*.egg-info/

# Poetry
poetry.lock