#!/usr/bin/env python3
"""
YKS Genius Hackathon Demo Script
BTK Akademi AI Hackathon 2025

Bu script, j<PERSON>ri sunumu için hazırlanmış demo senaryolarını içerir.
"""

import asyncio
import json
from datetime import datetime
from typing import List, Dict, Any

# Demo scenarios for 1-minute video
DEMO_SCENARIOS = [
    {
        "title": "🎯 Kişiselleştirilmiş Çalışma Planı",
        "duration": 10,
        "messages": [
            ("user", "<PERSON><PERSON><PERSON><PERSON>, YKS'ye hazırlanıyorum ve çalışma planına ihtiyacım var"),
            ("agent", "Merhab<PERSON>! 👋 Sana özel bir YKS çalışma planı hazırlayacağım. Hangi bölümü hedefliyorsun?"),
            ("user", "Tıp okumak istiyorum, matematik ve fen konularında zorlanıyorum"),
            ("agent", "Anladım! Tıp için SAY ağırlıklı bir plan hazırlıyorum. Matematik ve Fen'e ekstra odaklanacağız! 📚")
        ]
    },
    {
        "title": "📚 Akıllı Konu Anlatımı",
        "duration": 15,
        "messages": [
            ("user", "Limit konusunu anlamıyorum, bana anlatır mısın?"),
            ("agent", "Tabii ki! Limit konusunu en basit haliyle anlatayım 🎯\n\nLimit, bir fonksiyonun belirli bir noktaya yaklaşırken aldığı değerdir.\n\nÖrnek: f(x) = x² fonksiyonunda x→2 iken limit = 4"),
            ("user", "Örnek soru çözer misin?"),
            ("agent", "Hemen bir örnek çözelim:\n\nlim(x→3) (x²-9)/(x-3) = ?\n\nÇözüm: Payı çarpanlarına ayıralım\n= lim(x→3) (x+3)(x-3)/(x-3)\n= lim(x→3) (x+3) = 6 ✨")
        ]
    },
    {
        "title": "📝 Adaptif Soru Üretimi",
        "duration": 15,
        "messages": [
            ("user", "Bana matematik sorusu sor"),
            ("agent", "Seviyene uygun bir soru hazırlıyorum... 📝\n\nSoru: 2x + 5 = 17 denkleminin çözüm kümesi nedir?\n\nA) {6}  B) {7}  C) {8}  D) {9}  E) {10}"),
            ("user", "Cevap A, 6"),
            ("agent", "Harika! Doğru cevap! 🎉\n\nÇözüm: 2x = 17-5 = 12, x = 6\n\nBir soru daha çözmek ister misin? Bu sefer biraz daha zor olsun! 💪")
        ]
    },
    {
        "title": "📊 Performans Analizi",
        "duration": 10,
        "messages": [
            ("user", "Son performansımı göster"),
            ("agent", "İşte son 2 haftalık performans analizin 📊\n\n✅ Matematik: %72 (↑15%)\n✅ Fizik: %68 (↑8%)\n⚠️ Kimya: %45 (↓3%)\n✅ Biyoloji: %80 (↑12%)\n\nGenel başarı: %66 - Harika ilerleme! 🚀"),
            ("user", "Hangi konulara odaklanmalıyım?"),
            ("agent", "Analize göre öncelikli konular:\n\n1️⃣ Kimya - Organik Kimya (%35)\n2️⃣ Fizik - Elektrik (%42)\n3️⃣ Matematik - İntegral (%48)\n\nBu konulara günde 1'er saat ayırmanı öneriyorum! 💡")
        ]
    },
    {
        "title": "💪 Motivasyon Desteği",
        "duration": 10,
        "messages": [
            ("user", "Çok stres oluyorum, yapamayacağım gibi hissediyorum"),
            ("agent", "Hey, bu hisler çok normal! 🤗 Her YKS öğrencisi bu süreçten geçiyor.\n\nUnutma:\n• Her gün küçük adımlarla ilerliyorsun\n• Şu ana kadar harika iş çıkardın\n• Stres yapmak yerine planlı çalış\n\nSenin başaracağına inanıyorum! 💪"),
            ("user", "Teşekkür ederim, motivasyonum arttı"),
            ("agent", "Rica ederim! Her zaman yanındayım 🌟 Hadi şimdi kısa bir mola ver, sonra matematik çalışmaya devam edelim! Başaracaksın! 🎯")
        ]
    }
]

def print_demo_header():
    """Print demo header"""
    print("\n" + "="*60)
    print("🚀 YKS GENIUS - BTK Akademi AI Hackathon 2025 Demo")
    print("="*60)
    print("📱 Eğitim Temalı AI Projesi")
    print("🤖 Gemini API ile Çoklu AI Ajanları")
    print("🎯 YKS'ye Hazırlanan Öğrenciler için Kişisel AI Koçu")
    print("="*60 + "\n")

def print_scenario(scenario: Dict[str, Any]):
    """Print a demo scenario"""
    print(f"\n🎬 {scenario['title']} ({scenario['duration']}s)")
    print("-" * 50)
    
    for role, message in scenario['messages']:
        if role == "user":
            print(f"👤 Öğrenci: {message}")
        else:
            print(f"🤖 YKS Genius: {message}")
        print()
        # Simulate delay for video
        import time
        time.sleep(1)

def generate_video_script():
    """Generate script for 1-minute demo video"""
    print("\n📹 1 DAKİKALIK DEMO VİDEO SENARYOSU")
    print("="*60)
    
    script = """
AÇILIŞ (5 saniye):
- Logo ve "YKS Genius - AI ile YKS'ye Hazırlık" yazısı
- "BTK Akademi AI Hackathon 2025" etiketi

ANA DEMO (50 saniye):
1. Öğrenci Karşılama (5s)
   - "Merhaba, YKS'ye hazırlanıyorum"
   - AI hoş geldin mesajı ve 5 özellik

2. Çalışma Planı Oluşturma (10s)
   - Öğrenci zayıf alanlarını belirtir
   - AI kişisel plan önerir

3. Konu Anlatımı (10s)
   - Limit konusu örneği
   - Görsel ve basit açıklama

4. Soru Çözümü (10s)
   - Adaptif soru üretimi
   - Anında geri bildirim

5. Performans Analizi (10s)
   - Grafik ve ilerleme gösterimi
   - AI önerileri

6. Motivasyon (5s)
   - Öğrenci stres belirtir
   - AI motive edici mesaj

KAPANIŞ (5 saniye):
- "YKS Genius - Her Zaman Yanında!"
- GitHub ve canlı demo linkleri
"""
    print(script)

def print_technical_summary():
    """Print technical summary for judges"""
    print("\n🔧 TEKNİK ÖZET")
    print("="*60)
    
    summary = {
        "Ana Teknolojiler": [
            "✅ Google Gemini API (Zorunlu)",
            "✅ LangGraph (Multi-Agent Orchestration)",
            "✅ FastAPI (Backend)",
            "✅ React (Frontend)",
            "✅ WebSocket (Gerçek zamanlı iletişim)"
        ],
        "AI Ajanları": [
            "📅 Strateji Uzmanı - Çalışma planları",
            "📚 Konu Anlatım Uzmanı - Ders içerikleri",
            "📝 Pratik Uzmanı - Soru üretimi",
            "📊 Analiz Uzmanı - Performans takibi",
            "💪 Motivasyon Koçu - Psikolojik destek"
        ],
        "Özellikler": [
            "🎯 YKS'ye özel içerik ve algoritmalar",
            "🤖 Akıllı yönlendirme sistemi",
            "📈 Gerçek zamanlı performans analizi",
            "🔄 Adaptif zorluk ayarı",
            "💾 Agresif önbellekleme (Demo için)"
        ],
        "Kullanıcı Değeri": [
            "✨ Kişiselleştirilmiş öğrenme deneyimi",
            "⏰ 7/24 erişilebilir AI mentor",
            "📊 Veri odaklı ilerleme takibi",
            "🎯 YKS formatına uygun pratik",
            "💰 Ücretsiz kullanım (Gemini free tier)"
        ]
    }
    
    for category, items in summary.items():
        print(f"\n{category}:")
        for item in items:
            print(f"  {item}")

def main():
    """Run demo script"""
    print_demo_header()
    
    # Choice menu
    print("\n📋 DEMO SEÇENEKLERİ:")
    print("1. Tüm senaryoları göster")
    print("2. Video script'ini göster")
    print("3. Teknik özet göster")
    print("4. Canlı demo başlat (API gerekli)")
    
    choice = input("\nSeçiminiz (1-4): ")
    
    if choice == "1":
        for scenario in DEMO_SCENARIOS:
            print_scenario(scenario)
            input("\nDevam etmek için Enter'a basın...")
    
    elif choice == "2":
        generate_video_script()
    
    elif choice == "3":
        print_technical_summary()
    
    elif choice == "4":
        print("\n🌐 Canlı demo için:")
        print("1. Backend'i başlatın: cd backend && python run.py")
        print("2. Frontend'i başlatın: cd frontend && npm run dev")
        print("3. Tarayıcıda açın: http://localhost:5173")
        print("\n💡 Demo modunda fallback yanıtlar otomatik devrede!")
    
    else:
        print("Geçersiz seçim!")
    
    print("\n✅ Demo tamamlandı! Başarılar dileriz! 🎉")

if __name__ == "__main__":
    main()