#!/usr/bin/env python3
"""
YKS Genius Development Server Runner
"""

import sys
import os

def check_environment():
    """Check if we're in the right environment and dependencies are installed"""
    
    # Check if we're in the backend directory
    if not os.path.exists('app'):
        print("❌ Error: Must be run from the backend directory")
        print("💡 Try: cd backend && python run.py")
        sys.exit(1)
    
    # Check if virtual environment is activated or dependencies are installed
    try:
        import uvicorn
        from app.config import settings
    except ImportError as e:
        print("❌ Error: Dependencies not found")
        print(f"Missing: {e}")
        print()
        print("💡 Solutions:")
        print("1. Activate virtual environment:")
        print("   source venv/bin/activate  # Linux/Mac")
        print("   venv\\Scripts\\activate     # Windows")
        print()
        print("2. Or install dependencies:")
        print("   pip install -r requirements.txt")
        print()
        print("3. Or run the quick start script:")
        print("   cd .. && ./quick_start.sh")
        sys.exit(1)
    
    # Check for .env file
    if not os.path.exists('.env'):
        print("❌ Error: .env file not found")
        print("💡 Copy .env.example to .env and add your GEMINI_API_KEY")
        print("   cp .env.example .env")
        sys.exit(1)
    
    # Check for Gemini API key
    from dotenv import load_dotenv
    load_dotenv()
    
    if not os.getenv('GEMINI_API_KEY') or os.getenv('GEMINI_API_KEY') == 'your_actual_api_key_here':
        print("❌ Error: GEMINI_API_KEY not configured in .env")
        print("💡 Edit .env file and add your Gemini API key")
        print("   Get one from: https://makersuite.google.com/app/apikey")
        sys.exit(1)
    
    return True

if __name__ == "__main__":
    # Check environment first
    check_environment()
    
    # Import after environment check
    import uvicorn
    from app.config import settings
    
    print("🚀 Starting YKS Genius Backend...")
    print(f"📍 API: http://localhost:8000")
    print(f"📚 Docs: http://localhost:8000/docs")
    print(f"🔌 WebSocket: ws://localhost:8000/ws/{{student_id}}")
    print()
    
    try:
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level=settings.log_level.lower()
        )
    except KeyboardInterrupt:
        print("\n👋 Shutting down server...")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        sys.exit(1)