APP_NAME="YKS Genius"
APP_VERSION="1.0.0"
DEBUG=false
ENVIRONMENT="development"

GEMINI_API_KEY=""
GEMINI_MODEL_FLASH="gemini-1.5-flash"
GEMINI_MODEL_PRO="gemini-1.5-pro"

DATABASE_URL="postgresql://yks_user:yks_password@localhost:5432/yks_genius"
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

REDIS_URL="redis://:yks_redis_password@localhost:6379/0"
USE_REDIS=true
REDIS_MAX_CONNECTIONS=50

SECRET_KEY=""
JWT_SECRET_KEY=""
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30

WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=100

MAX_DAILY_GEMINI_CALLS=1400
GEMINI_RATE_LIMIT_PER_MINUTE=30
CACHE_TTL=3600
MAX_CACHE_SIZE=1000

CHROMA_HOST="localhost"
CHROMA_PORT=8001
CHROMA_COLLECTION_NAME="yks_content"

CORS_ORIGINS='["http://localhost:5173", "http://localhost:5174", "https://yks-genius.vercel.app"]'

LOG_LEVEL="INFO"