#!/usr/bin/env python3
"""
Kötü çevirileri bulan script
İngilizce-Türkçe karışımlarını tespit eder
"""

import os
import re
from pathlib import Path

def find_bad_translations(file_path: Path):
    """Dosyadaki kötü çevirileri bul"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        bad_patterns = [
            # İngilizce kelimeler + Türkçe kelimeler karışımı
            r'\b\w+\s+(ile|ve|için|den|dan|e|a|de|da|nin|nın|nun|nün)\s+\w+',
            r'\b(for|with|and|the|of|in|on|at|to|from)\s+[A-ZÇĞıİÖŞÜ]\w*',
            r'\b[A-ZÇĞıİÖŞÜ]\w*\s+(for|with|and|the|of|in|on|at|to|from)\b',
            
            # Bel<PERSON>li kötü çeviri kalıpları
            r'<PERSON>şle\s+\w+-related',
            r'Check\s+eğer',
            r'Get\s+\w+\s+(için|ile|ve)',
            r'Create\s+new\s+\w+',
            r'Update\s+\w+\s+(profile|data)',
            r'Başlat\s+\w+\s+(client|model)',
            r'Yapılandır\s+\w+\s+(client|API)',
            r'Use\s+\w+\s+(Veritabanı|Önbellek)',
            r'Invalidate\s+Önbellek',
            r'Rate\s+Sınır',
            r'API\s+Anahtar',
            r'Demo\s+mode',
            r'Performans\s+Analitik',
            r'Study\s+Oturum',
            r'Ajan\s+interaction',
            r'Müfredat-based\s+İçerik',
            r'İçerik-related\s+requests',
            r'Öğrenci\s+management',
            r'Soru\s+generation',
            r'Multi-choice\s+questions\s+ile',
            r'Subject\s+ve\s+topic',
            r'Difficulty\s+ve\s+YKS',
            r'Semantic\s+search\s+ve',
            r'İçerik\s+similarity',
            r'Öğrenci\s+query',
            r'Knowledge\s+base',
            r'Vektör\s+Deposu\s+for',
            r'using\s+ChromaDB',
            r'Smart\s+Model\s+selection',
            r'Aggressive\s+caching',
            r'Batch\s+processing',
            r'Demo\s+mode\s+fallbacks',
            r'Shared\s+state\s+arasında',
            r'bu\s+state\s+is\s+passed',
            r'Decision\s+types\s+for\s+routing',
            r'requests\s+e\s+agents'
        ]
        
        bad_translations = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            for pattern in bad_patterns:
                matches = re.finditer(pattern, line, re.IGNORECASE)
                for match in matches:
                    bad_translations.append({
                        'file': file_path,
                        'line': i,
                        'text': line.strip(),
                        'match': match.group(),
                        'pattern': pattern
                    })
        
        return bad_translations
        
    except Exception as e:
        print(f"Hata {file_path}: {e}")
        return []

def main():
    """Ana fonksiyon"""
    app_dir = Path(__file__).parent / "app"
    
    # Python dosyalarını bul
    python_files = []
    for root, dirs, files in os.walk(app_dir):
        for file in files:
            if file.endswith('.py'):
                python_files.append(Path(root) / file)
    
    print(f"🔍 {len(python_files)} Python dosyası kontrol ediliyor...")
    
    all_bad_translations = []
    
    # Her dosyayı kontrol et
    for file_path in python_files:
        bad_translations = find_bad_translations(file_path)
        all_bad_translations.extend(bad_translations)
    
    if all_bad_translations:
        print(f"\n❌ {len(all_bad_translations)} kötü çeviri bulundu:\n")
        
        # Dosyaya göre grupla
        by_file = {}
        for bt in all_bad_translations:
            file_key = str(bt['file'].relative_to(Path(__file__).parent))
            if file_key not in by_file:
                by_file[file_key] = []
            by_file[file_key].append(bt)
        
        for file_path, translations in by_file.items():
            print(f"📁 {file_path}:")
            for bt in translations:
                print(f"  Satır {bt['line']}: {bt['match']}")
                print(f"    → {bt['text']}")
            print()
    else:
        print("✅ Kötü çeviri bulunamadı!")

if __name__ == "__main__":
    main()
