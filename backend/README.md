# YKS Genius Backend

Multi-agent exam preparation system backend built with FastAPI, LangGraph, and Google Gemini.

## Setup with venv

### 1. Create Virtual Environment
```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python3 -m venv venv

# Activate virtual environment
# On macOS/Linux:
source venv/bin/activate

# On Windows:
# venv\Scripts\activate
```

### 2. Install Dependencies
```bash
# With venv activated
pip install -r requirements.txt
```

### 3. Environment Variables
Create a `.env` file in the backend directory:
```
GEMINI_API_KEY=your_api_key_here
DATABASE_URL=sqlite:///./yks_genius.db
```

### 4. Run the Application
```bash
# With venv activated
uvicorn app.main:app --reload --port 8000
```

The API will be available at http://localhost:8000
API documentation at http://localhost:8000/docs

## Using Poetry (Alternative)

If you prefer Poetry over venv:
```bash
poetry install
poetry run uvicorn app.main:app --reload
```

## Deactivating venv

When you're done working:
```bash
deactivate
```

## Project Structure
```
backend/
├── app/
│   ├── agents/        # Multi-agent system
│   ├── api/          # REST & WebSocket endpoints
│   ├── core/         # Core utilities
│   └── models/       # Database models
├── data/             # YKS curriculum data
└── tests/            # Unit tests
```