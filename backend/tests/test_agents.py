"""
Comprehensive unit tests for YKS Genius multi-agent system
Tests individual agents, inter-agent communication, and orchestration
"""

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timezone

from app.agents.base_agent import BaseAgent, AgentRole, AgentResponse
from app.agents.strategy_agent import StrategyAgent
from app.agents.content_agent import ContentAgent
from app.agents.orchestrator import YKSAgentOrchestrator, StudentState, RouterDecision
from app.agents.communication import (
    CommunicationProtocol, MessageType, CollaborationType, AgentMessage
)

# Fixtures
@pytest.fixture
def mock_gemini_client():
    """Mock Gemini client for testing"""
    client = Mock()
    client.generate_content = AsyncMock(return_value="Mock response")
    client.model_flash = Mock()
    client.model_pro = Mock()
    return client

@pytest.fixture
def mock_vector_store():
    """Mock vector store for testing"""
    store = Mock()
    store.search = AsyncMock(return_value=[
        {"content": "Mock content", "metadata": {"topic": "test"}}
    ])
    return store

@pytest.fixture
def communication_protocol():
    """Fresh communication protocol instance"""
    return CommunicationProtocol()

# Base Agent Tests
class TestBaseAgent:
    """Test the base agent functionality"""
    
    class ConcreteAgent(BaseAgent):
        """Concrete implementation for testing"""
        async def process(self, state):
            return self.create_response(True, {"test": "data"}, "Success")
        
        def can_handle(self, request_type, state):
            return request_type == "test"
    
    def test_agent_initialization(self, mock_gemini_client):
        """Test agent initialization"""
        agent = self.ConcreteAgent(
            name="Test Agent",
            role=AgentRole.STRATEGY,
            gemini_client=mock_gemini_client,
            description="Test description"
        )
        
        assert agent.name == "Test Agent"
        assert agent.role == AgentRole.STRATEGY
        assert agent.description == "Test description"
        assert agent.gemini_client == mock_gemini_client
        assert isinstance(agent.memory, list)
        assert isinstance(agent.capabilities, list)
    
    @pytest.mark.asyncio
    async def test_create_response(self, mock_gemini_client):
        """Test response creation"""
        agent = self.ConcreteAgent("Test", AgentRole.STRATEGY, mock_gemini_client)
        
        response = agent.create_response(
            success=True,
            data={"key": "value"},
            message="Test message",
            next_action="continue",
            metadata={"extra": "info"}
        )
        
        assert response["success"] is True
        assert response["data"] == {"key": "value"}
        assert response["message"] == "Test message"
        assert response["next_action"] == "continue"
        assert response["metadata"] == {"extra": "info"}
    
    @pytest.mark.asyncio
    async def test_call_gemini(self, mock_gemini_client):
        """Test Gemini API call"""
        agent = self.ConcreteAgent("Test", AgentRole.STRATEGY, mock_gemini_client)
        
        result = await agent.call_gemini("Test prompt", "Test context")
        
        assert result == "Mock response"
        mock_gemini_client.generate_content.assert_called_once()

# Strategy Agent Tests
class TestStrategyAgent:
    """Test the Strategy Agent"""
    
    @pytest.fixture
    def strategy_agent(self, mock_gemini_client):
        return StrategyAgent(mock_gemini_client)
    
    def test_initialization(self, strategy_agent):
        """Test strategy agent initialization"""
        assert strategy_agent.name == "Strateji Uzmanı"
        assert strategy_agent.role == AgentRole.STRATEGY
        assert "study_plan_creation" in strategy_agent.capabilities
        assert "weakness_analysis" in strategy_agent.capabilities
        assert hasattr(strategy_agent, 'yks_weights')
    
    def test_can_handle_strategy_requests(self, strategy_agent):
        """Test request handling logic"""
        state = {"messages": [{"content": "Çalışma planı oluştur"}]}
        
        assert strategy_agent.can_handle("study_plan_creation", state) is True
        assert strategy_agent.can_handle("explain_concept", state) is False
        
        # Test keyword matching
        state = {"messages": [{"content": "Haftalık program hazırla"}]}
        assert strategy_agent.can_handle("", state) is True
    
    @pytest.mark.asyncio
    async def test_create_study_plan(self, strategy_agent, mock_gemini_client):
        """Test study plan creation"""
        mock_gemini_client.generate_content.return_value = """
        {
            "weekly_plan": {
                "monday": ["Matematik: 2 saat", "Fizik: 1 saat"],
                "tuesday": ["Türkçe: 2 saat", "Kimya: 1 saat"]
            },
            "focus_areas": ["Matematik", "Fizik"],
            "daily_hours": 4
        }
        """
        
        state = {
            "request_type": "create_study_plan",
            "student_data": {
                "grade": 12,
                "target_field": "SAY",
                "available_hours": 4
            }
        }
        
        response = await strategy_agent.process(state)
        
        assert response["success"] is True
        assert "data" in response
        assert response["data"] is not None

# Content Agent Tests
class TestContentAgent:
    """Test the Content Agent"""
    
    @pytest.fixture
    def content_agent(self, mock_gemini_client, mock_vector_store):
        return ContentAgent(mock_gemini_client, mock_vector_store)
    
    def test_initialization(self, content_agent):
        """Test content agent initialization"""
        assert content_agent.name == "Konu Anlatım Uzmanı"
        assert content_agent.role == AgentRole.CONTENT
        assert "explain_concept" in content_agent.capabilities
        assert hasattr(content_agent, 'subjects')
        assert hasattr(content_agent, 'content_templates')
    
    def test_can_handle_content_requests(self, content_agent):
        """Test request handling logic"""
        state = {"messages": [{"content": "Limit konusunu anlat"}]}
        
        assert content_agent.can_handle("explain_concept", state) is True
        assert content_agent.can_handle("study_plan_creation", state) is False
        
        # Test keyword matching
        state = {"messages": [{"content": "Türev nedir?"}]}
        assert content_agent.can_handle("", state) is True
    
    @pytest.mark.asyncio
    async def test_explain_concept(self, content_agent, mock_gemini_client):
        """Test concept explanation"""
        mock_gemini_client.generate_content.return_value = """
        Limit kavramı, bir fonksiyonun belirli bir noktaya yaklaşırken
        aldığı değerleri inceler. Matematiksel olarak...
        """
        
        state = {
            "request_type": "explain_concept",
            "topic": "limit",
            "subject": "matematik",
            "difficulty": "medium"
        }
        
        response = await content_agent.process(state)
        
        assert response["success"] is True
        assert "message" in response
        assert len(response["message"]) > 0

# Communication Protocol Tests
class TestCommunicationProtocol:
    """Test inter-agent communication protocol"""
    
    def test_register_agent(self, communication_protocol):
        """Test agent registration"""
        communication_protocol.register_agent(
            "TestAgent",
            ["capability1", "capability2"]
        )
        
        assert "TestAgent" in communication_protocol.agent_capabilities
        assert communication_protocol.agent_capabilities["TestAgent"] == [
            "capability1", "capability2"
        ]
    
    def test_send_message(self, communication_protocol):
        """Test message sending"""
        message = communication_protocol.send_message(
            sender="Agent1",
            recipient="Agent2",
            message_type=MessageType.REQUEST,
            content={"action": "help"},
            priority=7
        )
        
        assert message.sender == "Agent1"
        assert message.recipient == "Agent2"
        assert message.message_type == MessageType.REQUEST
        assert message.priority == 7
        assert len(communication_protocol.message_queue) == 1
        assert len(communication_protocol.message_history) == 1
    
    def test_broadcast_message(self, communication_protocol):
        """Test broadcast messaging"""
        message = communication_protocol.broadcast_message(
            sender="Agent1",
            message_type=MessageType.NOTIFICATION,
            content={"info": "System update"},
            priority=5
        )
        
        assert message.recipient == "broadcast"
        assert message.message_type == MessageType.NOTIFICATION
    
    def test_get_messages_for_agent(self, communication_protocol):
        """Test message retrieval"""
        # Send multiple messages
        communication_protocol.send_message(
            "Agent1", "Agent2", MessageType.REQUEST, {"test": 1}
        )
        communication_protocol.send_message(
            "Agent1", "Agent3", MessageType.REQUEST, {"test": 2}
        )
        communication_protocol.broadcast_message(
            "Agent1", MessageType.NOTIFICATION, {"test": 3}
        )
        
        # Get messages for Agent2
        messages = communication_protocol.get_messages_for_agent("Agent2")
        
        assert len(messages) == 2  # Direct message + broadcast
        assert messages[0].content["test"] in [1, 3]
        
        # Queue should be reduced
        assert len(communication_protocol.message_queue) == 1
    
    def test_collaboration_initiation(self, communication_protocol):
        """Test collaboration initiation"""
        communication_protocol.register_agent("Agent1", ["analyze"])
        communication_protocol.register_agent("Agent2", ["plan"])
        communication_protocol.register_agent("Agent3", ["execute"])
        
        collab_id = communication_protocol.initiate_collaboration(
            initiator="Agent1",
            participants=["Agent2", "Agent3"],
            collaboration_type=CollaborationType.SEQUENTIAL,
            context={"task": "complex_analysis"}
        )
        
        assert collab_id in communication_protocol.active_collaborations
        collab = communication_protocol.active_collaborations[collab_id]
        assert collab["initiator"] == "Agent1"
        assert collab["participants"] == ["Agent2", "Agent3"]
        assert collab["type"] == CollaborationType.SEQUENTIAL
        assert collab["status"] == "active"
    
    def test_find_capable_agents(self, communication_protocol):
        """Test capability-based agent discovery"""
        communication_protocol.register_agent("Agent1", ["analyze", "plan"])
        communication_protocol.register_agent("Agent2", ["execute", "plan"])
        communication_protocol.register_agent("Agent3", ["analyze", "report"])
        
        planners = communication_protocol.find_capable_agents("plan")
        assert set(planners) == {"Agent1", "Agent2"}
        
        analyzers = communication_protocol.find_capable_agents("analyze")
        assert set(analyzers) == {"Agent1", "Agent3"}

# Orchestrator Tests
class TestOrchestrator:
    """Test the agent orchestrator"""
    
    @pytest.fixture
    def orchestrator(self, mock_gemini_client):
        return YKSAgentOrchestrator(mock_gemini_client)
    
    @pytest.mark.asyncio
    async def test_orchestrator_initialization(self, orchestrator):
        """Test orchestrator setup"""
        assert orchestrator.gemini_client is not None
        assert hasattr(orchestrator, 'strategy_agent')
        assert hasattr(orchestrator, 'content_agent')
        assert hasattr(orchestrator, 'practice_agent')
        assert hasattr(orchestrator, 'analytics_agent')
        assert hasattr(orchestrator, 'mentor_agent')
        assert orchestrator.app is not None
    
    @pytest.mark.asyncio
    async def test_analyze_request(self, orchestrator, mock_gemini_client):
        """Test request analysis and routing"""
        mock_gemini_client.generate_content.return_value = "STRATEGY"
        
        state = StudentState(
            messages=[{"content": "Çalışma planı oluştur", "role": "user"}],
            student_id="test",
            request_type=None,
            current_topic=None,
            current_subject=None,
            weaknesses=[],
            strengths=[],
            study_plan=None,
            performance_metrics={},
            session_context={},
            next_action=None,
            agent_history=[],
            active_agent=None,
            error=None
        )
        
        decision = await orchestrator._analyze_request(
            "Çalışma planı oluştur",
            state
        )
        
        assert decision == RouterDecision.STRATEGY
    
    @pytest.mark.asyncio
    async def test_process_student_request(self, orchestrator):
        """Test end-to-end request processing"""
        with patch.object(orchestrator.app, 'ainvoke') as mock_invoke:
            mock_invoke.return_value = {
                "messages": [
                    {"content": "Test request", "role": "user"},
                    {"content": "Test response", "role": "assistant"}
                ],
                "agent_history": ["router:strategy", "strategy:completed"],
                "error": None
            }
            
            response = await orchestrator.process_student_request(
                student_id="test_student",
                message="Bana çalışma planı oluştur",
                request_type="study_plan"
            )
            
            assert response["success"] is True
            assert "message" in response
            mock_invoke.assert_called_once()
    
    def test_get_agent_stats(self, orchestrator):
        """Test statistics gathering"""
        stats = orchestrator.get_agent_stats()
        
        assert stats["total_agents"] == 5
        assert len(stats["active_agents"]) == 5
        assert "strategy" in stats["active_agents"]
        assert "content" in stats["active_agents"]
        assert "practice" in stats["active_agents"]
        assert "analytics" in stats["active_agents"]
        assert "mentor" in stats["active_agents"]

# Integration Tests
class TestIntegration:
    """Test integration between components"""
    
    @pytest.mark.asyncio
    async def test_agent_collaboration(self, mock_gemini_client):
        """Test multi-agent collaboration scenario"""
        orchestrator = YKSAgentOrchestrator(mock_gemini_client)
        
        # Mock Gemini responses
        mock_gemini_client.generate_content.side_effect = [
            "STRATEGY",  # Router decision
            '{"weaknesses": ["matematik", "fizik"], "plan": "..."}',  # Strategy response
            "CONTENT",   # Router decision for follow-up
            "Matematik konuları: ..."  # Content response
        ]
        
        # Process request requiring collaboration
        with patch.object(orchestrator.app, 'ainvoke') as mock_invoke:
            mock_invoke.return_value = {
                "messages": [
                    {"content": "Zayıf olduğum konular için plan", "role": "user"},
                    {"content": "Plan oluşturuldu", "role": "assistant"}
                ],
                "agent_history": [
                    "router:strategy",
                    "strategy:completed",
                    "router:content",
                    "content:completed"
                ],
                "error": None
            }
            
            response = await orchestrator.process_student_request(
                student_id="test_student",
                message="Zayıf olduğum konular için detaylı plan ve konu anlatımı",
                context={"requires_collaboration": True}
            )
            
            assert response["success"] is True
            assert len(response.get("agent_history", [])) >= 2

if __name__ == "__main__":
    pytest.main([__file__, "-v"])