<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YKS Genius - WebSocket Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 10px;
        }
        .subtitle {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
        }
        .agents {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        .agent {
            text-align: center;
            padding: 10px;
            margin: 5px;
            background: #f0f0f0;
            border-radius: 8px;
            flex: 1;
            min-width: 150px;
        }
        .agent-icon {
            font-size: 30px;
            margin-bottom: 5px;
        }
        .agent-name {
            font-weight: bold;
            font-size: 14px;
        }
        .chat-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            margin-bottom: 20px;
            background: #fafafa;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 8px;
            max-width: 70%;
        }
        .user-message {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        .agent-message {
            background: #e9ecef;
            color: #333;
        }
        .agent-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        input {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        button {
            padding: 12px 30px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .quick-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .quick-btn {
            padding: 8px 16px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .quick-btn:hover {
            background: #5a6268;
        }
        .status {
            text-align: center;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .connected {
            background: #d4edda;
            color: #155724;
        }
        .disconnected {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 YKS Genius</h1>
        <p class="subtitle">AI Destekli YKS Koçunuz</p>
        
        <div class="agents">
            <div class="agent">
                <div class="agent-icon">📅</div>
                <div class="agent-name">Strateji Uzmanı</div>
            </div>
            <div class="agent">
                <div class="agent-icon">📚</div>
                <div class="agent-name">Konu Anlatım</div>
            </div>
            <div class="agent">
                <div class="agent-icon">📝</div>
                <div class="agent-name">Pratik Uzmanı</div>
            </div>
            <div class="agent">
                <div class="agent-icon">📊</div>
                <div class="agent-name">Analiz Uzmanı</div>
            </div>
            <div class="agent">
                <div class="agent-icon">💪</div>
                <div class="agent-name">Motivasyon Koçu</div>
            </div>
        </div>

        <div id="status" class="status disconnected">Bağlantı bekleniyor...</div>
        
        <div class="chat-container" id="chat"></div>
        
        <div class="quick-actions">
            <button class="quick-btn" onclick="sendQuickMessage('Merhaba!')">👋 Merhaba</button>
            <button class="quick-btn" onclick="sendQuickMessage('Bana haftalık çalışma planı oluştur')">📅 Çalışma Planı</button>
            <button class="quick-btn" onclick="sendQuickMessage('Limit konusunu anlat')">📚 Konu Anlatımı</button>
            <button class="quick-btn" onclick="sendQuickMessage('Bana 5 matematik sorusu sor')">📝 Soru Sor</button>
            <button class="quick-btn" onclick="sendQuickMessage('Performansımı göster')">📊 Analiz</button>
            <button class="quick-btn" onclick="sendQuickMessage('Motivasyona ihtiyacım var')">💪 Motivasyon</button>
        </div>
        
        <div class="input-container">
            <input type="text" id="messageInput" placeholder="Mesajınızı yazın..." onkeypress="if(event.key==='Enter')sendMessage()">
            <button onclick="sendMessage()">Gönder</button>
        </div>
    </div>

    <script>
        const API_URL = 'http://localhost:8000/api/v1';
        const chatDiv = document.getElementById('chat');
        const statusDiv = document.getElementById('status');
        const messageInput = document.getElementById('messageInput');
        
        // Welcome message
        addMessage('YKS Genius\'a hoş geldiniz! Size nasıl yardımcı olabilirim? 🎯', 'agent', 'Sistem');
        
        // Update status
        updateStatus(true);
        
        function updateStatus(connected) {
            if (connected) {
                statusDiv.className = 'status connected';
                statusDiv.textContent = '✅ Bağlı - Hazır';
            } else {
                statusDiv.className = 'status disconnected';
                statusDiv.textContent = '❌ Bağlantı kesildi';
            }
        }
        
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            // Add user message
            addMessage(message, 'user');
            messageInput.value = '';
            
            try {
                // Send to API
                const response = await fetch(`${API_URL}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        student_id: 'demo'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    addMessage(data.message || data.response, 'agent', data.agent_name || 'YKS Genius');
                } else {
                    addMessage('Bir hata oluştu. Lütfen tekrar deneyin.', 'agent', 'Sistem');
                }
            } catch (error) {
                console.error('Error:', error);
                addMessage('Bağlantı hatası. Lütfen sayfayı yenileyin.', 'agent', 'Sistem');
                updateStatus(false);
            }
        }
        
        function sendQuickMessage(message) {
            messageInput.value = message;
            sendMessage();
        }
        
        function addMessage(content, type, agentName = '') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            
            if (type === 'agent' && agentName) {
                const labelDiv = document.createElement('div');
                labelDiv.className = 'agent-label';
                labelDiv.textContent = agentName;
                messageDiv.appendChild(labelDiv);
            }
            
            const textDiv = document.createElement('div');
            textDiv.textContent = content;
            messageDiv.appendChild(textDiv);
            
            chatDiv.appendChild(messageDiv);
            chatDiv.scrollTop = chatDiv.scrollHeight;
        }
    </script>
</body>
</html>