[project]
name = "yks-genius-backend"
version = "0.1.0"
description = "Multi-agent YKS exam preparation system backend"
authors = [
    {name = "YKS Genius Team"}
]
readme = "README.md"
requires-python = "^3.11"
dependencies = [
    "fastapi (>=0.116.1,<0.117.0)",
    "uvicorn[standard] (>=0.35.0,<0.36.0)",
    "langgraph (>=0.5.4,<0.6.0)",
    "langchain (>=0.3.27,<0.4.0)",
    "google-generativeai (>=0.8.5,<0.9.0)",
    "chromadb (>=1.0.15,<2.0.0)",
    "sqlalchemy (>=2.0.41,<3.0.0)",
    "pydantic (>=2.11.7,<3.0.0)",
    "websockets (>=15.0.1,<16.0.0)",
    "httpx (>=0.28.1,<0.29.0)",
    "python-multipart (>=0.0.20,<0.0.21)",
    "python-jose[cryptography] (>=3.5.0,<4.0.0)",
    "passlib[bcrypt] (>=1.7.4,<2.0.0)",
    "python-dotenv (>=1.1.1,<2.0.0)",
    "pydantic-settings (>=2.10.1,<3.0.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"
pytest-asyncio = "^1.1.0"
black = "^25.1.0"
isort = "^6.0.1"
mypy = "^1.17.0"
pylint = "^3.3.7"

