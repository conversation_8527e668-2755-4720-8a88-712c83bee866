"""
İlerleme Model for YKS Genius
Tracks detailed learning İlerleme ve Performans Analitik üzerinde Zaman
"""

from sqlalchemy import Column, String, Integer, JSON, Boolean, Text, Float, ForeignKey, DateTime, Index
from sqlalchemy.orm import relationship
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from enum import Enum

from .base import BaseModel

class ProgressType(str, Enum):
    """Types nin İlerleme tracking"""
    SUBJECT = "subject"  # Overall subject Performans
    TOPIC = "topic"  # Specific topic mastery
    SKILL = "skill"  # Cognitive skills (problem solving, etc.)
    GOAL = "goal"  # İlerleme toward specific goals

class TrendDirection(str, Enum):
    """İlerleme trend directions"""
    IMPROVING = "improving"
    STABLE = "stable"
    DECLINING = "declining"
    FLUCTUATING = "fluctuating"

class Progress(BaseModel):
    """
    İlerleme Model tracking Öğrenci learning advancement
    
    Features:
    - Granular İlerleme tracking tarafından subject/topic
    - Performans trends ve Analitik
    - Skill development measurement
    - Goal achievement tracking
    - Personalized insights ve recommendations
    """
    
    # Foreign Keys
    student_id = Column(Integer, ForeignKey('students.id'), nullable=False, index=True)
    session_id = Column(Integer, ForeignKey('studysessions.id'), nullable=True, index=True)
    
    # İlerleme Classification
    progress_type = Column(String(20), nullable=False, default=ProgressType.SUBJECT)
    subject = Column(String(50), nullable=True, index=True)  # matematik, fizik, etc.
    topic = Column(String(100), nullable=True, index=True)  # Sınır, türev, etc.
    skill = Column(String(100), nullable=True)  # problem_solving, time_management, etc.
    
    # Akım Performans Metrikler
    current_score = Column(Float, nullable=False, default=0.0)  # 0-100
    previous_score = Column(Float, nullable=True)  # For comparison
    improvement_rate = Column(Float, nullable=True)  # Percentage change
    
    # Detailed Performans Data
    performance_data = Column(JSON, nullable=True, default=dict)
    # Structure: {
    # "accuracy": 75.5,
    # "Hız": 85.2,  # Questions per minute
    # "consistency": 78.8,  # Variance de Performans
    # "difficulty_handling": {"kolay": 95, "orta": 75, "zor": 45},
    # "question_types": {"calculation": 80, "conceptual": 70, "application": 65},
    # "recent_attempts": [85, 78, 82, 90, 76]  # Last 5 attempts
    # }
    
    # Learning Analitik
    mastery_level = Column(Float, default=0.0)  # 0-100, overall mastery
    confidence_level = Column(Float, default=0.0)  # 0-100, Öğrenci confidence
    retention_rate = Column(Float, default=0.0)  # 0-100, knowledge retention
    
    # Zaman-based Metrikler
    study_time_minutes = Column(Integer, default=0)  # Zaman spent üzerinde bu subject/topic
    questions_attempted = Column(Integer, default=0)
    questions_correct = Column(Integer, default=0)
    last_practiced = Column(DateTime(timezone=True), nullable=True)
    
    # Trend Analysis
    trend_direction = Column(String(20), nullable=True)  # improving, stable, declining
    trend_strength = Column(Float, nullable=True)  # 0-1, nasıl strong the trend is
    trend_data = Column(JSON, nullable=True, default=list)  # Historical data points
    
    # Goals ve Targets
    target_score = Column(Float, nullable=True)  # Öğrenci's target for bu Alan
    target_date = Column(DateTime(timezone=True), nullable=True)  # ne zaman e achieve target
    milestones = Column(JSON, nullable=True, default=list)  # Achievement milestones
    
    # Recommendations ve Insights
    strengths = Column(JSON, nullable=True, default=list)  # Identified strengths
    weaknesses = Column(JSON, nullable=True, default=list)  # Areas for improvement
    recommendations = Column(JSON, nullable=True, default=list)  # Study recommendations
    
    # Metadata
    data_points = Column(Integer, default=1)  # Sayı nin assessments
    reliability_score = Column(Float, default=0.0)  # nasıl reliable bu data is
    last_updated = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    
    # Relationships
    student = relationship("Student", back_populates="progress_records")
    session = relationship("StudySession", back_populates="progress_records")
    
    # Indexes for Performans
    __table_args__ = (
        Index('idx_student_subject', 'student_id', 'subject'),
        Index('idx_student_topic', 'student_id', 'topic'),
        Index('idx_mastery_level', 'mastery_level'),
        Index('idx_last_practiced', 'last_practiced'),
    )
    
    def __init__(self, **kwargs):
        """Başlat İlerleme ile Varsayılan values"""
        super().__init__(**kwargs)
        
        if not self.performance_data:
            self.performance_data = {}
        if not self.trend_data:
            self.trend_data = []
        if not self.milestones:
            self.milestones = []
        if not self.strengths:
            self.strengths = []
        if not self.weaknesses:
            self.weaknesses = []
        if not self.recommendations:
            self.recommendations = []
    
    def update_performance(self, new_score: float, session_data: Dict[str, Any] = None):
        """Güncelle Performans Metrikler ile new data"""
        self.previous_score = self.current_score
        self.current_score = new_score
        
        # Hesapla improvement rate
        if self.previous_score is not None and self.previous_score > 0:
            self.improvement_rate = ((new_score - self.previous_score) / self.previous_score) * 100
        
        # Güncelle trend data
        self.trend_data.append({
            "score": new_score,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data_point": self.data_points + 1
        })
        
        # Keep only last 20 data points for Performans
        if len(self.trend_data) > 20:
            self.trend_data = self.trend_data[-20:]
        
        self.data_points += 1
        self.last_updated = datetime.now(timezone.utc)
        self.last_practiced = datetime.now(timezone.utc)
        
        # Güncelle Performans data ile Oturum Bilgi
        if session_data:
            self.update_performance_data(session_data)
        
        # Analyze trends
        self.analyze_trends()
        
        # Güncelle mastery level
        self.calculate_mastery_level()
        
        # Güncelle reliability score
        self.calculate_reliability_score()
    
    def update_performance_data(self, session_data: Dict[str, Any]):
        """Güncelle detailed Performans Metrikler"""
        accuracy = session_data.get("accuracy", 0)
        questions_attempted = session_data.get("questions_attempted", 0)
        questions_correct = session_data.get("questions_correct", 0)
        time_taken = session_data.get("time_taken", 0)
        
        # Güncelle cumulative stats
        self.questions_attempted += questions_attempted
        self.questions_correct += questions_correct
        self.study_time_minutes += time_taken // 60
        
        # Güncelle Performans data
        if "recent_attempts" not in self.performance_data:
            self.performance_data["recent_attempts"] = []
        
        self.performance_data["recent_attempts"].append(accuracy)
        if len(self.performance_data["recent_attempts"]) > 10:
            self.performance_data["recent_attempts"] = self.performance_data["recent_attempts"][-10:]
        
        # Hesapla overall accuracy
        if self.questions_attempted > 0:
            self.performance_data["accuracy"] = (self.questions_correct / self.questions_attempted) * 100
        
        # Hesapla Hız (questions per minute)
        if self.study_time_minutes > 0:
            self.performance_data["speed"] = self.questions_attempted / self.study_time_minutes
    
    def analyze_trends(self):
        """Analyze Performans trends"""
        if len(self.trend_data) < 3:
            self.trend_direction = TrendDirection.STABLE
            self.trend_strength = 0.0
            return
        
        # Get recent scores
        recent_scores = [point["score"] for point in self.trend_data[-5:]]
        
        # Hesapla trend using linear regression (simplified)
        n = len(recent_scores)
        x_values = list(range(n))
        
        # Hesapla slope
        x_mean = sum(x_values) / n
        y_mean = sum(recent_scores) / n
        
        numerator = sum((x_values[i] - x_mean) * (recent_scores[i] - y_mean) for i in range(n))
        denominator = sum((x_values[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            slope = 0
        else:
            slope = numerator / denominator
        
        # Determine trend direction ve strength
        if slope > 2:
            self.trend_direction = TrendDirection.IMPROVING
            self.trend_strength = min(1.0, abs(slope) / 10)
        elif slope < -2:
            self.trend_direction = TrendDirection.DECLINING
            self.trend_strength = min(1.0, abs(slope) / 10)
        else:
            # Check for fluctuation
            variance = sum((score - y_mean) ** 2 for score in recent_scores) / n
            if variance > 100:  # High variance
                self.trend_direction = TrendDirection.FLUCTUATING
                self.trend_strength = min(1.0, variance / 200)
            else:
                self.trend_direction = TrendDirection.STABLE
                self.trend_strength = 0.0
    
    def calculate_mastery_level(self):
        """Hesapla overall mastery level (0-100)"""
        # Base score den Akım Performans
        base_score = self.current_score * 0.4
        
        # Consistency bonus
        consistency_bonus = 0
        if self.performance_data.get("recent_attempts"):
            attempts = self.performance_data["recent_attempts"]
            if len(attempts) >= 3:
                variance = sum((x - sum(attempts)/len(attempts))**2 for x in attempts) / len(attempts)
                consistency_bonus = max(0, 20 - variance/10)  # Lower variance = higher bonus
        
        # Retention bonus (based üzerinde Zaman beri last Pratik)
        retention_bonus = 0
        if self.last_practiced:
            days_since = (datetime.now(timezone.utc) - self.last_practiced).days
            if days_since <= 1:
                retention_bonus = 20
            elif days_since <= 7:
                retention_bonus = 15
            elif days_since <= 30:
                retention_bonus = 10
            else:
                retention_bonus = 0
        
        # Hacim bonus (based üzerinde Pratik amount)
        volume_bonus = min(20, self.questions_attempted / 50 * 20)
        
        self.mastery_level = min(100, base_score + consistency_bonus + retention_bonus + volume_bonus)
    
    def calculate_reliability_score(self):
        """Hesapla nasıl reliable bu İlerleme data is"""
        reliability = 0
        
        # Data Hacim factor (more data = more reliable)
        volume_factor = min(1.0, self.data_points / 10) * 0.4
        
        # Consistency factor (consistent Performans = more reliable)
        if self.performance_data.get("recent_attempts"):
            attempts = self.performance_data["recent_attempts"]
            if len(attempts) >= 3:
                mean_score = sum(attempts) / len(attempts)
                variance = sum((x - mean_score)**2 for x in attempts) / len(attempts)
                consistency_factor = max(0, 1 - variance / 1000) * 0.3
            else:
                consistency_factor = 0.2
        else:
            consistency_factor = 0.1
        
        # Recency factor (recent data = more reliable)
        if self.last_practiced:
            days_since = (datetime.now(timezone.utc) - self.last_practiced).days
            recency_factor = max(0, 1 - days_since / 30) * 0.3
        else:
            recency_factor = 0
        
        self.reliability_score = (volume_factor + consistency_factor + recency_factor) * 100
    
    def identify_strengths_and_weaknesses(self):
        """Identify specific strengths ve areas for improvement"""
        self.strengths = []
        self.weaknesses = []
        
        performance = self.performance_data
        
        # Analyze accuracy
        accuracy = performance.get("accuracy", 0)
        if accuracy >= 80:
            self.strengths.append("High accuracy in problem solving")
        elif accuracy < 60:
            self.weaknesses.append("Accuracy needs improvement")
        
        # Analyze Hız
        speed = performance.get("speed", 0)
        if speed > 1.5:  # More than 1.5 questions per minute
            self.strengths.append("Good solving speed")
        elif speed < 0.5:
            self.weaknesses.append("Solving speed needs improvement")
        
        # Analyze consistency
        if performance.get("recent_attempts"):
            attempts = performance["recent_attempts"]
            if len(attempts) >= 3:
                variance = sum((x - sum(attempts)/len(attempts))**2 for x in attempts) / len(attempts)
                if variance < 50:
                    self.strengths.append("Consistent performance")
                elif variance > 200:
                    self.weaknesses.append("Inconsistent performance")
        
        # Analyze difficulty handling
        difficulty_data = performance.get("difficulty_handling", {})
        for difficulty, score in difficulty_data.items():
            if score >= 80:
                self.strengths.append(f"Strong in {difficulty} level questions")
            elif score < 50:
                self.weaknesses.append(f"Needs work on {difficulty} level questions")
    
    def generate_recommendations(self):
        """Üret personalized study recommendations"""
        self.recommendations = []
        
        # Based üzerinde trend
        if self.trend_direction == TrendDirection.DECLINING:
            self.recommendations.append("Review fundamental concepts before advancing")
            self.recommendations.append("Increase practice frequency")
        elif self.trend_direction == TrendDirection.FLUCTUATING:
            self.recommendations.append("Focus on consistent study schedule")
            self.recommendations.append("Practice more regularly to improve stability")
        
        # Based üzerinde mastery level
        if self.mastery_level < 30:
            self.recommendations.append("Start with easier questions to build confidence")
            self.recommendations.append("Focus on understanding basic concepts")
        elif self.mastery_level > 80:
            self.recommendations.append("Challenge yourself with harder questions")
            self.recommendations.append("Help others to reinforce your knowledge")
        
        # Based üzerinde Zaman beri last Pratik
        if self.last_practiced:
            days_since = (datetime.now(timezone.utc) - self.last_practiced).days
            if days_since > 7:
                self.recommendations.append("Review this topic soon to maintain retention")
            elif days_since > 30:
                self.recommendations.append("This topic needs immediate attention")
        
        # Based üzerinde Performans data
        performance = self.performance_data
        if performance.get("accuracy", 0) < 60:
            self.recommendations.append("Focus on accuracy over speed")
        if performance.get("speed", 0) < 0.5:
            self.recommendations.append("Practice timed exercises to improve speed")
    
    def get_progress_summary(self) -> Dict[str, Any]:
        """Get comprehensive İlerleme summary"""
        return {
            "subject": self.subject,
            "topic": self.topic,
            "current_score": round(self.current_score, 1),
            "improvement_rate": round(self.improvement_rate, 1) if self.improvement_rate else None,
            "mastery_level": round(self.mastery_level, 1),
            "trend_direction": self.trend_direction,
            "trend_strength": round(self.trend_strength, 2) if self.trend_strength else None,
            "questions_attempted": self.questions_attempted,
            "questions_correct": self.questions_correct,
            "accuracy": round((self.questions_correct / self.questions_attempted * 100), 1) if self.questions_attempted > 0 else 0,
            "study_time_hours": round(self.study_time_minutes / 60, 1),
            "last_practiced": self.last_practiced.isoformat() if self.last_practiced else None,
            "strengths": self.strengths,
            "weaknesses": self.weaknesses,
            "recommendations": self.recommendations,
            "reliability_score": round(self.reliability_score, 1)
        }
    
    def is_goal_met(self) -> bool:
        """Check eğer target score has been achieved"""
        return self.target_score and self.current_score >= self.target_score
    
    def days_until_target(self) -> Optional[int]:
        """Hesapla days kadar target Tarih"""
        if not self.target_date:
            return None
        
        delta = self.target_date - datetime.now(timezone.utc)
        return max(0, delta.days)
    
    def estimated_time_to_mastery(self) -> Optional[int]:
        """Estimate days needed e reach mastery (80+ score)"""
        if self.mastery_level >= 80:
            return 0
        
        if not self.improvement_rate or self.improvement_rate <= 0:
            return None
        
        # Simple linear projection
        points_needed = 80 - self.mastery_level
        days_needed = points_needed / (self.improvement_rate / 7)  # Assuming weekly improvement rate
        
        return max(1, int(days_needed))
    
    @classmethod
    def get_student_progress_overview(cls, session, student_id: int) -> Dict[str, Any]:
        """Get overall İlerleme overview for a Öğrenci"""
        progress_records = session.query(cls).filter(cls.student_id == student_id).all()
        
        if not progress_records:
            return {"message": "No progress data available"}
        
        # Hesapla overall Metrikler
        avg_mastery = sum(p.mastery_level for p in progress_records) / len(progress_records)
        improving_subjects = len([p for p in progress_records if p.trend_direction == TrendDirection.IMPROVING])
        total_study_time = sum(p.study_time_minutes for p in progress_records)
        total_questions = sum(p.questions_attempted for p in progress_records)
        
        return {
            "total_subjects_tracked": len(progress_records),
            "average_mastery_level": round(avg_mastery, 1),
            "improving_subjects_count": improving_subjects,
            "total_study_hours": round(total_study_time / 60, 1),
            "total_questions_attempted": total_questions,
            "subjects_needing_attention": [
                {"subject": p.subject, "topic": p.topic, "mastery": p.mastery_level}
                for p in progress_records 
                if p.mastery_level < 50
            ]
        }
    
    def __repr__(self):
        return f"<Progress(id={self.id}, student_id={self.student_id}, subject='{self.subject}', topic='{self.topic}', score={self.current_score})>"