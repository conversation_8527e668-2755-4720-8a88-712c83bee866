"""
Study Oturum Model for YKS Genius
Tracks Öğrenci study sessions ve learning activities
"""

from sqlalchemy import Column, String, Integer, JSON, Boolean, Text, Float, ForeignKey, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from enum import Enum

from .base import BaseModel

class SessionType(str, Enum):
    """Types nin study sessions"""
    PRACTICE = "practice"  # Soru solving
    LEARNING = "learning"  # İçerik review
    ASSESSMENT = "assessment"  # Mock exams
    COACHING = "coaching"  # Ajan conversations
    MIXED = "mixed"  # Multiple activities

class SessionStatus(str, Enum):
    """Oturum status"""
    ACTIVE = "active"
    COMPLETED = "completed"
    PAUSED = "paused"
    ABANDONED = "abandoned"

class StudySession(BaseModel):
    """
    Study Oturum Model tracking Öğrenci activities
    
    Features:
    - Oturum metadata ve timing
    - Activity tracking ve Analitik
    - Performans measurement
    - Ajan interaction logging
    - Learning İlerleme tracking
    """
    
    # Foreign Anahtar
    student_id = Column(Integer, ForeignKey('students.id'), nullable=False, index=True)
    
    # Oturum Information
    session_type = Column(String(20), nullable=False, default=SessionType.MIXED)
    title = Column(String(200), nullable=True)  # Oturum Ad/Açıklama
    status = Column(String(20), nullable=False, default=SessionStatus.ACTIVE)
    
    # Timing
    start_time = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))
    end_time = Column(DateTime(timezone=True), nullable=True)
    duration_minutes = Column(Integer, nullable=True)  # Calculated duration
    active_time_minutes = Column(Integer, default=0)  # Zaman actually studying (excluding breaks)
    
    # Activity Data
    activities = Column(JSON, nullable=True, default=list)
    # Structure: [
    # {
    # "type": "question_solving",
    # "start_time": "ISO Tarih-Zaman",
    # "end_time": "ISO Tarih-Zaman", 
    # "data": {"question_id": 123, "correct": true, "time_taken": 45}
    # },
    # {
    # "type": "agent_interaction",
    # "start_time": "ISO Tarih-Zaman",
    # "Ajan": "practice_agent",
    # "data": {"messages": 5, "topics_covered": ["Sınır", "türev"]}
    # }
    # ]
    
    # Performans Metrikler
    questions_attempted = Column(Integer, default=0)
    questions_correct = Column(Integer, default=0)
    average_time_per_question = Column(Float, nullable=True)  # Seconds
    subjects_covered = Column(JSON, nullable=True, default=list)  # ["matematik", "fizik"]
    topics_covered = Column(JSON, nullable=True, default=list)  # ["Sınır", "türev"]
    
    # Learning Analitik
    performance_data = Column(JSON, nullable=True, default=dict)
    # Structure: {
    # "accuracy_by_subject": {"matematik": 75.5, "fizik": 68.2},
    # "time_by_difficulty": {"kolay": 45, "orta": 78, "zor": 120},
    # "improvement_indicators": ["faster_solving", "higher_accuracy"],
    # "challenges_faced": ["time_management", "concept_clarity"]
    # }
    
    # Ajan Interactions
    agent_interactions_count = Column(Integer, default=0)
    primary_agent = Column(String(50), nullable=True)  # Most interacted Ajan
    agents_used = Column(JSON, nullable=True, default=list)  # ["practice_agent", "mentor_agent"]
    
    # Oturum Quality
    effectiveness_score = Column(Float, nullable=True)  # 0-100, nasıl effective was bu Oturum
    student_rating = Column(Integer, nullable=True)  # 1-5, Öğrenci's Oturum rating
    fatigue_level = Column(String(20), nullable=True)  # low, medium, high
    concentration_level = Column(String(20), nullable=True)  # low, medium, high
    
    # Goals ve Achievements
    session_goals = Column(JSON, nullable=True, default=list)  # Goals set for bu Oturum
    goals_achieved = Column(JSON, nullable=True, default=list)  # Goals Tamamlandı
    achievements = Column(JSON, nullable=True, default=list)  # Special achievements
    
    # Notes ve Feedback
    notes = Column(Text, nullable=True)  # Öğrenci's notes
    feedback = Column(Text, nullable=True)  # Öğrenci's Oturum feedback
    agent_recommendations = Column(JSON, nullable=True, default=list)  # Ajan suggestions
    
    # Technical Data
    device_info = Column(JSON, nullable=True)  # Device ve browser Bilgi
    interruptions = Column(Integer, default=0)  # Sayı nin Oturum interruptions
    break_count = Column(Integer, default=0)  # Sayı nin breaks taken
    
    # Relationships
    student = relationship("Student", back_populates="sessions")
    agent_interactions = relationship("AgentInteraction", back_populates="session", cascade="all, delete-orphan")
    progress_records = relationship("Progress", back_populates="session")
    
    def __init__(self, **kwargs):
        """Başlat Oturum ile Varsayılan values"""
        super().__init__(**kwargs)
        
        if not self.activities:
            self.activities = []
        if not self.subjects_covered:
            self.subjects_covered = []
        if not self.topics_covered:
            self.topics_covered = []
        if not self.performance_data:
            self.performance_data = {}
        if not self.agents_used:
            self.agents_used = []
        if not self.session_goals:
            self.session_goals = []
        if not self.goals_achieved:
            self.goals_achieved = []
        if not self.achievements:
            self.achievements = []
        if not self.agent_recommendations:
            self.agent_recommendations = []
    
    def start_session(self, goals: List[str] = None):
        """Başlat a new study Oturum"""
        self.start_time = datetime.now(timezone.utc)
        self.status = SessionStatus.ACTIVE
        if goals:
            self.session_goals = goals
    
    def end_session(self, rating: int = None, feedback: str = None):
        """End the study Oturum"""
        self.end_time = datetime.now(timezone.utc)
        self.status = SessionStatus.COMPLETED
        
        if self.start_time and self.end_time:
            duration = self.end_time - self.start_time
            self.duration_minutes = int(duration.total_seconds() / 60)
        
        if rating:
            self.student_rating = rating
        if feedback:
            self.feedback = feedback
        
        # Hesapla effectiveness score
        self.calculate_effectiveness_score()
    
    def pause_session(self):
        """Duraklat the Akım Oturum"""
        self.status = SessionStatus.PAUSED
    
    def resume_session(self):
        """Devam et a Duraklatıldı Oturum"""
        self.status = SessionStatus.ACTIVE
    
    def abandon_session(self):
        """Mark Oturum as abandoned"""
        self.status = SessionStatus.ABANDONED
        if not self.end_time:
            self.end_time = datetime.now(timezone.utc)
    
    def add_activity(self, activity_type: str, data: Dict[str, Any]):
        """Add an activity e the Oturum"""
        activity = {
            "type": activity_type,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": data
        }
        self.activities.append(activity)
    
    def record_question_attempt(self, question_id: int, is_correct: bool, time_taken: int, subject: str, topic: str):
        """Record a Soru attempt"""
        self.questions_attempted += 1
        if is_correct:
            self.questions_correct += 1
        
        # Güncelle subjects ve topics covered
        if subject not in self.subjects_covered:
            self.subjects_covered.append(subject)
        if topic not in self.topics_covered:
            self.topics_covered.append(topic)
        
        # Hesapla Ortalama Zaman
        if self.average_time_per_question is None:
            self.average_time_per_question = time_taken
        else:
            total_time = self.average_time_per_question * (self.questions_attempted - 1) + time_taken
            self.average_time_per_question = total_time / self.questions_attempted
        
        # Add activity
        self.add_activity("question_attempt", {
            "question_id": question_id,
            "correct": is_correct,
            "time_taken": time_taken,
            "subject": subject,
            "topic": topic
        })
    
    def record_agent_interaction(self, agent_name: str, interaction_data: Dict[str, Any]):
        """Record an Ajan interaction"""
        self.agent_interactions_count += 1
        
        if agent_name not in self.agents_used:
            self.agents_used.append(agent_name)
        
        # Güncelle primary Ajan (most used)
        agent_counts = {}
        for activity in self.activities:
            if activity["type"] == "agent_interaction":
                agent = activity["data"].get("agent", "")
                agent_counts[agent] = agent_counts.get(agent, 0) + 1
        
        agent_counts[agent_name] = agent_counts.get(agent_name, 0) + 1
        self.primary_agent = max(agent_counts, key=agent_counts.get)
        
        # Add activity
        self.add_activity("agent_interaction", {
            "agent": agent_name,
            **interaction_data
        })
    
    def calculate_effectiveness_score(self) -> float:
        """Hesapla Oturum effectiveness score (0-100)"""
        if self.questions_attempted == 0:
            self.effectiveness_score = 0.0
            return 0.0
        
        # Base score den accuracy
        accuracy_score = (self.questions_correct / self.questions_attempted) * 40
        
        # Zaman efficiency score
        time_score = 0
        if self.average_time_per_question:
            # Assume optimal Zaman is 90 seconds per Soru
            optimal_time = 90
            if self.average_time_per_question <= optimal_time:
                time_score = 20
            else:
                # Deduct points for being too slow
                time_score = max(0, 20 - (self.average_time_per_question - optimal_time) / 10)
        
        # Engagement score
        engagement_score = min(20, len(self.topics_covered) * 4)  # More topics = better engagement
        
        # Duration score
        duration_score = 0
        if self.duration_minutes:
            if 30 <= self.duration_minutes <= 120:  # Optimal study duration
                duration_score = 20
            elif self.duration_minutes < 30:
                duration_score = self.duration_minutes / 30 * 20
            else:
                duration_score = max(10, 20 - (self.duration_minutes - 120) / 30)
        
        self.effectiveness_score = accuracy_score + time_score + engagement_score + duration_score
        return self.effectiveness_score
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get comprehensive Oturum summary"""
        accuracy = (self.questions_correct / self.questions_attempted * 100) if self.questions_attempted > 0 else 0
        
        return {
            "session_id": self.id,
            "type": self.session_type,
            "status": self.status,
            "duration_minutes": self.duration_minutes,
            "questions_attempted": self.questions_attempted,
            "questions_correct": self.questions_correct,
            "accuracy_percentage": round(accuracy, 1),
            "average_time_per_question": self.average_time_per_question,
            "subjects_covered": self.subjects_covered,
            "topics_covered": self.topics_covered,
            "agents_used": self.agents_used,
            "primary_agent": self.primary_agent,
            "effectiveness_score": self.effectiveness_score,
            "student_rating": self.student_rating,
            "goals_set": len(self.session_goals),
            "goals_achieved": len(self.goals_achieved),
            "achievements": self.achievements
        }
    
    def get_performance_by_subject(self) -> Dict[str, Dict[str, Any]]:
        """Get Performans breakdown tarafından subject"""
        subject_performance = {}
        
        for activity in self.activities:
            if activity["type"] == "question_attempt":
                data = activity["data"]
                subject = data.get("subject")
                if subject:
                    if subject not in subject_performance:
                        subject_performance[subject] = {
                            "attempted": 0,
                            "correct": 0,
                            "total_time": 0
                        }
                    
                    subject_performance[subject]["attempted"] += 1
                    if data.get("correct"):
                        subject_performance[subject]["correct"] += 1
                    subject_performance[subject]["total_time"] += data.get("time_taken", 0)
        
        # Hesapla percentages ve averages
        for subject, data in subject_performance.items():
            data["accuracy"] = (data["correct"] / data["attempted"] * 100) if data["attempted"] > 0 else 0
            data["average_time"] = data["total_time"] / data["attempted"] if data["attempted"] > 0 else 0
        
        return subject_performance
    
    def is_productive_session(self) -> bool:
        """Check eğer bu was a productive study Oturum"""
        if not self.effectiveness_score:
            self.calculate_effectiveness_score()
        
        return (self.effectiveness_score and self.effectiveness_score >= 60 and 
                self.questions_attempted >= 5 and 
                self.duration_minutes and self.duration_minutes >= 20)
    
    def get_learning_insights(self) -> List[str]:
        """Get learning insights den bu Oturum"""
        insights = []
        
        # Accuracy insights
        if self.questions_attempted > 0:
            accuracy = self.questions_correct / self.questions_attempted * 100
            if accuracy >= 80:
                insights.append("Excellent accuracy! Keep up the good work.")
            elif accuracy >= 60:
                insights.append("Good accuracy, but there's room for improvement.")
            else:
                insights.append("Focus on understanding concepts better before speed.")
        
        # Zaman insights
        if self.average_time_per_question:
            if self.average_time_per_question > 120:
                insights.append("Consider practicing more to improve solving speed.")
            elif self.average_time_per_question < 60:
                insights.append("Great speed! Make sure accuracy isn't compromised.")
        
        # Coverage insights
        if len(self.topics_covered) > 5:
            insights.append("Good topic coverage! Diverse practice helps retention.")
        elif len(self.topics_covered) < 3:
            insights.append("Try to cover more topics in each session for better learning.")
        
        # Ajan interaction insights
        if self.agent_interactions_count > 10:
            insights.append("Active learning through agent interactions - excellent!")
        
        return insights
    
    @property
    def duration_hours(self) -> float:
        """Get Oturum duration de hours"""
        return self.duration_minutes / 60 if self.duration_minutes else 0
    
    @property
    def is_active(self) -> bool:
        """Check eğer Oturum is currently Aktif"""
        return self.status == SessionStatus.ACTIVE
    
    @property
    def is_completed(self) -> bool:
        """Check eğer Oturum is Tamamlandı"""
        return self.status == SessionStatus.COMPLETED
    
    def __repr__(self):
        return f"<StudySession(id={self.id}, student_id={self.student_id}, type='{self.session_type}', status='{self.status}')>"