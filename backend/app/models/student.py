"""
Student model for YKS Genius
Stores student profiles, preferences, and basic information
"""

from sqlalchemy import Column, String, Integer, JSO<PERSON>, <PERSON>olean, Text, Float
from sqlalchemy.orm import relationship
from typing import Dict, Any, Optional, List

from .base import BaseModel

class Student(BaseModel):
    """
    Student model representing YKS candidates
    
    Fields:
    - Basic info: name, email, grade_level
    - Goals: target_university, target_department, target_score
    - Preferences: study_preferences (JSON)
    - Status: is_active, current_level
    """
    
    # Basic Information
    name = Column(String(100), nullable=False, index=True)
    email = Column(String(150), unique=True, nullable=False, index=True)
    phone = Column(String(20), nullable=True)
    grade_level = Column(Integer, nullable=False)  # 9, 10, 11, 12
    
    # Academic Goals
    target_university = Column(String(200), nullable=True)
    target_department = Column(String(200), nullable=True)
    target_score = Column(Integer, nullable=True)  # Target YKS score
    exam_year = Column(Integer, nullable=True)  # Which year planning to take YKS
    
    # Study Preferences (JSON field)
    study_preferences = Column(JSON, nullable=True, default=dict)
    # Structure: {
    #   "preferred_study_time": "morning|afternoon|evening|night",
    #   "learning_style": "visual|auditory|kinesthetic|mixed", 
    #   "difficulty_preference": "easy|medium|hard|adaptive",
    #   "subjects_of_interest": ["matematik", "fizik", ...],
    #   "daily_study_hours": 4,
    #   "break_intervals": 25,  # Pomodoro style
    #   "motivational_style": "encouraging|challenging|analytical"
    # }
    
    # Performance Tracking
    current_level = Column(String(20), default="beginner")  # beginner, intermediate, advanced
    overall_progress = Column(Float, default=0.0)  # 0.0 to 100.0
    study_streak = Column(Integer, default=0)  # Consecutive study days
    total_questions_solved = Column(Integer, default=0)
    total_study_time = Column(Integer, default=0)  # In minutes
    
    # Subject Performance (JSON)
    subject_scores = Column(JSON, nullable=True, default=dict)
    # Structure: {
    #   "matematik": {"accuracy": 75.5, "questions_solved": 150, "last_score": 85},
    #   "fizik": {"accuracy": 68.2, "questions_solved": 120, "last_score": 72},
    #   ...
    # }
    
    # Preferences and Settings
    notification_preferences = Column(JSON, nullable=True, default=dict)
    # Structure: {
    #   "email_notifications": true,
    #   "daily_reminders": true,
    #   "weekly_reports": true,
    #   "achievement_alerts": true
    # }
    
    # Status
    is_active = Column(Boolean, default=True)
    last_login = Column(String(50), nullable=True)  # ISO datetime string
    subscription_type = Column(String(20), default="free")  # free, premium, pro
    
    # Personal Notes
    notes = Column(Text, nullable=True)
    goals_description = Column(Text, nullable=True)
    
    # Relationships
    sessions = relationship("StudySession", back_populates="student", cascade="all, delete-orphan")
    progress_records = relationship("Progress", back_populates="student", cascade="all, delete-orphan")
    agent_interactions = relationship("AgentInteraction", back_populates="student", cascade="all, delete-orphan")
    
    def __init__(self, **kwargs):
        """Initialize student with default preferences"""
        super().__init__(**kwargs)
        
        # Set default study preferences if not provided
        if not self.study_preferences:
            self.study_preferences = {
                "preferred_study_time": "evening",
                "learning_style": "mixed",
                "difficulty_preference": "adaptive",
                "subjects_of_interest": [],
                "daily_study_hours": 4,
                "break_intervals": 25,
                "motivational_style": "encouraging"
            }
        
        # Set default notification preferences
        if not self.notification_preferences:
            self.notification_preferences = {
                "email_notifications": True,
                "daily_reminders": True,
                "weekly_reports": True,
                "achievement_alerts": True
            }
        
        # Initialize subject scores
        if not self.subject_scores:
            self.subject_scores = {}
    
    def get_subject_performance(self, subject: str) -> Dict[str, Any]:
        """Get performance data for a specific subject"""
        return self.subject_scores.get(subject, {
            "accuracy": 0.0,
            "questions_solved": 0,
            "last_score": 0,
            "improvement_rate": 0.0
        })
    
    def update_subject_performance(self, subject: str, accuracy: float, questions_count: int, score: int):
        """Update performance data for a subject"""
        if not self.subject_scores:
            self.subject_scores = {}
        
        current = self.subject_scores.get(subject, {
            "accuracy": 0.0,
            "questions_solved": 0,
            "last_score": 0,
            "improvement_rate": 0.0
        })
        
        # Calculate improvement rate
        if current["last_score"] > 0:
            improvement = ((score - current["last_score"]) / current["last_score"]) * 100
        else:
            improvement = 0.0
        
        self.subject_scores[subject] = {
            "accuracy": accuracy,
            "questions_solved": current["questions_solved"] + questions_count,
            "last_score": score,
            "improvement_rate": improvement
        }
    
    def get_weak_subjects(self, threshold: float = 60.0) -> List[str]:
        """Get subjects where student is performing below threshold"""
        weak_subjects = []
        for subject, data in self.subject_scores.items():
            if data.get("accuracy", 0) < threshold:
                weak_subjects.append(subject)
        return weak_subjects
    
    def get_strong_subjects(self, threshold: float = 80.0) -> List[str]:
        """Get subjects where student is performing above threshold"""
        strong_subjects = []
        for subject, data in self.subject_scores.items():
            if data.get("accuracy", 0) >= threshold:
                strong_subjects.append(subject)
        return strong_subjects
    
    def calculate_overall_performance(self) -> float:
        """Calculate overall performance across all subjects"""
        if not self.subject_scores:
            return 0.0
        
        total_accuracy = sum(data.get("accuracy", 0) for data in self.subject_scores.values())
        return total_accuracy / len(self.subject_scores)
    
    def get_study_statistics(self) -> Dict[str, Any]:
        """Get comprehensive study statistics"""
        return {
            "total_questions_solved": self.total_questions_solved,
            "total_study_time_hours": self.total_study_time / 60,
            "study_streak": self.study_streak,
            "overall_progress": self.overall_progress,
            "current_level": self.current_level,
            "weak_subjects": self.get_weak_subjects(),
            "strong_subjects": self.get_strong_subjects(),
            "overall_performance": self.calculate_overall_performance()
        }
    
    def is_goal_oriented(self) -> bool:
        """Check if student has set clear goals"""
        return bool(self.target_university and self.target_department)
    
    def get_learning_profile(self) -> Dict[str, Any]:
        """Get student's learning profile for agent personalization"""
        return {
            "learning_style": self.study_preferences.get("learning_style", "mixed"),
            "difficulty_preference": self.study_preferences.get("difficulty_preference", "adaptive"),
            "motivational_style": self.study_preferences.get("motivational_style", "encouraging"),
            "subjects_of_interest": self.study_preferences.get("subjects_of_interest", []),
            "grade_level": self.grade_level,
            "current_level": self.current_level,
            "goals": {
                "university": self.target_university,
                "department": self.target_department,
                "score": self.target_score
            }
        }
    
    def __repr__(self):
        return f"<Student(id={self.id}, name='{self.name}', email='{self.email}', grade={self.grade_level})>"