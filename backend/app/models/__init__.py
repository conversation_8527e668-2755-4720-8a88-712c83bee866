"""
YKS Genius Veritabanı Modelleri
PostgreSQL veritabanı için SQLAlchemy ORM modelleri
"""

from .base import BaseModel
from .student import Student
from .question import Question, QuestionDifficulty, YKSType, QuestionStatus
from .session import StudySession, SessionType, SessionStatus
from .progress import Progress, ProgressType, TrendDirection
from .agent_interaction import AgentInteraction, InteractionType, AgentRole, InteractionStatus

# Kolay import için tüm modelleri dışa aktar
__all__ = [
    # Te<PERSON>
    "BaseModel",

    # <PERSON><PERSON><PERSON><PERSON>
    "Student",

    # <PERSON><PERSON>
    "Question",
    "QuestionDifficulty",
    "YKSType",
    "QuestionStatus",

    # O<PERSON>um
    "StudySession",
    "SessionType",
    "SessionStatus",

    # <PERSON><PERSON><PERSON>e
    "Progress",
    "ProgressType",
    "TrendDirection",

    # <PERSON><PERSON>
    "AgentInteraction",
    "InteractionType",
    "AgentRole",
    "InteractionStatus"
]