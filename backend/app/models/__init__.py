"""
YKS Genius Veritabanı Modelleri
PostgreSQL SQLAlchemy için veritabanı ORM modelleri
"""

from .base import BaseModel
from .student import Student
from .question import Question, QuestionDifficulty, YKSType, QuestionStatus
from .session import StudySession, SessionType, SessionStatus
from .progress import Progress, ProgressType, TrendDirection
from .agent_interaction import AgentInteraction, InteractionType, AgentRole, InteractionStatus

# Kolay İçe tüm için aktar modelleri dışa aktar
__all__ = [
    # Temel
    "BaseModel",

    # <PERSON><PERSON><PERSON><PERSON>
    "Student",

    # <PERSON>ru
    "Question",
    "QuestionDifficulty",
    "YKSType",
    "QuestionStatus",

    # Oturum
    "StudySession",
    "SessionType",
    "SessionStatus",

    # İlerleme
    "Progress",
    "ProgressType",
    "TrendDirection",

    # <PERSON><PERSON>
    "AgentInteraction",
    "InteractionType",
    "AgentRole",
    "InteractionStatus"
]