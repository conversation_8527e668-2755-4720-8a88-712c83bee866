"""
YKS Genius Database Models
SQLAlchemy ORM models for PostgreSQL database
"""

from .base import BaseModel
from .student import Student
from .question import Question, QuestionDifficulty, YKSType, QuestionStatus
from .session import StudySession, SessionType, SessionStatus
from .progress import Progress, ProgressType, TrendDirection
from .agent_interaction import AgentInteraction, InteractionType, AgentRole, InteractionStatus

# Export all models for easy import
__all__ = [
    # Base
    "BaseModel",
    
    # Student
    "Student",
    
    # Question
    "Question",
    "QuestionDifficulty", 
    "YKSType",
    "QuestionStatus",
    
    # Session
    "StudySession",
    "SessionType",
    "SessionStatus", 
    
    # Progress
    "Progress",
    "ProgressType",
    "TrendDirection",
    
    # Agent Interaction
    "AgentInteraction",
    "InteractionType",
    "AgentRole",
    "InteractionStatus"
]