"""
Ortak alanlar ve yardımcı fonksiyonlar içeren temel Model sınıfı
"""

from sqlalchemy import Column, Integer, DateTime, String
from sqlalchemy.ext.declarative import declared_attr
from datetime import datetime, timezone
import uuid

from ..database import Base

class BaseModel(Base):
    """
    Ortak alanları olan soyut temel Model
    Tüm modeller bu sınıftan miras alır
    """
    __abstract__ = True

    id = Column(Integer, primary_key=True, index=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    @declared_attr
    def __tablename__(cls):
        """Sınıf adından tablo adı oluştur"""
        return cls.__name__.lower() + 's'

    def to_dict(self) -> dict:
        """Model örneğini sözlüğe dönüştür"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }

    def update_from_dict(self, data: dict):
        """Model örneğini sözlükten güncelle"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
        self.updated_at = datetime.now(timezone.utc)

    @classmethod
    def get_table_name(cls) -> str:
        """Bu Model için tablo adını al"""
        return cls.__tablename__

    def __repr__(self):
        return f"<{self.__class__.__name__}(id={self.id})>"