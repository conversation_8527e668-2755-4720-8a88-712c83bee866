"""
Student Answer model for YKS Genius
Tracks all student answers and provides analytics data
"""

from sqlalchemy import Column, String, Integer, Float, Boolean, Text, DateTime, ForeignKey, Index
from sqlalchemy.orm import relationship
from typing import Dict, Any, Optional, List
from enum import Enum
from datetime import datetime

from .base import BaseModel

class AnswerStatus(str, Enum):
    """Answer submission status"""
    SUBMITTED = "submitted"
    GRADED = "graded"
    REVIEWED = "reviewed"

class StudentAnswer(BaseModel):
    """
    Student Answer model for tracking all question attempts

    Features:
    - Records all student answers with timestamps
    - Tracks correctness and time spent
    - Provides scoring and performance metrics
    - Links to questions and students for analytics
    - Supports session-based tracking
    """

    # Student and Question references
    student_id = Column(String(50), nullable=False, index=True)  # Student identifier
    question_id = Column(String(50), nullable=False, index=True)  # Question identifier
    session_id = Column(String(50), nullable=True, index=True)  # Session grouping

    # Answer details
    student_answer = Column(String(10), nullable=False)  # A, B, C, D, E or text answer
    correct_answer = Column(String(10), nullable=False)  # Expected answer
    is_correct = Column(<PERSON><PERSON><PERSON>, nullable=False, index=True)  # Whether answer was correct

    # Performance metrics
    time_spent = Column(Integer, nullable=False)  # Time in seconds
    score = Column(Float, nullable=False)  # Points earned (0-120)
    difficulty_score = Column(Float, nullable=True)  # Question difficulty (0-1)

    # Question metadata (cached for analytics)
    subject = Column(String(50), nullable=True, index=True)  # matematik, fizik, etc.
    topic = Column(String(100), nullable=True, index=True)  # specific topic
    difficulty = Column(String(20), nullable=True, index=True)  # kolay, orta, zor
    yks_type = Column(String(10), nullable=True, index=True)  # TYT, AYT, MSU

    # Submission details
    status = Column(String(20), default=AnswerStatus.SUBMITTED)  # Answer status
    submitted_at = Column(DateTime, default=datetime.utcnow, index=True)
    graded_at = Column(DateTime, nullable=True)

    # Performance analysis
    time_performance = Column(String(20), nullable=True)  # ideal, fast, slow
    efficiency_score = Column(Float, nullable=True)  # Time efficiency percentage

    # Additional context
    hints_used = Column(Integer, default=0)  # Number of hints used
    attempts_count = Column(Integer, default=1)  # Number of attempts for this question
    confidence_level = Column(String(20), nullable=True)  # Student's confidence level

    # Feedback and notes
    ai_feedback = Column(Text, nullable=True)  # AI-generated feedback
    explanation_viewed = Column(Boolean, default=False)  # Whether student viewed explanation
    notes = Column(Text, nullable=True)  # Any additional notes

    # Analytics flags
    is_review_session = Column(Boolean, default=False)  # Part of review session
    is_practice_session = Column(Boolean, default=False)  # Part of practice session
    is_test_session = Column(Boolean, default=False)  # Part of test session

    # Indexes for performance
    __table_args__ = (
        Index('idx_student_subject', 'student_id', 'subject'),
        Index('idx_student_topic', 'student_id', 'topic'),
        Index('idx_session_performance', 'session_id', 'is_correct'),
        Index('idx_time_performance', 'submitted_at', 'is_correct'),
        Index('idx_analytics', 'student_id', 'submitted_at', 'is_correct'),
    )

    def __init__(self, **kwargs):
        """Initialize answer with calculated fields"""
        super().__init__(**kwargs)

        # Calculate performance metrics if not provided
        if self.student_answer and self.correct_answer and self.is_correct is None:
            self.is_correct = self.student_answer.upper() == self.correct_answer.upper()

        if self.time_spent and self.time_performance is None:
            self.calculate_time_performance()

    def calculate_time_performance(self, expected_time: int = 120):
        """Calculate time performance metrics"""
        if self.time_spent:
            if self.time_spent <= expected_time:
                self.time_performance = "ideal"
                self.efficiency_score = 100.0
            elif self.time_spent < expected_time * 0.7:
                self.time_performance = "fast"
                self.efficiency_score = min(100, (expected_time * 0.7 / self.time_spent) * 100)
            else:
                self.time_performance = "slow"
                self.efficiency_score = max(20, (expected_time / self.time_spent) * 100)

    def calculate_score(self, base_score: float = 100.0, time_bonus_factor: float = 0.2):
        """Calculate final score with time bonus"""
        if not self.is_correct:
            self.score = 0.0
            return

        # Base score for correct answer
        score = base_score

        # Time bonus (max 20% of base score)
        if self.efficiency_score:
            time_bonus = min(base_score * time_bonus_factor,
                           (self.efficiency_score - 50) / 50 * base_score * time_bonus_factor)
            score += max(0, time_bonus)

        # Difficulty bonus
        if self.difficulty_score:
            difficulty_bonus = self.difficulty_score * base_score * 0.1
            score += difficulty_bonus

        # Hint penalty
        hint_penalty = self.hints_used * base_score * 0.05
        score -= hint_penalty

        # Multiple attempts penalty
        if self.attempts_count > 1:
            attempt_penalty = (self.attempts_count - 1) * base_score * 0.1
            score -= attempt_penalty

        self.score = max(0, min(120, round(score, 1)))

    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        return {
            "answer_correct": self.is_correct,
            "score": self.score,
            "time_spent": self.time_spent,
            "time_performance": self.time_performance,
            "efficiency": self.efficiency_score,
            "hints_used": self.hints_used,
            "attempts": self.attempts_count,
            "submitted_at": self.submitted_at,
            "subject": self.subject,
            "topic": self.topic,
            "difficulty": self.difficulty
        }

    def to_analytics_record(self) -> Dict[str, Any]:
        """Convert to analytics record format"""
        return {
            "student_id": self.student_id,
            "question_id": self.question_id,
            "session_id": self.session_id,
            "subject": self.subject,
            "topic": self.topic,
            "difficulty": self.difficulty,
            "yks_type": self.yks_type,
            "is_correct": self.is_correct,
            "score": self.score,
            "time_spent": self.time_spent,
            "efficiency": self.efficiency_score,
            "submitted_at": self.submitted_at.isoformat() if self.submitted_at else None,
            "session_type": "review" if self.is_review_session else
                           "practice" if self.is_practice_session else
                           "test" if self.is_test_session else "general"
        }

    @classmethod
    def get_student_statistics(cls, session, student_id: str,
                             subject: str = None,
                             days_limit: int = None) -> Dict[str, Any]:
        """Get comprehensive statistics for a student"""
        query = session.query(cls).filter(cls.student_id == student_id)

        if subject:
            query = query.filter(cls.subject == subject)

        if days_limit:
            from datetime import datetime, timedelta
            cutoff_date = datetime.utcnow() - timedelta(days=days_limit)
            query = query.filter(cls.submitted_at >= cutoff_date)

        answers = query.all()

        if not answers:
            return {
                "total_questions": 0,
                "correct_answers": 0,
                "accuracy_rate": 0.0,
                "average_score": 0.0,
                "average_time": 0.0,
                "total_time": 0,
                "subjects_breakdown": {},
                "topics_breakdown": {},
                "difficulty_breakdown": {}
            }

        total = len(answers)
        correct = sum(1 for a in answers if a.is_correct)
        total_score = sum(a.score for a in answers)
        total_time = sum(a.time_spent for a in answers)

        # Subject breakdown
        subjects = {}
        for answer in answers:
            if answer.subject:
                if answer.subject not in subjects:
                    subjects[answer.subject] = {"total": 0, "correct": 0, "time": 0}
                subjects[answer.subject]["total"] += 1
                if answer.is_correct:
                    subjects[answer.subject]["correct"] += 1
                subjects[answer.subject]["time"] += answer.time_spent

        # Topic breakdown
        topics = {}
        for answer in answers:
            if answer.topic:
                if answer.topic not in topics:
                    topics[answer.topic] = {"total": 0, "correct": 0}
                topics[answer.topic]["total"] += 1
                if answer.is_correct:
                    topics[answer.topic]["correct"] += 1

        # Difficulty breakdown
        difficulties = {}
        for answer in answers:
            if answer.difficulty:
                if answer.difficulty not in difficulties:
                    difficulties[answer.difficulty] = {"total": 0, "correct": 0}
                difficulties[answer.difficulty]["total"] += 1
                if answer.is_correct:
                    difficulties[answer.difficulty]["correct"] += 1

        return {
            "total_questions": total,
            "correct_answers": correct,
            "accuracy_rate": (correct / total) * 100,
            "average_score": total_score / total if total > 0 else 0,
            "average_time": total_time / total if total > 0 else 0,
            "total_time": total_time,
            "subjects_breakdown": subjects,
            "topics_breakdown": topics,
            "difficulty_breakdown": difficulties,
            "recent_performance": [a.get_performance_summary()
                                 for a in sorted(answers, key=lambda x: x.submitted_at, reverse=True)[:10]]
        }

    @classmethod
    def get_session_statistics(cls, session, session_id: str) -> Dict[str, Any]:
        """Get statistics for a specific session"""
        answers = session.query(cls).filter(cls.session_id == session_id).all()

        if not answers:
            return {"error": "Session not found"}

        total = len(answers)
        correct = sum(1 for a in answers if a.is_correct)
        total_score = sum(a.score for a in answers)
        total_time = sum(a.time_spent for a in answers)

        return {
            "session_id": session_id,
            "total_questions": total,
            "correct_answers": correct,
            "accuracy_rate": (correct / total) * 100,
            "total_score": total_score,
            "average_score": total_score / total,
            "total_time": total_time,
            "average_time": total_time / total,
            "completion_rate": 100.0,  # All answers in session are complete
            "performance_trend": [a.is_correct for a in sorted(answers, key=lambda x: x.submitted_at)],
            "subjects_covered": list(set(a.subject for a in answers if a.subject)),
            "topics_covered": list(set(a.topic for a in answers if a.topic))
        }

    def mark_as_reviewed(self):
        """Mark answer as reviewed by student"""
        self.explanation_viewed = True
        self.status = AnswerStatus.REVIEWED

    def add_feedback(self, feedback: str):
        """Add AI-generated feedback"""
        self.ai_feedback = feedback
        self.status = AnswerStatus.GRADED
        self.graded_at = datetime.utcnow()

    def __repr__(self):
        return f"<StudentAnswer(id={self.id}, student='{self.student_id}', question='{self.question_id}', correct={self.is_correct}, score={self.score})>"
