"""
Ajan Interaction Model for YKS Genius
Tracks conversations ve interactions arasında students ve AI agents
"""

from sqlalchemy import Column, String, Inte<PERSON>, JSON, Boolean, Text, Float, ForeignKey, DateTime, Index
from sqlalchemy.orm import relationship
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from enum import Enum

from .base import BaseModel

class InteractionType(str, Enum):
    """Types nin Ajan interactions"""
    CHAT = "chat"  # General conversation
    QUESTION = "question"  # Soru-related interaction
    EXPLANATION = "explanation"  # Concept explanation
    FEEDBACK = "feedback"  # Performans feedback
    MOTIVATION = "motivation"  # Motivational support
    ASSESSMENT = "assessment"  # Skill assessment

class AgentRole(str, Enum):
    """AI Ajan roles"""
    STRATEGY = "strategy"  # Study planning ve Strateji
    CONTENT = "content"  # İçerik explanation ve teaching
    PRACTICE = "practice"  # Soru Pratik ve drilling
    ANALYTICS = "analytics"  # Performans analysis
    MENTOR = "mentor"  # Motivation ve emotional support

class InteractionStatus(str, Enum):
    """Interaction status"""
    ACTIVE = "active"  # Ongoing conversation
    COMPLETED = "completed"  # Conversation finished
    FLAGGED = "flagged"  # Flagged for review
    ARCHIVED = "archived"  # Archived interaction

class AgentInteraction(BaseModel):
    """
    Ajan Interaction Model tracking AI-Öğrenci conversations
    
    Features:
    - Detailed conversation logging
    - Ajan Performans tracking
    - Context ve personalization data
    - Quality Metrikler ve feedback
    - Learning outcome measurement
    """
    
    # Foreign Keys
    student_id = Column(Integer, ForeignKey('students.id'), nullable=False, index=True)
    session_id = Column(Integer, ForeignKey('studysessions.id'), nullable=True, index=True)
    question_id = Column(Integer, ForeignKey('questions.id'), nullable=True, index=True)
    
    # Interaction Classification
    interaction_type = Column(String(20), nullable=False, default=InteractionType.CHAT)
    agent_role = Column(String(20), nullable=False, index=True)
    agent_name = Column(String(100), nullable=False)  # Specific Ajan instance Ad
    
    # Conversation Data
    user_message = Column(Text, nullable=False)  # Öğrenci's input
    agent_response = Column(Text, nullable=False)  # Ajan's Yanıt
    conversation_context = Column(JSON, nullable=True, default=dict)  # Previous context
    
    # Metadata
    timestamp = Column(DateTime(timezone=True), default=lambda: datetime.now(timezone.utc), index=True)
    response_time_ms = Column(Integer, nullable=True)  # Zaman e Üret Yanıt
    status = Column(String(20), default=InteractionStatus.COMPLETED)
    
    # Personalization Data
    student_context = Column(JSON, nullable=True, default=dict)
    # Structure: {
    # "current_level": "intermediate",
    # "learning_style": "visual",
    # "recent_performance": {"matematik": 75, "fizik": 68},
    # "mood": "motivated",
    # "session_progress": {"questions_solved": 5, "accuracy": 80}
    # }
    
    # Ajan Decision Data
    agent_reasoning = Column(JSON, nullable=True, default=dict)
    # Structure: {
    # "primary_intent": "explain_concept",
    # "confidence_score": 0.85,
    # "fallback_used": false,
    # "context_used": ["previous_question", "student_level"],
    # "personalization_applied": ["learning_style", "difficulty_adjustment"]
    # }
    
    # İçerik Analysis
    topics_discussed = Column(JSON, nullable=True, default=list)  # ["Sınır", "türev"]
    concepts_explained = Column(JSON, nullable=True, default=list)  # Specific concepts
    difficulty_level = Column(String(20), nullable=True)  # Interaction difficulty
    
    # Quality Metrikler
    helpfulness_score = Column(Float, nullable=True)  # 0-100, nasıl helpful was Yanıt
    accuracy_score = Column(Float, nullable=True)  # 0-100, nasıl accurate was Yanıt
    relevance_score = Column(Float, nullable=True)  # 0-100, nasıl relevant was Yanıt
    student_satisfaction = Column(Integer, nullable=True)  # 1-5, Öğrenci rating
    
    # Learning Outcomes
    learning_objectives = Column(JSON, nullable=True, default=list)  # ne was supposed e be learned
    objectives_met = Column(JSON, nullable=True, default=list)  # ne was actually learned
    knowledge_gained = Column(Boolean, default=False)  # Did Öğrenci gain knowledge
    misconceptions_corrected = Column(JSON, nullable=True, default=list)  # Fixed misconceptions
    
    # Follow-yukarı Data
    follow_up_needed = Column(Boolean, default=False)
    next_steps = Column(JSON, nullable=True, default=list)  # Recommended next actions
    related_topics = Column(JSON, nullable=True, default=list)  # Related topics e explore
    
    # Technical Data
    model_used = Column(String(50), nullable=True)  # hangi AI Model was used
    tokens_used = Column(Integer, nullable=True)  # Token consumption
    cache_hit = Column(Boolean, default=False)  # Was cached Yanıt used
    
    # Feedback ve Improvement
    student_feedback = Column(Text, nullable=True)  # Öğrenci's feedback üzerinde interaction
    improvement_suggestions = Column(JSON, nullable=True, default=list)  # Areas for improvement
    flagged_issues = Column(JSON, nullable=True, default=list)  # Any issues flagged
    
    # Relationships
    student = relationship("Student", back_populates="agent_interactions")
    session = relationship("StudySession", back_populates="agent_interactions")
    question = relationship("Question", back_populates="agent_interactions")
    
    # Indexes for Performans
    __table_args__ = (
        Index('idx_student_agent', 'student_id', 'agent_role'),
        Index('idx_session_timestamp', 'session_id', 'timestamp'),
        Index('idx_interaction_type', 'interaction_type'),
        Index('idx_timestamp_agent', 'timestamp', 'agent_role'),
    )
    
    def __init__(self, **kwargs):
        """Başlat interaction ile Varsayılan values"""
        super().__init__(**kwargs)
        
        if not self.conversation_context:
            self.conversation_context = {}
        if not self.student_context:
            self.student_context = {}
        if not self.agent_reasoning:
            self.agent_reasoning = {}
        if not self.topics_discussed:
            self.topics_discussed = []
        if not self.concepts_explained:
            self.concepts_explained = []
        if not self.learning_objectives:
            self.learning_objectives = []
        if not self.objectives_met:
            self.objectives_met = []
        if not self.misconceptions_corrected:
            self.misconceptions_corrected = []
        if not self.next_steps:
            self.next_steps = []
        if not self.related_topics:
            self.related_topics = []
        if not self.improvement_suggestions:
            self.improvement_suggestions = []
        if not self.flagged_issues:
            self.flagged_issues = []
    
    def record_response_metrics(self, response_time: int, model_used: str, tokens_used: int, cache_hit: bool = False):
        """Record technical Metrikler hakkında the Yanıt"""
        self.response_time_ms = response_time
        self.model_used = model_used
        self.tokens_used = tokens_used
        self.cache_hit = cache_hit
    
    def set_quality_scores(self, helpfulness: float, accuracy: float, relevance: float):
        """Set quality assessment scores"""
        self.helpfulness_score = max(0, min(100, helpfulness))
        self.accuracy_score = max(0, min(100, accuracy))
        self.relevance_score = max(0, min(100, relevance))
    
    def add_learning_outcome(self, objective: str, was_met: bool, knowledge_gained: bool = False):
        """Add a learning outcome assessment"""
        if objective not in self.learning_objectives:
            self.learning_objectives.append(objective)
        
        if was_met and objective not in self.objectives_met:
            self.objectives_met.append(objective)
        
        if knowledge_gained:
            self.knowledge_gained = True
    
    def add_misconception_correction(self, misconception: str, correction: str):
        """Record a corrected misconception"""
        correction_record = {
            "misconception": misconception,
            "correction": correction,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        self.misconceptions_corrected.append(correction_record)
    
    def add_follow_up(self, next_step: str, priority: str = "medium", related_topic: str = None):
        """Add follow-yukarı actions veya topics"""
        self.follow_up_needed = True
        
        follow_up = {
            "action": next_step,
            "priority": priority,
            "added_at": datetime.now(timezone.utc).isoformat()
        }
        self.next_steps.append(follow_up)
        
        if related_topic and related_topic not in self.related_topics:
            self.related_topics.append(related_topic)
    
    def set_student_feedback(self, rating: int, feedback: str = None):
        """Record Öğrenci feedback üzerinde the interaction"""
        self.student_satisfaction = max(1, min(5, rating))
        if feedback:
            self.student_feedback = feedback
        
        # Flag for review eğer rating is low
        if rating <= 2:
            self.status = InteractionStatus.FLAGGED
            if "low_satisfaction" not in self.flagged_issues:
                self.flagged_issues.append("low_satisfaction")
    
    def flag_issue(self, issue_type: str, description: str):
        """Flag an issue ile bu interaction"""
        self.status = InteractionStatus.FLAGGED
        
        issue_record = {
            "type": issue_type,
            "description": description,
            "flagged_at": datetime.now(timezone.utc).isoformat()
        }
        self.flagged_issues.append(issue_record)
    
    def calculate_overall_quality(self) -> float:
        """Hesapla overall interaction quality score"""
        scores = []
        
        if self.helpfulness_score is not None:
            scores.append(self.helpfulness_score)
        if self.accuracy_score is not None:
            scores.append(self.accuracy_score)
        if self.relevance_score is not None:
            scores.append(self.relevance_score)
        
        # Include Öğrenci satisfaction (Çevir 1-5 e 0-100)
        if self.student_satisfaction:
            satisfaction_score = (self.student_satisfaction - 1) / 4 * 100
            scores.append(satisfaction_score)
        
        return sum(scores) / len(scores) if scores else 0.0
    
    def get_effectiveness_metrics(self) -> Dict[str, Any]:
        """Get interaction effectiveness Metrikler"""
        return {
            "objectives_set": len(self.learning_objectives),
            "objectives_met": len(self.objectives_met),
            "success_rate": (len(self.objectives_met) / len(self.learning_objectives) * 100) if self.learning_objectives else 0,
            "knowledge_gained": self.knowledge_gained,
            "misconceptions_corrected": len(self.misconceptions_corrected),
            "follow_up_generated": len(self.next_steps),
            "concepts_covered": len(self.concepts_explained),
            "overall_quality": self.calculate_overall_quality()
        }
    
    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get summary nin the conversation"""
        return {
            "interaction_id": self.id,
            "agent": self.agent_name,
            "agent_role": self.agent_role,
            "type": self.interaction_type,
            "timestamp": self.timestamp.isoformat(),
            "user_message_preview": self.user_message[:100] + "..." if len(self.user_message) > 100 else self.user_message,
            "response_preview": self.agent_response[:100] + "..." if len(self.agent_response) > 100 else self.agent_response,
            "topics_discussed": self.topics_discussed,
            "response_time_ms": self.response_time_ms,
            "student_satisfaction": self.student_satisfaction,
            "quality_score": self.calculate_overall_quality(),
            "follow_up_needed": self.follow_up_needed,
            "status": self.status
        }
    
    def is_high_quality(self) -> bool:
        """Check eğer bu is a high-quality interaction"""
        quality_score = self.calculate_overall_quality()
        return (quality_score >= 75 and 
                self.student_satisfaction and self.student_satisfaction >= 4 and
                len(self.objectives_met) >= len(self.learning_objectives) / 2)
    
    def needs_improvement(self) -> bool:
        """Check eğer bu interaction needs improvement"""
        quality_score = self.calculate_overall_quality()
        return (quality_score < 50 or 
                (self.student_satisfaction and self.student_satisfaction <= 2) or
                len(self.flagged_issues) > 0)
    
    def get_improvement_areas(self) -> List[str]:
        """Get areas o need improvement"""
        improvements = []
        
        if self.helpfulness_score and self.helpfulness_score < 60:
            improvements.append("Response helpfulness")
        if self.accuracy_score and self.accuracy_score < 70:
            improvements.append("Information accuracy")
        if self.relevance_score and self.relevance_score < 60:
            improvements.append("Response relevance")
        if self.response_time_ms and self.response_time_ms > 5000:
            improvements.append("Response speed")
        if len(self.objectives_met) < len(self.learning_objectives) / 2:
            improvements.append("Learning effectiveness")
        
        return improvements + [issue["type"] for issue in self.flagged_issues]
    
    def archive_interaction(self):
        """Archive bu interaction"""
        self.status = InteractionStatus.ARCHIVED
    
    @classmethod
    def get_agent_performance_summary(cls, session, agent_role: str, days: int = 30) -> Dict[str, Any]:
        """Get Performans summary for a specific Ajan"""
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
        
        interactions = session.query(cls).filter(
            cls.agent_role == agent_role,
            cls.timestamp >= cutoff_date
        ).all()
        
        if not interactions:
            return {"message": f"No interactions found for {agent_role} in the last {days} days"}
        
        # Hesapla Metrikler
        total_interactions = len(interactions)
        avg_response_time = sum(i.response_time_ms for i in interactions if i.response_time_ms) / total_interactions
        avg_quality = sum(i.calculate_overall_quality() for i in interactions) / total_interactions
        
        satisfaction_scores = [i.student_satisfaction for i in interactions if i.student_satisfaction]
        avg_satisfaction = sum(satisfaction_scores) / len(satisfaction_scores) if satisfaction_scores else 0
        
        flagged_count = len([i for i in interactions if i.status == InteractionStatus.FLAGGED])
        
        return {
            "agent_role": agent_role,
            "period_days": days,
            "total_interactions": total_interactions,
            "average_response_time_ms": round(avg_response_time, 1),
            "average_quality_score": round(avg_quality, 1),
            "average_satisfaction": round(avg_satisfaction, 1),
            "flagged_interactions": flagged_count,
            "flagged_percentage": round(flagged_count / total_interactions * 100, 1),
            "high_quality_interactions": len([i for i in interactions if i.is_high_quality()]),
            "needs_improvement_count": len([i for i in interactions if i.needs_improvement()])
        }
    
    @classmethod
    def get_student_interaction_history(cls, session, student_id: int, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent interaction history for a Öğrenci"""
        interactions = session.query(cls).filter(
            cls.student_id == student_id
        ).order_by(cls.timestamp.desc()).limit(limit).all()
        
        return [interaction.get_conversation_summary() for interaction in interactions]
    
    def __repr__(self):
        return f"<AgentInteraction(id={self.id}, student_id={self.student_id}, agent='{self.agent_role}', type='{self.interaction_type}')>"