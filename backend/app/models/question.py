"""
YKS Genius için Soru modeli
Meta veri ve analitik ile YKS pratik sorularını saklar
"""

from sqlalchemy import Column, String, Integer, JSON, Boolean, Text, Float, Index
from sqlalchemy.orm import relationship
from typing import Dict, Any, Optional, List
from enum import Enum

from .base import BaseModel

class QuestionDifficulty(str, Enum):
    """Soru zorluk seviyeleri"""
    KOLAY = "kolay"
    ORTA = "orta"
    ZOR = "zor"
    COKZOR = "çok_zor"

class YKSType(str, Enum):
    """YKS sınav türleri"""
    TYT = "TYT"
    AYT = "AYT"
    MSU = "MSU"

class QuestionStatus(str, Enum):
    """Soru durumu"""
    ACTIVE = "active"
    DRAFT = "draft"
    ARCHIVED = "archived"

class Question(BaseModel):
    """
    Soru Model representing YKS Pratik questions

    Features:
    - Multi-choice questions ile explanations
    - Subject ve topic categorization
    - Difficulty ve YKS type classification
    - Performans Analitik tracking
    - Tags for advanced filtering
    """

    # İçerik
    content = Column(Text, nullable=False)  # Soru Metin
    subject = Column(String(50), nullable=False, index=True)  # matematik, fizik, etc.
    topic = Column(String(100), nullable=False, index=True)  # Sınır, türev, etc.
    subtopic = Column(String(100), nullable=True)  # More specific categorization

    # Classification
    difficulty = Column(String(20), nullable=False, index=True)  # kolay, orta, zor, çok_zor
    yks_type = Column(String(10), nullable=False, index=True)  # TYT, AYT, MSU
    estimated_time = Column(Integer, nullable=True)  # Seconds e solve

    # Options ve Cevap
    options = Column(JSON, nullable=False)
    # Structure: {"A": "option1", "B": "option2", "C": "option3", "D": "option4", "E": "option5"}

    correct_answer = Column(String(1), nullable=False)  # A, B, C, D, E

    # Explanations
    explanation = Column(Text, nullable=True)  # Detailed solution explanation
    solution_steps = Column(JSON, nullable=True)  # Step-by-step solution
    # Structure: ["Step 1 Açıklama", "Step 2 Açıklama", ...]

    common_mistakes = Column(JSON, nullable=True)  # Common Öğrenci mistakes
    # Structure: ["Mistake 1", "Mistake 2", ...]

    tips = Column(Text, nullable=True)  # Solving tips ve tricks

    # Metadata
    tags = Column(JSON, nullable=True, default=list)  # ["integral", "substitution", ...]
    source = Column(String(100), nullable=True)  # Source nin Soru (textbook, exam, etc.)
    year = Column(Integer, nullable=True)  # Year eğer den actual exam
    question_number = Column(Integer, nullable=True)  # Original Soru Sayı

    # Analitik
    times_asked = Column(Integer, default=0)  # nasıl many times bu Soru was asked
    times_correct = Column(Integer, default=0)  # nasıl many times answered correctly
    average_time = Column(Float, nullable=True)  # Ortalama solving Zaman de seconds
    success_rate = Column(Float, default=0.0)  # Percentage nin correct answers

    # Quality Metrikler
    quality_score = Column(Float, default=0.0)  # Internal quality rating
    feedback_count = Column(Integer, default=0)  # Sayı nin feedback submissions
    report_count = Column(Integer, default=0)  # Sayı nin reports (errors, etc.)

    # Status ve Visibility
    status = Column(String(20), default=QuestionStatus.ACTIVE)
    is_verified = Column(Boolean, default=False)  # Verified tarafından experts
    created_by = Column(String(50), nullable=True)  # Creator identifier

    # Relationships
    agent_interactions = relationship("AgentInteraction", back_populates="question")

    # Indexes for Performans
    __table_args__ = (
        Index('idx_subject_topic', 'subject', 'topic'),
        Index('idx_difficulty_type', 'difficulty', 'yks_type'),
        Index('idx_success_rate', 'success_rate'),
        Index('idx_tags', 'tags'),  # For JSON queries
    )

    def __init__(self, **kwargs):
        """Başlat Soru ile Varsayılan values"""
        super().__init__(**kwargs)

        if not self.tags:
            self.tags = []

        if not self.solution_steps:
            self.solution_steps = []

        if not self.common_mistakes:
            self.common_mistakes = []

    def calculate_success_rate(self):
        """Hesapla ve Güncelle Başarılı rate"""
        if self.times_asked > 0:
            self.success_rate = (self.times_correct / self.times_asked) * 100
        else:
            self.success_rate = 0.0

    def record_attempt(self, is_correct: bool, time_taken: int):
        """Record a Öğrenci attempt üzerinde bu Soru"""
        self.times_asked += 1

        if is_correct:
            self.times_correct += 1

        # Güncelle Ortalama Zaman
        if self.average_time is None:
            self.average_time = time_taken
        else:
            # Çalışıyor Ortalama
            total_time = self.average_time * (self.times_asked - 1) + time_taken
            self.average_time = total_time / self.times_asked

        # Recalculate Başarılı rate
        self.calculate_success_rate()

    def get_difficulty_score(self) -> float:
        """Get numeric difficulty score (0-1)"""
        difficulty_map = {
            QuestionDifficulty.KOLAY: 0.25,
            QuestionDifficulty.ORTA: 0.5,
            QuestionDifficulty.ZOR: 0.75,
            QuestionDifficulty.COKZOR: 1.0
        }
        return difficulty_map.get(self.difficulty, 0.5)

    def is_suitable_for_student(self, student_level: str, subject_performance: float) -> bool:
        """Check eğer Soru is suitable for Öğrenci's level"""
        difficulty_score = self.get_difficulty_score()

        # Adjust based üzerinde Öğrenci level
        level_adjustments = {
            "beginner": 0.3,
            "intermediate": 0.6,
            "advanced": 0.9
        }

        student_threshold = level_adjustments.get(student_level, 0.5)

        # Consider subject Performans
        performance_factor = subject_performance / 100.0
        adjusted_threshold = student_threshold * 0.7 + performance_factor * 0.3

        # Allow some variance for adaptive learning
        return abs(difficulty_score - adjusted_threshold) <= 0.3

    def get_related_topics(self) -> List[str]:
        """Get related topics den tags"""
        return [tag for tag in self.tags if tag != self.topic]

    def add_tag(self, tag: str):
        """Add a tag e the Soru"""
        if tag not in self.tags:
            self.tags.append(tag)

    def remove_tag(self, tag: str):
        """Remove a tag den the Soru"""
        if tag in self.tags:
            self.tags.remove(tag)

    def get_analytics_summary(self) -> Dict[str, Any]:
        """Get Analitik summary for bu Soru"""
        return {
            "times_asked": self.times_asked,
            "times_correct": self.times_correct,
            "success_rate": self.success_rate,
            "average_time": self.average_time,
            "difficulty_score": self.get_difficulty_score(),
            "quality_score": self.quality_score,
            "is_popular": self.times_asked > 10,
            "is_challenging": self.success_rate < 50,
            "needs_review": self.report_count > 2
        }

    def to_student_format(self, include_answer: bool = False) -> Dict[str, Any]:
        """Çevir e Biçimlendir suitable for Öğrenci interaction"""
        data = {
            "id": self.id,
            "content": self.content,
            "subject": self.subject,
            "topic": self.topic,
            "difficulty": self.difficulty,
            "yks_type": self.yks_type,
            "estimated_time": self.estimated_time,
            "options": self.options,
            "tips": self.tips,
            "tags": self.tags
        }

        if include_answer:
            data.update({
                "correct_answer": self.correct_answer,
                "explanation": self.explanation,
                "solution_steps": self.solution_steps,
                "common_mistakes": self.common_mistakes
            })

        return data

    def validate_question(self) -> List[str]:
        """Doğrula Soru data ve return Liste nin errors"""
        errors = []

        # Check Gerekli fields
        if not self.content or len(self.content.strip()) < 10:
            errors.append("Question content is too short")

        if not self.options or len(self.options) != 5:
            errors.append("Question must have exactly 5 options (A-E)")

        if self.correct_answer not in ['A', 'B', 'C', 'D', 'E']:
            errors.append("Correct answer must be one of A, B, C, D, E")

        if self.correct_answer not in self.options:
            errors.append("Correct answer key not found in options")

        # Check subject validity
        valid_subjects = [
            "matematik", "fizik", "kimya", "biyoloji", "türkçe",
            "tarih", "coğrafya", "felsefe", "din", "geometri"
        ]
        if self.subject not in valid_subjects:
            errors.append(f"Invalid subject: {self.subject}")

        # Check YKS type
        if self.yks_type not in [t.value for t in YKSType]:
            errors.append(f"Invalid YKS type: {self.yks_type}")

        return errors

    @classmethod
    def get_questions_by_criteria(cls, session, subject: str = None, topic: str = None,
                                 difficulty: str = None, yks_type: str = None,
                                 limit: int = 20) -> List['Question']:
        """Get questions matching specified criteria"""
        query = session.query(cls).filter(cls.status == QuestionStatus.ACTIVE)

        if subject:
            query = query.filter(cls.subject == subject)
        if topic:
            query = query.filter(cls.topic == topic)
        if difficulty:
            query = query.filter(cls.difficulty == difficulty)
        if yks_type:
            query = query.filter(cls.yks_type == yks_type)

        return query.limit(limit).all()

    def __repr__(self):
        return f"<Question(id={self.id}, subject='{self.subject}', topic='{self.topic}', difficulty='{self.difficulty}')>"