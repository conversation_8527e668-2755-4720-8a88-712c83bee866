"""
YKS Genius için Soru modeli
Meta veri ve analitik ile YKS pratik sorularını saklar
"""

from sqlalchemy import Column, String, Integer, JSON, Boolean, Text, Float, Index
from sqlalchemy.orm import relationship
from typing import Dict, Any, Optional, List
from enum import Enum

from .base import BaseModel

class QuestionDifficulty(str, Enum):
    """Soru zorluk seviyeleri"""
    KOLAY = "kolay"
    ORTA = "orta"
    ZOR = "zor"
    COKZOR = "çok_zor"

class YKSType(str, Enum):
    """YKS sınav türleri"""
    TYT = "TYT"
    AYT = "AYT"
    MSU = "MSU"

class QuestionStatus(str, Enum):
    """Soru durumu"""
    ACTIVE = "active"
    DRAFT = "draft"
    ARCHIVED = "archived"

class Question(BaseModel):
    """
    YKS pratik sorularını temsil eden Soru Modeli

    Özellikler:
    - Açıklamalı çoktan seçmeli sorular
    - Ders ve konu kategorilendirmesi
    - Zorluk ve YKS türü sınıflandırması
    - Performans analitik takibi
    - Gelişmiş filtreleme için etiketler
    """

    # İçerik
    content = Column(Text, nullable=False)  # Soru metni
    subject = Column(String(50), nullable=False, index=True)  # matematik, fizik, vb.
    topic = Column(String(100), nullable=False, index=True)  # limit, türev, vb.
    subtopic = Column(String(100), nullable=True)  # Daha spesifik kategorilendirme

    # Sınıflandırma
    difficulty = Column(String(20), nullable=False, index=True)  # kolay, orta, zor, çok_zor
    yks_type = Column(String(10), nullable=False, index=True)  # TYT, AYT, MSU
    estimated_time = Column(Integer, nullable=True)  # Çözüm için saniye

    # Seçenekler ve Cevap
    options = Column(JSON, nullable=False)
    # Yapı: {"A": "seçenek1", "B": "seçenek2", "C": "seçenek3", "D": "seçenek4", "E": "seçenek5"}

    correct_answer = Column(String(1), nullable=False)  # A, B, C, D, E

    # Açıklamalar
    explanation = Column(Text, nullable=True)  # Detaylı çözüm açıklaması
    solution_steps = Column(JSON, nullable=True)  # Adım adım çözüm
    # Yapı: ["Adım 1 açıklaması", "Adım 2 açıklaması", ...]

    common_mistakes = Column(JSON, nullable=True)  # Yaygın öğrenci hataları
    # Yapı: ["Hata 1", "Hata 2", ...]

    tips = Column(Text, nullable=True)  # Çözüm ipuçları ve püf noktaları

    # Meta veriler
    tags = Column(JSON, nullable=True, default=list)  # ["integral", "yerine koyma", ...]
    source = Column(String(100), nullable=True)  # Sorunun kaynağı (ders kitabı, sınav, vb.)
    year = Column(Integer, nullable=True)  # Gerçek sınavdan ise yılı
    question_number = Column(Integer, nullable=True)  # Orijinal soru numarası

    # Analitik
    times_asked = Column(Integer, default=0)  # Bu sorunun kaç kez sorulduğu
    times_correct = Column(Integer, default=0)  # Kaç kez doğru cevaplandığı
    average_time = Column(Float, nullable=True)  # Ortalama çözüm süresi (saniye)
    success_rate = Column(Float, default=0.0)  # Doğru cevap yüzdesi

    # Kalite metrikleri
    quality_score = Column(Float, default=0.0)  # İç kalite puanı
    feedback_count = Column(Integer, default=0)  # Geri bildirim sayısı
    report_count = Column(Integer, default=0)  # Rapor sayısı (hatalar, vb.)

    # Durum ve görünürlük
    status = Column(String(20), default=QuestionStatus.ACTIVE)
    is_verified = Column(Boolean, default=False)  # Uzmanlar tarafından doğrulandı
    created_by = Column(String(50), nullable=True)  # Oluşturan kişi tanımlayıcısı

    # İlişkiler
    agent_interactions = relationship("AgentInteraction", back_populates="question")

    # Performans için indeksler
    __table_args__ = (
        Index('idx_subject_topic', 'subject', 'topic'),
        Index('idx_difficulty_type', 'difficulty', 'yks_type'),
        Index('idx_success_rate', 'success_rate'),
        Index('idx_tags', 'tags'),  # JSON sorguları için
    )

    def __init__(self, **kwargs):
        """Soruyu varsayılan değerlerle başlat"""
        super().__init__(**kwargs)

        if not self.tags:
            self.tags = []

        if not self.solution_steps:
            self.solution_steps = []

        if not self.common_mistakes:
            self.common_mistakes = []

    def calculate_success_rate(self):
        """Başarı oranını hesapla ve güncelle"""
        if self.times_asked > 0:
            self.success_rate = (self.times_correct / self.times_asked) * 100
        else:
            self.success_rate = 0.0

    def record_attempt(self, is_correct: bool, time_taken: int):
        """Bu soru üzerinde bir öğrenci denemesini kaydet"""
        self.times_asked += 1

        if is_correct:
            self.times_correct += 1

        # Ortalama zamanı güncelle
        if self.average_time is None:
            self.average_time = time_taken
        else:
            # Çalışan ortalama
            total_time = self.average_time * (self.times_asked - 1) + time_taken
            self.average_time = total_time / self.times_asked

        # Başarı oranını yeniden hesapla
        self.calculate_success_rate()

    def get_difficulty_score(self) -> float:
        """Sayısal zorluk puanını al (0-1)"""
        difficulty_map = {
            QuestionDifficulty.KOLAY: 0.25,
            QuestionDifficulty.ORTA: 0.5,
            QuestionDifficulty.ZOR: 0.75,
            QuestionDifficulty.COKZOR: 1.0
        }
        return difficulty_map.get(self.difficulty, 0.5)

    def is_suitable_for_student(self, student_level: str, subject_performance: float) -> bool:
        """Sorunun öğrencinin seviyesine uygun olup olmadığını kontrol et"""
        difficulty_score = self.get_difficulty_score()

        # Öğrenci seviyesine göre ayarla
        level_adjustments = {
            "beginner": 0.3,
            "intermediate": 0.6,
            "advanced": 0.9
        }

        student_threshold = level_adjustments.get(student_level, 0.5)

        # Ders performansını dikkate al
        performance_factor = subject_performance / 100.0
        adjusted_threshold = student_threshold * 0.7 + performance_factor * 0.3

        # Uyarlanabilir öğrenme için biraz varyans izin ver
        return abs(difficulty_score - adjusted_threshold) <= 0.3

    def get_related_topics(self) -> List[str]:
        """Etiketlerden ilgili konuları al"""
        return [tag for tag in self.tags if tag != self.topic]

    def add_tag(self, tag: str):
        """Soruya bir etiket ekle"""
        if tag not in self.tags:
            self.tags.append(tag)

    def remove_tag(self, tag: str):
        """Sorudan bir etiket kaldır"""
        if tag in self.tags:
            self.tags.remove(tag)

    def get_analytics_summary(self) -> Dict[str, Any]:
        """Bu soru için analitik özetini al"""
        return {
            "times_asked": self.times_asked,
            "times_correct": self.times_correct,
            "success_rate": self.success_rate,
            "average_time": self.average_time,
            "difficulty_score": self.get_difficulty_score(),
            "quality_score": self.quality_score,
            "is_popular": self.times_asked > 10,
            "is_challenging": self.success_rate < 50,
            "needs_review": self.report_count > 2
        }

    def to_student_format(self, include_answer: bool = False) -> Dict[str, Any]:
        """Öğrenci etkileşimi için uygun formata çevir"""
        data = {
            "id": self.id,
            "content": self.content,
            "subject": self.subject,
            "topic": self.topic,
            "difficulty": self.difficulty,
            "yks_type": self.yks_type,
            "estimated_time": self.estimated_time,
            "options": self.options,
            "tips": self.tips,
            "tags": self.tags
        }

        if include_answer:
            data.update({
                "correct_answer": self.correct_answer,
                "explanation": self.explanation,
                "solution_steps": self.solution_steps,
                "common_mistakes": self.common_mistakes
            })

        return data

    def validate_question(self) -> List[str]:
        """Soru verilerini doğrula ve hata listesini döndür"""
        errors = []

        # Gerekli alanları kontrol et
        if not self.content or len(self.content.strip()) < 10:
            errors.append("Soru içeriği çok kısa")

        if not self.options or len(self.options) != 5:
            errors.append("Soru tam olarak 5 seçeneğe sahip olmalı (A-E)")

        if self.correct_answer not in ['A', 'B', 'C', 'D', 'E']:
            errors.append("Doğru cevap A, B, C, D, E'den biri olmalı")

        if self.correct_answer not in self.options:
            errors.append("Doğru cevap anahtarı seçeneklerde bulunamadı")

        # Ders geçerliliğini kontrol et
        valid_subjects = [
            "matematik", "fizik", "kimya", "biyoloji", "türkçe",
            "tarih", "coğrafya", "felsefe", "din", "geometri"
        ]
        if self.subject not in valid_subjects:
            errors.append(f"Geçersiz ders: {self.subject}")

        # YKS türünü kontrol et
        if self.yks_type not in [t.value for t in YKSType]:
            errors.append(f"Geçersiz YKS türü: {self.yks_type}")

        return errors

    @classmethod
    def get_questions_by_criteria(cls, session, subject: str = None, topic: str = None,
                                 difficulty: str = None, yks_type: str = None,
                                 limit: int = 20) -> List['Question']:
        """Belirtilen kriterlere uyan soruları al"""
        query = session.query(cls).filter(cls.status == QuestionStatus.ACTIVE)

        if subject:
            query = query.filter(cls.subject == subject)
        if topic:
            query = query.filter(cls.topic == topic)
        if difficulty:
            query = query.filter(cls.difficulty == difficulty)
        if yks_type:
            query = query.filter(cls.yks_type == yks_type)

        return query.limit(limit).all()

    def __repr__(self):
        return f"<Question(id={self.id}, subject='{self.subject}', topic='{self.topic}', difficulty='{self.difficulty}')>"