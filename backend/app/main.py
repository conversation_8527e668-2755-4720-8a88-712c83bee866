"""
YKS Genius Backend - Main Application
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import logging
from datetime import datetime
from typing import Optional

from .config import settings
from .core.gemini_client import GeminiOptimizer
from .agents import YKSAgentOrchestrator
from .core.hackathon_optimizations import setup_hackathon_mode
from .api.websocket import manager
from .api import routes
from .database import init_database, get_database_status
from .vector_store import initialize_vector_store, get_vector_store
from .curriculum_loader import initialize_curriculum, get_curriculum_loader
from .data_access import initialize_data_access, get_data_access
from .core.cache import cache_health_check

# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level),
    format=settings.log_format
)
logger = logging.getLogger(__name__)

# Global service instances
orchestrator = None
gemini_client = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    global orchestrator, gemini_client
    
    # Startup
    logger.info(f"Starting {settings.app_name} v{settings.app_version}")
    logger.info("Initializing services...")
    
    # Setup hackathon mode if enabled
    if setup_hackathon_mode():
        logger.info("🏁 Hackathon mode activated!")
    
    try:
        # Initialize database
        logger.info("Initializing database...")
        await init_database()
        
        # Skip vector store initialization for hackathon demo (can hang)
        if not settings.hackathon_mode:
            logger.info("Initializing vector store...")
            await initialize_vector_store()
            
            logger.info("Initializing YKS curriculum...")
            await initialize_curriculum()
            
            logger.info("Initializing data access layer...")
            await initialize_data_access()
        else:
            logger.info("⚡ Hackathon mode: Skipping slow initialization steps...")
        
        # Initialize Gemini client
        logger.info("Initializing Gemini client...")
        gemini_client = GeminiOptimizer()
        
        # Initialize multi-agent orchestrator with LangGraph
        logger.info("YKS Genius çok ajanlı sistemi başlatılıyor...")
        orchestrator = YKSAgentOrchestrator(gemini_client=gemini_client)
        
        # Connect WebSocket manager with orchestrator
        logger.info("WebSocket manager'ı orchestrator'a bağlanıyor...")
        manager.set_orchestrator(orchestrator)
        
        logger.info("✅ All services initialized successfully!")
    except Exception as e:
        logger.error(f"Failed to initialize services: {e}")
        raise
    
    yield
    
    # Shutdown
    logger.info("Shutting down application...")
    orchestrator = None
    gemini_client = None

# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    description="YKS sınavına hazırlık için AI destekli çok ajanlı sistem",
    version=settings.app_version,
    lifespan=lifespan,
    debug=settings.debug
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include REST API routes
app.include_router(routes.router, prefix="/api/v1", tags=["API"])

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    # Check database status
    db_status = get_database_status()
    
    # Check vector store status
    try:
        vs = get_vector_store()
        vs_status = await vs.health_check()
    except Exception as e:
        vs_status = {"status": "unhealthy", "error": str(e)}
    
    # Check curriculum status
    try:
        curriculum = get_curriculum_loader()
        curriculum_status = {
            "status": "healthy" if curriculum.is_curriculum_loaded() else "not_loaded",
            "loaded": curriculum.is_curriculum_loaded()
        }
    except Exception as e:
        curriculum_status = {"status": "unhealthy", "error": str(e)}
    
    # Check cache status
    try:
        cache_status = await cache_health_check()
    except Exception as e:
        cache_status = {"status": "unhealthy", "error": str(e)}
    
    # Check data access layer
    try:
        data_access = get_data_access()
        da_status = await data_access.health_check()
    except Exception as e:
        da_status = {"overall": "unhealthy", "error": str(e)}
    
    # Determine overall status
    all_healthy = (
        db_status["status"] == "healthy" and
        vs_status["status"] == "healthy" and
        curriculum_status["status"] == "healthy" and
        cache_status["status"] == "healthy" and
        da_status["overall"] == "healthy" and
        orchestrator is not None
    )
    
    return {
        "status": "healthy" if all_healthy else "degraded",
        "timestamp": datetime.now().isoformat(),
        "services": {
            "api": "operational",
            "database": db_status["status"],
            "vector_store": vs_status["status"],
            "curriculum": curriculum_status["status"],
            "cache": cache_status["status"],
            "data_access": da_status["overall"],
            "gemini": "operational" if orchestrator else "pending",
            "websocket": "operational" if manager else "pending"
        },
        "details": {
            "database": db_status,
            "vector_store": vs_status,
            "curriculum": curriculum_status,
            "cache": cache_status,
            "data_access": da_status
        },
        "websocket_connections": manager.get_connection_count() if manager else 0
    }

# API info endpoint
@app.get("/api/v1/info")
async def api_info():
    """API information endpoint"""
    return {
        "name": settings.app_name,
        "version": settings.app_version,
        "description": "YKS sınavına hazırlık için AI destekli çok ajanlı sistem",
        "features": [
            "🎯 Kişiselleştirilmiş çalışma planı",
            "📚 Konu anlatımı ve örnekler",
            "📝 Seviyeye uygun soru üretimi",
            "📊 Performans analizi",
            "💪 Motivasyon desteği"
        ],
        "endpoints": {
            "health": "/health",
            "chat": "/api/v1/chat",
            "welcome": "/api/v1/welcome",
            "database_status": "/api/v1/database/status",
            "websocket": "/ws/{student_id}",
            "websocket_info": "/api/v1/websocket/info",
            "docs": "/docs"
        }
    }

# Welcome endpoint
@app.get("/api/v1/welcome")
async def welcome():
    """Hoşgeldin mesajı ve hızlı aksiyonlar"""
    return {
        "success": True,
        "message": "YKS Genius'a hoş geldiniz! 🎯",
        "description": "Size YKS yolculuğunuzda yardımcı olacak AI destekli koçunuz",
        "agents": [
            {"name": "Strateji Uzmanı", "role": "Çalışma planı ve program oluşturma"},
            {"name": "Konu Anlatım Uzmanı", "role": "Konuları anlaşılır şekilde açıklama"},
            {"name": "Pratik Uzmanı", "role": "Soru üretimi ve alıştırma"},
            {"name": "Analiz Uzmanı", "role": "Performans takibi ve öneriler"},
            {"name": "Motivasyon Koçu", "role": "Motivasyon ve stres yönetimi"}
        ],
        "quick_actions": [
            "Bana haftalık çalışma planı oluştur",
            "Matematik limit konusunu anlat",
            "Bana 5 matematik sorusu sor",
            "Son performansımı göster",
            "Motivasyona ihtiyacım var"
        ]
    }

# Chat endpoint
from pydantic import BaseModel

class ChatRequest(BaseModel):
    message: str
    student_id: str = "demo"
    request_type: Optional[str] = None

@app.post("/api/v1/chat")
async def chat(request: ChatRequest):
    """Ana sohbet endpoint'i - mesajları agent sistemine yönlendirir"""
    if not orchestrator:
        return {"success": False, "error": "Sistem henüz hazır değil"}
    
    try:
        response = await orchestrator.process_student_request(
            student_id=request.student_id,
            message=request.message,
            request_type=request.request_type
        )
        return response
    except Exception as e:
        logger.error(f"Chat error: {e}")
        return {
            "success": False,
            "error": "Bir hata oluştu, lütfen tekrar deneyin",
            "details": str(e) if settings.debug else None
        }

# Demo scenarios endpoint
@app.get("/api/v1/demo-scenarios")
async def get_demo_scenarios():
    """Hackathon sunumu için demo senaryoları"""
    return {
        "success": True,
        "scenarios": [
            {
                "title": "Çalışma Planı Oluşturma",
                "messages": [
                    "Tıp okumak istiyorum, bana çalışma planı oluştur",
                    "Matematik ve fizik konularında zorlanıyorum"
                ]
            },
            {
                "title": "Konu Anlatımı",
                "messages": [
                    "Limit konusunu anlatır mısın?",
                    "Türev alma kurallarını öğrenmek istiyorum"
                ]
            },
            {
                "title": "Soru Çözümü",
                "messages": [
                    "Bana 5 matematik sorusu sor",
                    "TYT seviyesinde geometri soruları istiyorum"
                ]
            }
        ]
    }

# Test Day 6 components endpoint
@app.get("/api/v1/test/day6")
async def test_day6_components():
    """Test Day 6 components functionality"""
    try:
        results = {
            "timestamp": datetime.now().isoformat(),
            "tests": {}
        }
        
        # Test curriculum loader
        try:
            curriculum = get_curriculum_loader()
            subjects = curriculum.get_subjects("TYT")  
            math_topics = curriculum.get_topics("Matematik", "TYT")
            results["tests"]["curriculum"] = {
                "status": "pass",
                "subjects_count": len(subjects),
                "math_topics_count": len(math_topics),
                "sample_subjects": subjects[:3],
                "sample_topics": math_topics[:3]
            }
        except Exception as e:
            results["tests"]["curriculum"] = {"status": "fail", "error": str(e)}
        
        # Test cache system
        try:
            from .core.cache import get_cache
            cache = get_cache()
            
            # Test basic operations
            cache.set("test_key", "test_value", ttl=60)
            retrieved = cache.get("test_key")
            cache.delete("test_key")
            
            stats = cache.get_stats()
            results["tests"]["cache"] = {
                "status": "pass" if retrieved == "test_value" else "fail",
                "stats": stats
            }
        except Exception as e:
            results["tests"]["cache"] = {"status": "fail", "error": str(e)}
        
        # Test data access layer
        try:
            data_access = get_data_access()
            health = await data_access.health_check()
            results["tests"]["data_access"] = {
                "status": "pass" if health["overall"] in ["healthy", "degraded"] else "fail",
                "health": health
            }
        except Exception as e:
            results["tests"]["data_access"] = {"status": "fail", "error": str(e)}
        
        # Overall status
        all_passed = all(
            test.get("status") == "pass" 
            for test in results["tests"].values()
        )
        
        results["overall_status"] = "pass" if all_passed else "fail"
        results["message"] = "✅ Day 6 components working!" if all_passed else "❌ Some Day 6 components failed"
        
        return results
        
    except Exception as e:
        return {
            "overall_status": "fail",
            "message": f"Day 6 test failed: {e}",
            "timestamp": datetime.now().isoformat()
        }

# Database status endpoint for detailed monitoring
@app.get("/api/v1/database/status")
async def database_status():
    """Detailed database status and statistics"""
    try:
        # Get database status
        db_status = get_database_status()
        
        # Get vector store statistics
        vs = get_vector_store()
        vs_stats = vs.get_collection_stats()
        
        return {
            "success": True,
            "database": db_status,
            "vector_store": {
                "status": "operational",
                "statistics": vs_stats
            },
            "monitoring": {
                "last_check": datetime.now().isoformat(),
                "uptime_status": "monitoring_enabled"
            }
        }
    except Exception as e:
        logger.error(f"Database status check failed: {e}")
        return {
            "success": False,
            "error": "Database status check failed",
            "details": str(e) if settings.debug else None
        }

# WebSocket endpoint for real-time agent communication
@app.websocket("/ws/{student_id}")
async def websocket_endpoint(websocket: WebSocket, student_id: str):
    """
    WebSocket endpoint for real-time communication with agents
    
    Args:
        websocket: WebSocket connection
        student_id: Unique student identifier
    """
    logger.info(f"WebSocket connection attempt from student: {student_id}")
    
    try:
        # Connect the student
        await manager.connect(websocket, student_id)
        
        # Keep connection alive and handle messages
        while True:
            try:
                # Receive message
                data = await websocket.receive_text()
                
                # Handle the message
                await manager.handle_message(student_id, data)
                
            except WebSocketDisconnect:
                logger.info(f"Student {student_id} disconnected normally")
                manager.disconnect(student_id)
                break
            except Exception as e:
                logger.error(f"WebSocket error for {student_id}: {e}")
                manager.disconnect(student_id)
                break
                
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
        if student_id in manager.active_connections:
            manager.disconnect(student_id)

# WebSocket info endpoint
@app.get("/api/v1/websocket/info")
async def websocket_info():
    """Get WebSocket connection information"""
    return {
        "success": True,
        "endpoint": "/ws/{student_id}",
        "protocol": "ws://",
        "connection_info": manager.get_connection_info(),
        "features": [
            "Real-time agent responses",
            "Typing indicators",
            "Message history",
            "Multi-agent communication",
            "Error recovery"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level=settings.log_level.lower()
    )