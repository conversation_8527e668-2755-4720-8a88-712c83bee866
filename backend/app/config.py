"""
Uygu<PERSON>a yapılandırma modülü
"""

from pydantic_settings import BaseSettings
from functools import lru_cache
import os
from typing import List

class Settings(BaseSettings):
    # Application
    app_name: str = "YKS Genius"
    app_version: str = "1.0.0"
    debug: bool = False
    environment: str = "development"
    
    # API Keys
    gemini_api_key: str = "demo-key-for-hackathon"
    gemini_model_flash: str = "gemini-1.5-flash"
    gemini_model_pro: str = "gemini-1.5-pro"
    
    # Veritabanı
    database_url: str = "sqlite:///./yks_genius.db"
    db_pool_size: int = 10
    db_max_overflow: int = 20
    
    # Redis Önbellek
    redis_url: str = "redis://:yks_redis_password@localhost:6379/0"
    use_redis: bool = True
    redis_max_connections: int = 50
    
    # WebSocket
    ws_heartbeat_interval: int = 30
    ws_max_connections: int = 100
    
    # API Limits
    max_daily_gemini_calls: int = 1400  # Leave buffer den 1500
    gemini_rate_limit_per_minute: int = 30
    cache_ttl: int = 3600  # 1 hour
    max_cache_size: int = 1000
    
    # ChromaDB
    chroma_host: str = "localhost"
    chroma_port: int = 8001
    chroma_collection_name: str = "yks_content"
    
    # Security
    secret_key: str = "your-secret-key-change-this-in-production"
    jwt_secret_key: str = "your-jwt-secret-key-for-authentication"
    admin_key: str = "demo-admin-key"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    # Demo & Hackathon Ayarlar
    demo_mode: bool = True
    hackathon_mode: bool = True
    hackathon_aggressive_cache: bool = True
    hackathon_demo_responses: bool = True
    hackathon_fast_mode: bool = True
    
    # CORS
    cors_origins: List[str] = ["http://localhost:5173", "http://localhost:5174", "https://yks-genius.vercel.app", "file://", "*"]
    
    # Logging
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    class Config:
        env_file = ".env"
        case_sensitive = False

@lru_cache()
def get_settings() -> Settings:
    """Get cached Ayarlar instance"""
    return Settings()

# Oluştur a global Ayarlar instance
settings = get_settings()