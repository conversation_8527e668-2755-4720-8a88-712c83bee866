"""
YKS Curriculum Data Loader
Loads and manages YKS curriculum data for content generation and study planning
"""

import json
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import asyncio
from datetime import datetime

from .vector_store import get_vector_store
from .core.hackathon_optimizations import HackathonOptimizer

logger = logging.getLogger(__name__)

class YKSCurriculumLoader:
    """
    Loads and manages YKS curriculum data

    Features:
    - Load curriculum from JSON file
    - Populate vector store with curriculum content
    - Provide curriculum-based question generation templates
    - Support study plan creation based on curriculum
    """

    def __init__(self):
        self.curriculum_data: Optional[Dict[str, Any]] = None
        self.subjects_loaded = False

    async def load_curriculum(self, force_reload: bool = False) -> bool:
        """
        Load YKS curriculum data from JSON file

        Args:
            force_reload: Force reload even if already loaded

        Returns:
            bool: Success status
        """
        if self.curriculum_data and not force_reload:
            logger.info("Curriculum data already loaded")
            return True

        try:
            # Load curriculum JSON file
            curriculum_file = Path(__file__).parent.parent / "yks_curriculum.json"

            if not curriculum_file.exists():
                logger.error(f"Curriculum file not found: {curriculum_file}")
                return False

            with open(curriculum_file, 'r', encoding='utf-8') as f:
                self.curriculum_data = json.load(f)

            logger.info("✅ YKS curriculum data loaded successfully")

            # Populate vector store with curriculum content
            await self._populate_vector_store()

            self.subjects_loaded = True
            return True

        except Exception as e:
            logger.error(f"Failed to load curriculum: {e}")
            return False

    async def _populate_vector_store(self):
        """Populate vector store with curriculum content"""
        if not self.curriculum_data:
            return

        try:
            vs = get_vector_store()

            # Add topic explanations to vector store
            for exam_type in ["TYT", "AYT"]:
                if exam_type not in self.curriculum_data:
                    continue

                for subject, subject_data in self.curriculum_data[exam_type].items():
                    if "topics" not in subject_data:
                        continue

                    for topic, topic_data in subject_data["topics"].items():
                        # Create explanation content
                        content = f"{subject} - {topic}: "
                        content += f"Bu konu {exam_type} sınavında önemli konulardan biridir. "
                        content += f"Alt konuları: {', '.join(topic_data['subtopics'])}. "

                        # Difficulty distribution info
                        diff_weights = topic_data.get("difficulty_weights", {})
                        if diff_weights:
                            content += f"Zorluk dağılımı - Kolay: %{int(diff_weights.get('kolay', 0)*100)}, "
                            content += f"Orta: %{int(diff_weights.get('orta', 0)*100)}, "
                            content += f"Zor: %{int(diff_weights.get('zor', 0)*100)}"

                        # Add to vector store
                        explanation_id = f"curriculum_{exam_type.lower()}_{subject.lower()}_{topic.lower().replace(' ', '_')}"
                        await vs.add_explanation(
                            explanation_id,
                            content,
                            {
                                "subject": subject.lower(),
                                "topic": topic.lower(),
                                "exam_type": exam_type,
                                "type": "curriculum_explanation",
                                "subtopics": topic_data["subtopics"],
                                "source": "curriculum"
                            }
                        )

            logger.info("✅ Curriculum content added to vector store")

        except Exception as e:
            logger.error(f"Failed to populate vector store with curriculum: {e}")

    def get_subjects(self, exam_type: str = "TYT") -> List[str]:
        """Get list of subjects for exam type"""
        if not self.curriculum_data or exam_type not in self.curriculum_data:
            return []

        # Filter out non-subject keys like study_plan_templates, etc.
        subjects = []
        for key, value in self.curriculum_data[exam_type].items():
            if isinstance(value, dict) and "topics" in value:
                subjects.append(key)
        return subjects

    def get_topics(self, subject: str, exam_type: str = "TYT") -> List[str]:
        """Get list of topics for subject"""
        if not self.curriculum_data or exam_type not in self.curriculum_data:
            return []

        subject_data = self.curriculum_data[exam_type].get(subject, {})
        topics = subject_data.get("topics", {})
        return list(topics.keys())

    def get_subtopics(self, subject: str, topic: str, exam_type: str = "TYT") -> List[str]:
        """Get subtopics for a specific topic"""
        if not self.curriculum_data or exam_type not in self.curriculum_data:
            return []

        subject_data = self.curriculum_data[exam_type].get(subject, {})
        topic_data = subject_data.get("topics", {}).get(topic, {})
        return topic_data.get("subtopics", [])

    def get_difficulty_weights(self, subject: str, topic: str, exam_type: str = "TYT") -> Dict[str, float]:
        """Get difficulty distribution for topic"""
        if not self.curriculum_data or exam_type not in self.curriculum_data:
            return {"kolay": 0.33, "orta": 0.33, "zor": 0.34}

        subject_data = self.curriculum_data[exam_type].get(subject, {})
        topic_data = subject_data.get("topics", {}).get(topic, {})
        return topic_data.get("difficulty_weights", {"kolay": 0.33, "orta": 0.33, "zor": 0.34})

    def get_subject_info(self, subject: str, exam_type: str = "TYT") -> Dict[str, Any]:
        """Get complete subject information including total questions and time"""
        if not self.curriculum_data or exam_type not in self.curriculum_data:
            return {}

        subject_data = self.curriculum_data[exam_type].get(subject, {})
        return {
            "topics": subject_data.get("topics", {}),
            "total_questions": subject_data.get("total_questions", 0),
            "time_minutes": subject_data.get("time_minutes", 0)
        }

    def get_all_subtopics_for_subject(self, subject: str, exam_type: str = "TYT") -> List[str]:
        """Get all subtopics for a subject across all topics"""
        if not self.curriculum_data or exam_type not in self.curriculum_data:
            return []

        subject_data = self.curriculum_data[exam_type].get(subject, {})
        topics = subject_data.get("topics", {})

        all_subtopics = []
        for topic_data in topics.values():
            subtopics = topic_data.get("subtopics", [])
            all_subtopics.extend(subtopics)

        return all_subtopics

    def get_study_plan_template(self, template_name: str = "balanced_20_week") -> Optional[Dict[str, Any]]:
        """Get study plan template"""
        if not self.curriculum_data:
            return None

        templates = self.curriculum_data.get("study_plan_templates", {})
        return templates.get(template_name)

    def get_available_templates(self) -> List[str]:
        """Get list of available study plan templates"""
        if not self.curriculum_data:
            return []

        templates = self.curriculum_data.get("study_plan_templates", {})
        return list(templates.keys())

    def get_performance_benchmark(self, category: str, exam_type: str = "TYT") -> Optional[Dict[str, Any]]:
        """Get performance benchmark for category"""
        if not self.curriculum_data:
            return None

        benchmarks = self.curriculum_data.get("performance_benchmarks", {})
        exam_benchmarks = benchmarks.get(exam_type, {})
        targets = exam_benchmarks.get("target_scores", {})
        return targets.get(category)

    def get_question_pattern(self, subject: str, difficulty: str, exam_type: str = "TYT") -> List[str]:
        """Get question patterns for subject/difficulty"""
        if not self.curriculum_data:
            return []

        patterns = self.curriculum_data.get("question_patterns", {})
        subject_patterns = patterns.get(subject.lower(), {})
        exam_patterns = subject_patterns.get(exam_type, {})

        pattern_key = f"{difficulty.lower()}_patterns"
        return exam_patterns.get(pattern_key, [])

    def generate_study_plan_data(self,
                                student_profile: Dict[str, Any],
                                template_name: str = "balanced_20_week") -> Dict[str, Any]:
        """
        Generate personalized study plan data based on curriculum

        Args:
            student_profile: Student information and preferences
            template_name: Study plan template to use

        Returns:
            Dict with study plan structure
        """
        template = self.get_study_plan_template(template_name)
        if not template:
            return {}

        # Get student preferences
        weak_areas = student_profile.get("weak_areas", [])
        daily_hours = student_profile.get("daily_study_hours", template["daily_hours"])

        # Create personalized plan
        plan_data = {
            "template_used": template_name,
            "student_id": student_profile.get("id", "unknown"),
            "total_weeks": len(template["weekly_structure"]),
            "daily_hours": daily_hours,
            "weekly_plans": []
        }

        # Generate weekly plans
        for week_key, week_data in template["weekly_structure"].items():
            week_plan = {
                "phase": week_key,
                "focus": week_data["focus"],
                "subjects": week_data["subjects"],
                "daily_breakdown": week_data["daily_breakdown"],
                "recommendations": []
            }

            # Add personalized recommendations
            if weak_areas:
                week_plan["recommendations"].append(f"Zayıf olunan konulara odaklan: {', '.join(weak_areas)}")

            plan_data["weekly_plans"].append(week_plan)

        return plan_data

    def get_topic_prerequisites(self, subject: str, topic: str, exam_type: str = "TYT") -> List[str]:
        """Get prerequisite topics for a given topic"""
        # Simple prerequisite mapping (could be enhanced with graph structure)
        prerequisites_map = {
            "matematik": {
                "Türev": ["Limit ve Süreklilik", "Fonksiyonlar"],
                "İntegral": ["Türev", "Fonksiyonlar"],
                "Analitik Geometri": ["Fonksiyonlar", "Temel Kavramlar"],
                "Polinomlar": ["Çarpanlara Ayırma", "Fonksiyonlar"],
                "Permütasyon ve Kombinasyon": ["Temel Kavramlar"],
                "Olasılık": ["Permütasyon ve Kombinasyon"]
            },
            "fizik": {
                "İş, Güç ve Enerji": ["Kuvvet ve Hareket", "Hareket"],
                "Momentum": ["Kuvvet ve Hareket", "Hareket"],
                "Elektrik ve Manyetizma": ["Temel Fizik Kavramları"],
                "Optik": ["Dalgalar"],
                "Atom Fiziği": ["Modern Fizik Temelleri"]
            },
            "kimya": {
                "Kimyasal Bağlar": ["Modern Atom Teorisi"],
                "Kimyasal Tepkimeler": ["Kimyasal Bağlar"],
                "Kimyasal Denge": ["Kimyasal Tepkimeler"],
                "Organik Kimya": ["Kimyasal Bağlar"]
            },
            "biyoloji": {
                "Kalıtım": ["Hücre Biyolojisi"],
                "Hayvan Biyolojisi": ["Hücre Biyolojisi"],
                "Bitki Biyolojisi": ["Hücre Biyolojisi"],
                "Evrim": ["Kalıtım", "Canlılar ve Çevre"]
            }
        }

        # Use exam_type for potential future differentiation
        subject_prereqs = prerequisites_map.get(subject.lower(), {})
        return subject_prereqs.get(topic, [])

    def get_curriculum_statistics(self) -> Dict[str, Any]:
        """Get comprehensive curriculum statistics"""
        if not self.curriculum_data:
            return {}

        stats = {
            "exam_types": list(self.curriculum_data.keys()),
            "total_subjects": 0,
            "total_topics": 0,
            "total_subtopics": 0,
            "subjects_by_exam": {}
        }

        for exam_type in ["TYT", "AYT"]:
            if exam_type not in self.curriculum_data:
                continue

            subjects = self.get_subjects(exam_type)
            stats["subjects_by_exam"][exam_type] = {
                "subjects": subjects,
                "count": len(subjects),
                "topics_count": 0,
                "subtopics_count": 0
            }

            for subject in subjects:
                topics = self.get_topics(subject, exam_type)
                stats["subjects_by_exam"][exam_type]["topics_count"] += len(topics)

                for topic in topics:
                    subtopics = self.get_subtopics(subject, topic, exam_type)
                    stats["subjects_by_exam"][exam_type]["subtopics_count"] += len(subtopics)

            stats["total_subjects"] += len(subjects)
            stats["total_topics"] += stats["subjects_by_exam"][exam_type]["topics_count"]
            stats["total_subtopics"] += stats["subjects_by_exam"][exam_type]["subtopics_count"]

        return stats

    def is_curriculum_loaded(self) -> bool:
        """Check if curriculum is loaded"""
        return self.curriculum_data is not None and self.subjects_loaded

# Global curriculum loader instance
curriculum_loader: Optional[YKSCurriculumLoader] = None

def get_curriculum_loader() -> YKSCurriculumLoader:
    """Get global curriculum loader instance"""
    global curriculum_loader
    if curriculum_loader is None:
        curriculum_loader = YKSCurriculumLoader()
    return curriculum_loader

async def initialize_curriculum():
    """Initialize curriculum loader with data"""
    try:
        loader = get_curriculum_loader()
        success = await loader.load_curriculum()

        if success:
            logger.info("✅ YKS curriculum initialized successfully")
        else:
            logger.error("❌ Failed to initialize YKS curriculum")

        return success

    except Exception as e:
        logger.error(f"Curriculum initialization error: {e}")
        return False