"""
Data Access Layer for YKS Genius
Unified interface for Veritabanı, <PERSON>ek<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ve Müfredat data
"""

import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
import asyncio
from sqlalchemy.orm import Session
from sqlalchemy import desc, and_, or_

from .database import get_db_context
from .models import (
    Student, Question, StudySession, Progress, AgentInteraction,
    QuestionDifficulty, YKSType, SessionType, ProgressType
)
from .vector_store import get_vector_store
from .curriculum_loader import get_curriculum_loader
from .core.cache import get_cache, CachePresets
from .core.hackathon_optimizations import HackathonOptimizer

logger = logging.getLogger(__name__)

class YKSDataAccess:
    """
    Unified data access layer providing:
    - Öğrenci management ile caching
    - Soru generation ve retrieval
    - Performans Analitik
    - Study Oturum tracking
    - Ajan interaction logging
    - Müfredat-based İçerik
    """
    
    def __init__(self):
        self.cache = get_cache()
        self.vector_store = get_vector_store()
        self.curriculum = get_curriculum_loader()
    
    # Öğrenci Management
    @CachePresets.student_data(ttl=1800)
    async def get_student_profile(self, student_id: str) -> Optional[Dict[str, Any]]:
        """Get complete Öğrenci profile ile Performans data"""
        try:
            with get_db_context() as db:
                student = db.query(Student).filter(Student.id == student_id).first()
                if not student:
                    return None
                
                # Get recent Performans data
                recent_progress = db.query(Progress).filter(
                    Progress.student_id == student_id
                ).order_by(desc(Progress.created_at)).limit(10).all()
                
                # Get study sessions
                recent_sessions = db.query(StudySession).filter(
                    StudySession.student_id == student_id
                ).order_by(desc(StudySession.created_at)).limit(5).all()
                
                profile = student.to_dict()
                profile.update({
                    'recent_progress': [p.to_dict() for p in recent_progress],
                    'recent_sessions': [s.to_dict() for s in recent_sessions],
                    'performance_summary': await self._calculate_performance_summary(student_id, db)
                })
                
                return profile
                
        except Exception as e:
            logger.error(f"Error getting student profile {student_id}: {e}")
            return None
    
    async def create_student(self, student_data: Dict[str, Any]) -> Optional[str]:
        """Oluştur new Öğrenci profile"""
        try:
            with get_db_context() as db:
                student = Student(**student_data)
                db.add(student)
                db.commit()
                
                # Invalidate Önbellek
                self.cache.delete_by_tags(["student"])
                
                logger.info(f"Created student profile: {student.id}")
                return str(student.id)
                
        except Exception as e:
            logger.error(f"Error creating student: {e}")
            return None
    
    async def update_student(self, student_id: str, updates: Dict[str, Any]) -> bool:
        """Güncelle Öğrenci profile"""
        try:
            with get_db_context() as db:
                student = db.query(Student).filter(Student.id == student_id).first()
                if not student:
                    return False
                
                student.update_from_dict(updates)
                db.commit()
                
                # Invalidate Önbellek
                self.cache.delete_by_tags(["student"])
                
                return True
                
        except Exception as e:
            logger.error(f"Error updating student {student_id}: {e}")
            return False
    
    # Soru Management
    @CachePresets.vector_search(ttl=1800)
    async def get_questions_for_topic(self, 
                                     subject: str,
                                     topic: str,
                                     difficulty: str = "orta",
                                     count: int = 5,
                                     exam_type: str = "TYT") -> List[Dict[str, Any]]:
        """Get questions for specific topic ile difficulty filtering"""
        try:
            # First try vector search for semantic matching
            query = f"{subject} {topic} {difficulty} seviye soru"
            filters = {
                "subject": subject.lower(),
                "topic": topic.lower(),
                "difficulty": difficulty.lower(),
                "exam_type": exam_type
            }
            
            vector_results = await self.vector_store.search_questions(
                query, filters, limit=count
            )
            
            # eğer we have enough den Vektör Deposu, return those
            if len(vector_results) >= count:
                return vector_results[:count]
            
            # Otherwise, supplement ile Veritabanı queries
            with get_db_context() as db:
                db_questions = db.query(Question).filter(
                    and_(
                        Question.subject == subject.lower(),
                        Question.topic == topic.lower(),
                        Question.difficulty == difficulty.lower(),
                        Question.yks_type == exam_type
                    )
                ).limit(count - len(vector_results)).all()
                
                db_results = [
                    {
                        "id": str(q.id),
                        "content": q.content,
                        "options": q.options,
                        "correct_answer": q.correct_answer,
                        "explanation": q.explanation,
                        "metadata": {
                            "subject": q.subject,
                            "topic": q.topic,
                            "difficulty": q.difficulty,
                            "exam_type": q.yks_type
                        },
                        "similarity_score": 0.5  # Varsayılan score for DB results
                    }
                    for q in db_questions
                ]
                
                return vector_results + db_results
                
        except Exception as e:
            logger.error(f"Error getting questions for {subject}/{topic}: {e}")
            return []
    
    async def save_question(self, question_data: Dict[str, Any]) -> Optional[str]:
        """Kaydet new Soru e Veritabanı ve Vektör Deposu"""
        try:
            with get_db_context() as db:
                question = Question(**question_data)
                db.add(question)
                db.commit()
                
                # Add e Vektör Deposu
                await self.vector_store.add_question(
                    str(question.id),
                    question.content,
                    {
                        "subject": question.subject,
                        "topic": question.topic,
                        "difficulty": question.difficulty,
                        "exam_type": question.yks_type,
                        "tags": question.tags or []
                    }
                )
                
                logger.info(f"Saved question: {question.id}")
                return str(question.id)
                
        except Exception as e:
            logger.error(f"Error saving question: {e}")
            return None
    
    # Performans Analitik
    async def _calculate_performance_summary(self, student_id: str, db: Session) -> Dict[str, Any]:
        """Hesapla Performans summary for Öğrenci"""
        try:
            # Get İlerleme data den last 30 days
            thirty_days_ago = datetime.now() - timedelta(days=30)
            
            progress_data = db.query(Progress).filter(
                and_(
                    Progress.student_id == student_id,
                    Progress.created_at >= thirty_days_ago
                )
            ).all()
            
            if not progress_data:
                return {"status": "no_data"}
            
            # Hesapla Metrikler
            total_questions = sum(p.questions_attempted or 0 for p in progress_data)
            total_correct = sum(p.questions_correct or 0 for p in progress_data)
            accuracy = (total_correct / total_questions * 100) if total_questions > 0 else 0
            
            # Subject breakdown
            subject_performance = {}
            for progress in progress_data:
                if progress.subject:
                    if progress.subject not in subject_performance:
                        subject_performance[progress.subject] = {
                            "questions": 0,
                            "correct": 0,
                            "accuracy": 0
                        }
                    
                    subject_performance[progress.subject]["questions"] += progress.questions_attempted or 0
                    subject_performance[progress.subject]["correct"] += progress.questions_correct or 0
            
            # Hesapla accuracies
            for subject in subject_performance:
                data = subject_performance[subject]
                if data["questions"] > 0:
                    data["accuracy"] = (data["correct"] / data["questions"]) * 100
            
            return {
                "total_questions": total_questions,
                "total_correct": total_correct,
                "overall_accuracy": round(accuracy, 2),
                "subjects": subject_performance,
                "trend": self._calculate_trend(progress_data),
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error calculating performance summary: {e}")
            return {"status": "error", "error": str(e)}
    
    def _calculate_trend(self, progress_data: List[Progress]) -> str:
        """Hesapla Performans trend"""
        if len(progress_data) < 2:
            return "insufficient_data"
        
        # Sort tarafından Tarih
        sorted_data = sorted(progress_data, key=lambda x: x.created_at)
        
        # Compare first half vs second half
        mid_point = len(sorted_data) // 2
        first_half = sorted_data[:mid_point]
        second_half = sorted_data[mid_point:]
        
        def calculate_avg_accuracy(data):
            total_q = sum(p.questions_attempted or 0 for p in data)
            total_c = sum(p.questions_correct or 0 for p in data)
            return (total_c / total_q * 100) if total_q > 0 else 0
        
        first_acc = calculate_avg_accuracy(first_half)
        second_acc = calculate_avg_accuracy(second_half)
        
        diff = second_acc - first_acc
        
        if diff > 5:
            return "improving"
        elif diff < -5:
            return "declining"
        else:
            return "stable"
    
    # Study Oturum Management
    async def start_study_session(self, 
                                 student_id: str,
                                 session_type: str,
                                 subject: str,
                                 topic: Optional[str] = None) -> Optional[str]:
        """Başlat new study Oturum"""
        try:
            with get_db_context() as db:
                session = StudySession(
                    student_id=student_id,
                    session_type=SessionType(session_type),
                    subject=subject,
                    topic=topic,
                    start_time=datetime.now(),
                    status="active"
                )
                db.add(session)
                db.commit()
                
                return str(session.id)
                
        except Exception as e:
            logger.error(f"Error starting study session: {e}")
            return None
    
    async def end_study_session(self, 
                               session_id: str,
                               questions_attempted: int = 0,
                               questions_correct: int = 0,
                               notes: Optional[str] = None) -> bool:
        """End study Oturum ile results"""
        try:
            with get_db_context() as db:
                session = db.query(StudySession).filter(StudySession.id == session_id).first()
                if not session:
                    return False
                
                session.end_time = datetime.now()
                session.questions_attempted = questions_attempted
                session.questions_correct = questions_correct
                session.notes = notes
                session.status = "completed"
                
                # Oluştur İlerleme record
                if questions_attempted > 0:
                    progress = Progress(
                        student_id=session.student_id,
                        subject=session.subject,
                        topic=session.topic,
                        progress_type=ProgressType.PRACTICE,
                        questions_attempted=questions_attempted,
                        questions_correct=questions_correct,
                        accuracy_rate=(questions_correct / questions_attempted) * 100,
                        session_duration=(session.end_time - session.start_time).total_seconds() / 60
                    )
                    db.add(progress)
                
                db.commit()
                
                # Invalidate caches
                self.cache.delete_by_tags(["student"])
                
                return True
                
        except Exception as e:
            logger.error(f"Error ending study session {session_id}: {e}")
            return False
    
    # Ajan Interaction Logging
    async def log_agent_interaction(self,
                                   student_id: str,
                                   agent_type: str,
                                   request: str,
                                   response: str,
                                   interaction_type: str = "chat",
                                   metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """Log Ajan interaction"""
        try:
            with get_db_context() as db:
                interaction = AgentInteraction(
                    student_id=student_id,
                    agent_type=agent_type,
                    interaction_type=interaction_type,
                    request_content=request,
                    response_content=response,
                    metadata=metadata or {},
                    response_time=0.5  # bu would be calculated de real implementation
                )
                db.add(interaction)
                db.commit()
                
                return str(interaction.id)
                
        except Exception as e:
            logger.error(f"Error logging agent interaction: {e}")
            return None
    
    # Müfredat Integration
    @CachePresets.curriculum_data(ttl=7200)
    async def get_curriculum_topics(self, 
                                   subject: str,
                                   exam_type: str = "TYT") -> List[Dict[str, Any]]:
        """Get Müfredat topics ile enhanced metadata"""
        try:
            topics = self.curriculum.get_topics(subject, exam_type)
            
            enhanced_topics = []
            for topic in topics:
                subtopics = self.curriculum.get_subtopics(subject, topic, exam_type)
                difficulty_weights = self.curriculum.get_difficulty_weights(subject, topic, exam_type)
                prerequisites = self.curriculum.get_topic_prerequisites(subject, topic, exam_type)
                
                enhanced_topics.append({
                    "name": topic,
                    "subject": subject,
                    "exam_type": exam_type,
                    "subtopics": subtopics,
                    "difficulty_distribution": difficulty_weights,
                    "prerequisites": prerequisites,
                    "estimated_study_hours": len(subtopics) * 2  # Rough estimate
                })
            
            return enhanced_topics
            
        except Exception as e:
            logger.error(f"Error getting curriculum topics: {e}")
            return []
    
    # İçerik Search Integration
    @CachePresets.vector_search(ttl=900)
    async def search_educational_content(self,
                                        query: str,
                                        content_type: str = "explanation",
                                        subject: Optional[str] = None,
                                        limit: int = 5) -> List[Dict[str, Any]]:
        """Search for educational İçerik karşısında all sources"""
        try:
            results = []
            
            # Search Vektör Deposu
            if content_type == "explanation":
                vector_results = await self.vector_store.search_explanations(
                    query,
                    {"subject": subject.lower()} if subject else None,
                    limit
                )
            else:
                vector_results = await self.vector_store.search_questions(
                    query,
                    {"subject": subject.lower()} if subject else None,
                    limit
                )
            
            results.extend(vector_results)
            
            # Enhance results ile Müfredat context
            for result in results:
                if "metadata" in result and "topic" in result["metadata"]:
                    topic = result["metadata"]["topic"]
                    subject_name = result["metadata"].get("subject", subject)
                    
                    if subject_name:
                        prerequisites = self.curriculum.get_topic_prerequisites(
                            subject_name, topic
                        )
                        result["metadata"]["prerequisites"] = prerequisites
            
            return results
            
        except Exception as e:
            logger.error(f"Error searching educational content: {e}")
            return []
    
    # Health Check
    async def health_check(self) -> Dict[str, Any]:
        """Check health nin all data access components"""
        health_status = {
            "overall": "healthy",
            "components": {}
        }
        
        try:
            # Check Veritabanı
            with get_db_context() as db:
                db.execute("SELECT 1")
                health_status["components"]["database"] = "healthy"
        except Exception as e:
            health_status["components"]["database"] = f"unhealthy: {e}"
            health_status["overall"] = "degraded"
        
        try:
            # Check Vektör Deposu
            vs_health = await self.vector_store.health_check()
            health_status["components"]["vector_store"] = vs_health["status"]
            if vs_health["status"] != "healthy":
                health_status["overall"] = "degraded"
        except Exception as e:
            health_status["components"]["vector_store"] = f"unhealthy: {e}"
            health_status["overall"] = "degraded"
        
        try:
            # Check Önbellek
            cache_stats = self.cache.get_stats()
            health_status["components"]["cache"] = "healthy"
            health_status["cache_stats"] = cache_stats
        except Exception as e:
            health_status["components"]["cache"] = f"unhealthy: {e}"
            health_status["overall"] = "degraded"
        
        try:
            # Check Müfredat
            curriculum_loaded = self.curriculum.is_curriculum_loaded()
            health_status["components"]["curriculum"] = "healthy" if curriculum_loaded else "not_loaded"
            if not curriculum_loaded:
                health_status["overall"] = "degraded"
        except Exception as e:
            health_status["components"]["curriculum"] = f"unhealthy: {e}"
            health_status["overall"] = "degraded"
        
        return health_status

# Global data access instance
_data_access: Optional[YKSDataAccess] = None

def get_data_access() -> YKSDataAccess:
    """Get global data access instance"""
    global _data_access
    if _data_access is None:
        _data_access = YKSDataAccess()
    return _data_access

async def initialize_data_access():
    """Başlat data access layer"""
    try:
        data_access = get_data_access()
        
        # Ensure Müfredat is loaded
        await data_access.curriculum.load_curriculum()
        
        # Run health check
        health = await data_access.health_check()
        
        if health["overall"] == "healthy":
            logger.info("✅ Data access layer initialized successfully")
        else:
            logger.warning(f"⚠️ Data access layer initialized with issues: {health}")
        
        return data_access
        
    except Exception as e:
        logger.error(f"Failed to initialize data access layer: {e}")
        raise