"""
YKS Genius için REST API Rotaları
Öğrenci yönetimi, çalışma planlaması, sorular ve analitik için kapsamlı API uç noktaları
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Body, Path
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import logging
import json
import uuid

from .schemas import (
    # Öğrenci schemas
    StudentCreate, StudentUpdate, StudentProfile,
    # Study plan schemas
    StudyPlanRequest, StudyPlan, StudyPlanWeek,
    # Soru schemas
    QuestionGenerate, QuestionResponse, QuestionsResponse, AnswerSubmit, AnswerResult,
    # Analitik schemas
    PerformanceMetrics, SubjectPerformance,
    # İçerik schemas
    ContentSearchRequest, ContentSearchResponse, ContentItem,
    # Topic schemas
    TopicsResponse, TopicInfo,
    # Oturum schemas
    ActiveSessionsResponse, ActiveSession,
    # Chat schemas
    ChatMessage, ChatResponse,
    # System schemas
    SystemMetrics, AgentListResponse, AgentInfo,
    # Base schemas
    BaseResponse, ErrorResponse,
    # Enums
    YKSExamType, DifficultyLevel, Subject, AgentType
)

from ..dependencies import (
    get_current_student, get_optional_student, get_admin_user,
    get_database, get_gemini_client, get_vector_store, get_orchestrator,
    get_connection_manager, get_cache, validate_rate_limit,
    PaginationParams, get_pagination, SearchParams, get_search_params
)

logger = logging.getLogger(__name__)
router = APIRouter()


# Öğrenci Management Endpoints
@router.post("/students", response_model=StudentProfile, tags=["Students"])
async def create_student(
    student_data: StudentCreate,
    db=Depends(get_database)
):
    """Oluştur new Öğrenci profile"""
    try:
        # Üret unique Öğrenci Kimlik
        student_id = str(uuid.uuid4())

        # Oluştur Öğrenci record (placeholder for Veritabanı implementation)
        student_dict = student_data.dict()
        student_dict.update({
            "id": student_id,
            "is_active": True,
            "current_level": "Başlangıç",
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        })

        # TODO: Kaydet e Veritabanı
        logger.info(f"Created student profile: {student_id}")

        return StudentProfile(**student_dict)
    except Exception as e:
        logger.error(f"Error creating student: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@router.get("/students/{student_id}", response_model=StudentProfile, tags=["Students"])
async def get_student(
    student_id: str = Path(..., description="Student ID"),
    demo_mode: bool = Query(True, description="Enable demo mode"),
    db=Depends(get_database)
):
    """Get Öğrenci profile"""
    try:
        # For demo mode, return demo Öğrenci data
        if demo_mode:
            student_data = {
                "id": student_id,
                "name": "Demo Öğrenci",
                "email": "<EMAIL>",
                "grade_level": 12,
                "target_university": "İTÜ",
                "target_department": "Bilgisayar Mühendisliği",
                "target_score": 450,
                "is_active": True,
                "current_level": "Orta",
                "created_at": datetime.now() - timedelta(days=30),
                "updated_at": datetime.now()
            }
        else:
            # TODO: Getir den Veritabanı
            student_data = {
                "id": student_id,
                "name": f"Öğrenci {student_id[-4:]}",
                "email": f"student{student_id[-4:]}@example.com",
                "grade_level": 12,
                "is_active": True,
                "current_level": "Başlangıç",
                "created_at": datetime.now() - timedelta(days=30),
                "updated_at": datetime.now()
            }

        return StudentProfile(**student_data)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching student {student_id}: {e}")
        raise HTTPException(status_code=404, detail="Student not found")


@router.put("/students/{student_id}", response_model=StudentProfile, tags=["Students"])
async def update_student(
    student_id: str = Path(..., description="Student ID"),
    updates: StudentUpdate = Body(...),
    demo_mode: bool = Query(True, description="Enable demo mode"),
    db=Depends(get_database)
):
    """Güncelle Öğrenci profile"""
    try:
        # For demo mode, allow any updates
        if not demo_mode:
            raise HTTPException(status_code=403, detail="Updates only allowed in demo mode")

        # TODO: Güncelle de Veritabanı
        updated_data = {
            "id": student_id,
            "name": "Demo Öğrenci",
            "email": "<EMAIL>",
            "grade_level": 12,
            **{k: v for k, v in updates.dict().items() if v is not None},
            "updated_at": datetime.now()
        }

        logger.info(f"Updated student profile: {student_id}")
        return StudentProfile(**updated_data)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating student {student_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))


# Study Plan Endpoints
@router.get("/students/{student_id}/study-plan", response_model=StudyPlan, tags=["Study Plans"])
async def get_study_plan(
    student_id: str = Path(..., description="Student ID"),
    demo_mode: bool = Query(True, description="Enable demo mode"),
    db=Depends(get_database)
):
    """Get Öğrenci's Akım study plan"""
    try:
        # For demo mode, return sample study plan
        if not demo_mode:
            raise HTTPException(status_code=403, detail="Access only allowed in demo mode")

        # TODO: Getir den Veritabanı
        # For demo, return a sample study plan
        sample_weeks = [
            StudyPlanWeek(
                week_number=1,
                focus_subjects=[Subject.MATEMATIK, Subject.FIZIK],
                daily_schedule={
                    "Pazartesi": ["Limit ve Süreklilik", "Hareket"],
                    "Salı": ["Türev", "Kuvvet"],
                    "Çarşamba": ["İntegral", "İş ve Enerji"],
                    "Perşembe": ["Fonksiyonlar", "Momentum"],
                    "Cuma": ["Geometri", "Elektrik"],
                    "Cumartesi": ["Deneme Sınavı"],
                    "Pazar": ["Tekrar"]
                },
                goals=["Limit konusunu bitir", "Newton yasalarını öğren"],
                estimated_hours=25
            )
        ]

        plan_data = {
            "id": str(uuid.uuid4()),
            "student_id": student_id,
            "title": "12. Sınıf YKS Hazırlık Planı",
            "description": "Kişiselleştirilmiş 12 haftalık YKS hazırlık programı",
            "duration_weeks": 12,
            "total_hours": 300,
            "weekly_plans": sample_weeks,
            "created_by_agent": AgentType.STRATEGY,
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "success": True,
            "timestamp": datetime.now()
        }

        return StudyPlan(**plan_data)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching study plan for {student_id}: {e}")
        raise HTTPException(status_code=404, detail="Study plan not found")


@router.post("/students/{student_id}/study-plan/generate", response_model=StudyPlan, tags=["Study Plans"])
async def generate_study_plan(
    student_id: str = Path(..., description="Student ID"),
    plan_request: StudyPlanRequest = Body(...),
    demo_mode: bool = Query(True, description="Enable demo mode"),
    orchestrator=Depends(get_orchestrator),
    db=Depends(get_database)
):
    """Üret personalized study plan using Strateji Ajan"""
    try:
        # For demo mode, Üret sample study plan
        if not demo_mode:
            raise HTTPException(status_code=403, detail="Access only allowed in demo mode")

        # Use Orkestratör e Üret study plan
        agent_request = {
            "student_id": student_id,
            "message": f"Bana {plan_request.duration_weeks} haftalık çalışma planı oluştur",
            "request_type": "study_plan",
            "context": {
                "duration_weeks": plan_request.duration_weeks,
                "daily_hours": plan_request.daily_hours,
                "focus_subjects": [s.value for s in plan_request.focus_subjects],
                "weak_areas": plan_request.weak_areas,
                "preferences": plan_request.preferences
            }
        }

        response = await orchestrator.process_student_request(**agent_request)

        if response.get("success"):
            # Çevir Ajan Yanıt e study plan Biçimlendir
            plan_data = {
                "id": str(uuid.uuid4()),
                "student_id": student_id,
                "title": "AI Destekli Çalışma Planı",
                "description": response.get("message", ""),
                "duration_weeks": plan_request.duration_weeks,
                "total_hours": plan_request.duration_weeks * plan_request.daily_hours * 7,
                "weekly_plans": [],  # TODO: Ayrıştır den Ajan Yanıt
                "created_by_agent": AgentType.STRATEGY,
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "success": True,
                "timestamp": datetime.now()
            }

            # TODO: Kaydet e Veritabanı
            return StudyPlan(**plan_data)
        else:
            raise HTTPException(status_code=400, detail="Failed to generate study plan")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating study plan for {student_id}: {e}")
        raise HTTPException(status_code=500, detail="Study plan generation failed")


# Soru Generation Endpoints
@router.post("/questions/generate", response_model=QuestionsResponse, tags=["Questions"])
async def generate_questions(
    question_request: QuestionGenerate = Body(...),
    current_student: Dict = Depends(get_current_student),
    orchestrator=Depends(get_orchestrator),
    _rate_limit=Depends(validate_rate_limit)
):
    """Üret Pratik questions using Pratik Ajan"""
    try:
        # Use Orkestratör e Üret questions
        agent_request = {
            "student_id": current_student["id"],
            "message": f"Bana {question_request.count} adet {question_request.subject.value} sorusu sor",
            "request_type": "practice",
            "context": {
                "subject": question_request.subject.value,
                "topic": question_request.topic,
                "difficulty": question_request.difficulty.value,
                "exam_type": question_request.exam_type.value,
                "count": question_request.count
            }
        }

        response = await orchestrator.process_student_request(**agent_request)

        if response.get("success") and response.get("data", {}).get("questions"):
            questions_data = response["data"]["questions"]

            # Çevir e API Biçimlendir
            questions = []
            for i, q_data in enumerate(questions_data):
                question = QuestionResponse(
                    id=str(uuid.uuid4()),
                    question=q_data.get("question", ""),
                    options=[
                        {"key": k, "text": v}
                        for k, v in q_data.get("options", {}).items()
                    ],
                    correct_answer=q_data.get("correct_answer", "A"),
                    explanation=q_data.get("explanation", ""),
                    solution_steps=q_data.get("solution_steps", []),
                    common_mistakes=q_data.get("common_mistakes", []),
                    tips=q_data.get("tips"),
                    subject=question_request.subject,
                    topic=q_data.get("topic", question_request.topic or "Genel"),
                    difficulty=DifficultyLevel(q_data.get("difficulty", question_request.difficulty.value)),
                    exam_type=question_request.exam_type,
                    estimated_time=q_data.get("estimated_time", 120),
                    yks_frequency=q_data.get("yks_frequency", "orta"),
                    created_at=datetime.now(),
                    success=True,
                    timestamp=datetime.now()
                )
                questions.append(question)

            return QuestionsResponse(
                questions=questions,
                total=len(questions),
                subject=question_request.subject,
                quiz_mode=question_request.count > 1,
                success=True,
                timestamp=datetime.now()
            )
        else:
            raise HTTPException(status_code=400, detail="Failed to generate questions")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating questions: {e}")
        raise HTTPException(status_code=500, detail="Question generation failed")


@router.post("/questions/{question_id}/answer", response_model=AnswerResult, tags=["Questions"])
async def submit_answer(
    question_id: str = Path(..., description="Question ID"),
    answer: AnswerSubmit = Body(...),
    current_student: Dict = Depends(get_current_student),
    db=Depends(get_database)
):
    """Submit Cevap for evaluation ve Kaydet İstatistikler"""
    try:
        # TODO: Getir Soru den Veritabanı
        # For demo, Oluştur sample Soru data
        sample_questions = {
            "math_001": {
                "correct_answer": "B",
                "explanation": "Limit değeri x=2'ye yaklaşırken f(x)=4 olur.",
                "topic": "Limit",
                "difficulty": "orta"
            },
            "physics_001": {
                "correct_answer": "C",
                "explanation": "Newton'un ikinci yasasına göre F=ma'dir.",
                "topic": "Kuvvet",
                "difficulty": "kolay"
            }
        }

        # Get Soru data (fallback e sample)
        question_data = sample_questions.get(question_id, {
            "correct_answer": "A",
            "explanation": "Açıklama mevcut değil.",
            "topic": "Genel",
            "difficulty": "orta"
        })

        # Evaluate Cevap
        is_correct = answer.student_answer.upper() == question_data["correct_answer"]

        # Hesapla Zaman Performans
        expected_time = 120  # seconds
        time_performance = {
            "status": "ideal" if answer.time_spent <= expected_time else
                     "hızlı" if answer.time_spent < expected_time * 0.7 else "yavaş",
            "efficiency": min(100, (expected_time / max(answer.time_spent, 1)) * 100)
        }

        # Hesapla score
        base_score = 100 if is_correct else 0
        time_bonus = max(0, (expected_time - answer.time_spent) / expected_time * 20) if is_correct else 0
        final_score = min(120, base_score + time_bonus)

        result_data = {
            "is_correct": is_correct,
            "correct_answer": question_data["correct_answer"],
            "explanation": question_data["explanation"],
            "time_performance": time_performance,
            "score": round(final_score),
            "feedback": "Tebrikler! Doğru cevap." if is_correct else "Yanlış cevap. Tekrar deneyin.",
            "next_difficulty": DifficultyLevel.ZOR if is_correct else DifficultyLevel.KOLAY,
            "similar_questions": [str(uuid.uuid4()) for _ in range(3)],
            "success": True,
            "timestamp": datetime.now()
        }

        # Kaydet Cevap İstatistikler (simulate Veritabanı Kaydet)
        answer_record = {
            "id": str(uuid.uuid4()),
            "question_id": question_id,
            "student_id": current_student["id"],
            "student_answer": answer.student_answer,
            "is_correct": is_correct,
            "time_spent": answer.time_spent,
            "score": final_score,
            "submitted_at": datetime.now(),
            "session_id": getattr(answer, 'session_id', None)
        }

        logger.info(f"Answer submitted: {answer_record}")

        # TODO: Kaydet e actual Veritabanı
        # TODO: Güncelle Öğrenci Performans Metrikler
        # TODO: Güncelle Soru İstatistikler

        return AnswerResult(**result_data)

    except Exception as e:
        logger.error(f"Error submitting answer for question {question_id}: {e}")
        raise HTTPException(status_code=400, detail="Answer submission failed")


# Analitik Endpoints
@router.get("/students/{student_id}/analytics", response_model=PerformanceMetrics, tags=["Analytics"])
async def get_analytics(
    student_id: str = Path(..., description="Student ID"),
    period: str = Query("week", pattern="^(day|week|month|all)$", description="Analytics period"),
    demo_mode: bool = Query(True, description="Enable demo mode"),
    db=Depends(get_database)
):
    """Get Öğrenci Performans Analitik"""
    try:
        # For demo mode, return sample Analitik
        if not demo_mode:
            raise HTTPException(status_code=403, detail="Access only allowed in demo mode")

        # TODO: Hesapla real Analitik den Veritabanı
        # For demo, return sample Analitik

        subjects_performance = [
            SubjectPerformance(
                subject=Subject.MATEMATIK,
                total_questions=50,
                correct_answers=35,
                accuracy_rate=70.0,
                average_time=95.5,
                improvement_trend=5.2,
                weak_topics=["İntegral", "Logaritma"],
                strong_topics=["Limit", "Türev"]
            ),
            SubjectPerformance(
                subject=Subject.FIZIK,
                total_questions=30,
                correct_answers=20,
                accuracy_rate=66.7,
                average_time=105.0,
                improvement_trend=2.1,
                weak_topics=["Modern Fizik"],
                strong_topics=["Hareket", "Kuvvet"]
            )
        ]

        analytics_data = {
            "student_id": student_id,
            "period": period,
            "overall_accuracy": 68.5,
            "total_questions_answered": 80,
            "total_study_time": 1200,  # minutes
            "subjects": subjects_performance,
            "daily_progress": [
                {"date": "2024-01-01", "questions": 10, "accuracy": 75},
                {"date": "2024-01-02", "questions": 8, "accuracy": 62.5},
                {"date": "2024-01-03", "questions": 12, "accuracy": 83.3}
            ],
            "achievements": ["İlk 50 soruyu tamamladı", "7 gün üst üste çalıştı"],
            "recommendations": [
                "İntegral konusuna daha fazla odaklanın",
                "Günlük çalışma süresini artırın"
            ],
            "estimated_yks_score": 380,
            "success": True,
            "timestamp": datetime.now()
        }

        return PerformanceMetrics(**analytics_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching analytics for {student_id}: {e}")
        raise HTTPException(status_code=500, detail="Analytics fetch failed")


# İçerik Search Endpoints
@router.get("/content/search", response_model=ContentSearchResponse, tags=["Content"])
async def search_content(
    search_params: SearchParams = Depends(get_search_params),
    subject: Optional[Subject] = Query(None, description="Filter by subject"),
    exam_type: YKSExamType = Query(YKSExamType.TYT, description="Exam type"),
    limit: int = Query(5, ge=1, le=20, description="Number of results"),
    current_student: Dict = Depends(get_optional_student),
    vector_store=Depends(get_vector_store)
):
    """Search educational İçerik"""
    try:
        if not search_params.query:
            raise HTTPException(status_code=400, detail="Search query required")

        # TODO: Implement vector search
        # For demo, return sample İçerik
        sample_results = [
            ContentItem(
                id=str(uuid.uuid4()),
                title="Limit Kavramı",
                content="Limit, bir fonksiyonun belirli bir noktaya yaklaşırken aldığı değerdir...",
                subject=Subject.MATEMATIK,
                topic="Limit",
                exam_type=exam_type,
                relevance_score=0.95,
                content_type="explanation"
            ),
            ContentItem(
                id=str(uuid.uuid4()),
                title="Limit Örnekleri",
                content="Örnek 1: lim(x→2) (x²-4)/(x-2) = ?",
                subject=Subject.MATEMATIK,
                topic="Limit",
                exam_type=exam_type,
                relevance_score=0.87,
                content_type="example"
            )
        ]

        search_response = {
            "query": search_params.query,
            "results": sample_results[:limit],
            "total_found": len(sample_results),
            "search_time": 0.15,
            "success": True,
            "timestamp": datetime.now()
        }

        return ContentSearchResponse(**search_response)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error searching content: {e}")
        raise HTTPException(status_code=500, detail="Content search failed")


# Topic Management Endpoints
@router.get("/topics", response_model=TopicsResponse, tags=["Topics"])
async def get_topics(
    exam_type: YKSExamType = Query(YKSExamType.TYT, description="Exam type"),
    subject: Optional[Subject] = Query(None, description="Filter by subject"),
    current_student: Dict = Depends(get_optional_student),
    db=Depends(get_database)
):
    """Get Mevcut topics for study"""
    try:
        # TODO: Getir den Veritabanı
        # For demo, return sample topics
        sample_topics = [
            TopicInfo(
                name="Limit ve Süreklilik",
                subject=Subject.MATEMATIK,
                exam_type=exam_type,
                question_count=45,
                average_difficulty=3.2,
                completion_rate=0.75 if current_student else None
            ),
            TopicInfo(
                name="Türev",
                subject=Subject.MATEMATIK,
                exam_type=exam_type,
                question_count=52,
                average_difficulty=3.8,
                completion_rate=0.60 if current_student else None
            ),
            TopicInfo(
                name="Hareket",
                subject=Subject.FIZIK,
                exam_type=exam_type,
                question_count=35,
                average_difficulty=2.9,
                completion_rate=0.80 if current_student else None
            )
        ]

        # Filter tarafından subject eğer specified
        if subject:
            sample_topics = [t for t in sample_topics if t.subject == subject]

        return TopicsResponse(
            exam_type=exam_type,
            subject=subject,
            topics=sample_topics,
            total=len(sample_topics),
            success=True,
            timestamp=datetime.now()
        )

    except Exception as e:
        logger.error(f"Error fetching topics: {e}")
        raise HTTPException(status_code=500, detail="Topics fetch failed")


# Oturum Management Endpoints
@router.get("/sessions/active", response_model=ActiveSessionsResponse, tags=["Sessions"])
async def get_active_sessions(
    current_student: Dict = Depends(get_current_student),
    connection_manager=Depends(get_connection_manager)
):
    """Get Aktif study sessions"""
    try:
        # Get Aktif sessions den connection manager
        active_connections = connection_manager.get_connection_info()

        sessions = []
        for student_id in active_connections.get("students", []):
            session = ActiveSession(
                student_id=student_id,
                session_start=datetime.now() - timedelta(minutes=30),
                current_activity="Soru çözme",
                subject=Subject.MATEMATIK,
                topic="Limit",
                questions_answered=5,
                time_spent=30,
                connection_status="connected"
            )
            sessions.append(session)

        return ActiveSessionsResponse(
            sessions=sessions,
            total_active=len(sessions),
            success=True,
            timestamp=datetime.now()
        )

    except Exception as e:
        logger.error(f"Error fetching active sessions: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch active sessions")


# Administrative Endpoints
@router.get("/admin/metrics", response_model=SystemMetrics, tags=["Admin"])
async def get_system_metrics(
    admin_user: Dict = Depends(get_admin_user),
    gemini_client=Depends(get_gemini_client),
    connection_manager=Depends(get_connection_manager),
    db=Depends(get_database)
):
    """Get comprehensive system Metrikler (Admin only)"""
    try:
        # Gather system Metrikler
        metrics_data = {
            "active_connections": connection_manager.get_connection_count(),
            "total_students": 1250,  # TODO: Sayım den Veritabanı
            "questions_generated_today": 2340,  # TODO: Sayım den Veritabanı
            "average_response_time": 0.85,
            "gemini_usage": gemini_client.get_usage_stats(),
            "cache_stats": {"hit_rate": "87%", "size": "245MB"},
            "database_status": "healthy",
            "uptime": "5 days, 12 hours",
            "success": True,
            "timestamp": datetime.now()
        }

        return SystemMetrics(**metrics_data)

    except Exception as e:
        logger.error(f"Error fetching system metrics: {e}")
        raise HTTPException(status_code=500, detail="Metrics fetch failed")


@router.get("/agents", response_model=AgentListResponse, tags=["Agents"])
async def get_agents(
    current_student: Dict = Depends(get_optional_student),
    orchestrator=Depends(get_orchestrator)
):
    """Get Mevcut AI agents information"""
    try:
        agents = [
            AgentInfo(
                type=AgentType.STRATEGY,
                name="Strateji Uzmanı",
                description="Kişiselleştirilmiş çalışma planları oluşturur",
                status="active",
                capabilities=["study_planning", "goal_setting", "time_management"],
                performance_metrics={"response_time": "0.8s", "accuracy": "94%"}
            ),
            AgentInfo(
                type=AgentType.CONTENT,
                name="Konu Anlatım Uzmanı",
                description="Konuları anlaşılır şekilde açıklar",
                status="active",
                capabilities=["content_explanation", "examples", "concept_clarification"],
                performance_metrics={"response_time": "1.2s", "satisfaction": "96%"}
            ),
            AgentInfo(
                type=AgentType.PRACTICE,
                name="Pratik Uzmanı",
                description="Adaptif soru üretimi ve değerlendirme",
                status="active",
                capabilities=["question_generation", "answer_evaluation", "difficulty_adaptation"],
                performance_metrics={"questions_generated": "15.2k", "accuracy": "92%"}
            ),
            AgentInfo(
                type=AgentType.ANALYTICS,
                name="Analiz Uzmanı",
                description="Performans analizi ve öneriler",
                status="active",
                capabilities=["performance_tracking", "trend_analysis", "recommendations"],
                performance_metrics={"insights_generated": "3.4k", "prediction_accuracy": "89%"}
            ),
            AgentInfo(
                type=AgentType.MENTOR,
                name="Motivasyon Koçu",
                description="Motivasyon ve rehberlik desteği",
                status="active",
                capabilities=["motivation_support", "guidance", "stress_management"],
                performance_metrics={"student_engagement": "91%", "satisfaction": "97%"}
            )
        ]

        return AgentListResponse(
            agents=agents,
            total=len(agents),
            success=True,
            timestamp=datetime.now()
        )

    except Exception as e:
        logger.error(f"Error fetching agents: {e}")
        raise HTTPException(status_code=500, detail="Agents fetch failed")


# Chat Endpoints (extending existing functionality)
@router.post("/chat", response_model=ChatResponse, tags=["Chat"])
async def enhanced_chat(
    message: ChatMessage = Body(...),
    current_student: Dict = Depends(get_current_student),
    orchestrator=Depends(get_orchestrator),
    _rate_limit=Depends(validate_rate_limit)
):
    """Enhanced chat endpoint ile full Ajan integration"""
    try:
        # İşle boyunca Orkestratör
        response = await orchestrator.process_student_request(
            student_id=current_student["id"],
            message=message.content,
            request_type=message.agent_type.value if message.agent_type else None,
            context=message.context
        )

        if response.get("success"):
            return ChatResponse(
                message=response.get("message", ""),
                agent=AgentType(response.get("agent", "content")),
                data=response.get("data", {}),
                suggestions=response.get("suggestions", []),
                next_action=response.get("next_action"),
                success=True,
                timestamp=datetime.now()
            )
        else:
            raise HTTPException(status_code=400, detail="Chat processing failed")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in enhanced chat: {e}")
        raise HTTPException(status_code=500, detail="Chat service unavailable")


# Webhook Endpoints
@router.post("/webhooks/study-reminder", tags=["Webhooks"])
async def study_reminder_webhook(
    payload: Dict = Body(...),
    db=Depends(get_database)
):
    """İşle study reminder webhooks"""
    try:
        # İşle webhook payload
        logger.info(f"Received study reminder webhook: {payload}")

        # TODO: İşle reminder logic
        # Gönder notifications, Güncelle study schedules, etc.

        return {"status": "processed", "timestamp": datetime.now()}

    except Exception as e:
        logger.error(f"Error processing webhook: {e}")
        raise HTTPException(status_code=400, detail="Webhook processing failed")
