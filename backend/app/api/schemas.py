"""
YKS Genius için API Şemaları
İstek/yanıt doğrulama ve serileştirme için Pydantic modelleri
"""

from pydantic import BaseModel, Field, validator
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, date
from enum import Enum


class YKSExamType(str, Enum):
    """YKS Sınav Türleri"""
    TYT = "TYT"
    AYT = "AYT"
    YDT = "YDT"


class DifficultyLevel(str, Enum):
    """Soru Zorluk Seviyeleri"""
    KOLAY = "kolay"
    ORTA = "orta"
    ZOR = "zor"
    COK_ZOR = "çok_zor"


class Subject(str, Enum):
    """Akademik Dersler"""
    MATEMATIK = "matematik"
    FIZIK = "fizik"
    KIMYA = "kimya"
    BIYOLOJI = "biyoloji"
    TURKCE = "türkçe"
    TARIH = "tarih"
    COGRAFYA = "coğrafya"
    FELSEFE = "felsefe"
    INGILIZCE = "ingilizce"


class AgentType(str, Enum):
    """A<PERSON>"""
    STRATEGY = "strategy"
    CONTENT = "content"
    PRACTICE = "practice"
    ANALYTICS = "analytics"
    MENTOR = "mentor"


# Temel Yanıt Modelleri
class BaseResponse(BaseModel):
    """Temel Yanıt Modeli"""
    success: bool = True
    timestamp: datetime = Field(default_factory=datetime.now)


class ErrorResponse(BaseResponse):
    """Hata Yanıt Modeli"""
    success: bool = False
    error: str
    details: Optional[str] = None


class HealthResponse(BaseResponse):
    """Sağlık kontrolü yanıtı"""
    status: str
    version: str = "1.0.0"
    services: Dict[str, Any] = {}


# Öğrenci Modelleri
class StudentCreate(BaseModel):
    """Öğrenci oluşturma isteği"""
    name: str = Field(..., min_length=2, max_length=100)
    email: Optional[str] = Field(None, pattern=r'^[^@]+@[^@]+\.[^@]+$')
    grade_level: int = Field(..., ge=9, le=12)
    target_university: Optional[str] = Field(None, max_length=200)
    target_department: Optional[str] = Field(None, max_length=200)
    target_score: Optional[int] = Field(None, ge=100, le=560)
    study_preferences: Optional[Dict[str, Any]] = Field(default_factory=dict)


class StudentUpdate(BaseModel):
    """Öğrenci Güncelle İstek"""
    name: Optional[str] = Field(None, min_length=2, max_length=100)
    email: Optional[str] = Field(None, pattern=r'^[^@]+@[^@]+\.[^@]+$')
    target_university: Optional[str] = Field(None, max_length=200)
    target_department: Optional[str] = Field(None, max_length=200)
    target_score: Optional[int] = Field(None, ge=100, le=560)
    study_preferences: Optional[Dict[str, Any]] = None


class StudentProfile(BaseResponse):
    """Öğrenci profile Yanıt"""
    id: str
    name: str
    email: Optional[str]
    grade_level: int
    target_university: Optional[str]
    target_department: Optional[str]
    target_score: Optional[int]
    study_preferences: Dict[str, Any]
    current_level: Optional[str]
    is_active: bool
    created_at: datetime
    updated_at: datetime


# Study Plan Models
class StudyPlanRequest(BaseModel):
    """Study plan generation İstek"""
    duration_weeks: int = Field(12, ge=1, le=52)
    daily_hours: int = Field(4, ge=1, le=12)
    focus_subjects: List[Subject] = Field(default_factory=list)
    weak_areas: List[str] = Field(default_factory=list)
    preferences: Optional[Dict[str, Any]] = Field(default_factory=dict)


class StudyPlanWeek(BaseModel):
    """Weekly study plan"""
    week_number: int
    focus_subjects: List[Subject]
    daily_schedule: Dict[str, List[str]]  # day -> topics
    goals: List[str]
    estimated_hours: int


class StudyPlan(BaseResponse):
    """Study plan Yanıt"""
    id: str
    student_id: str
    title: str
    description: str
    duration_weeks: int
    total_hours: int
    weekly_plans: List[StudyPlanWeek]
    created_by_agent: AgentType
    created_at: datetime
    updated_at: datetime


# Soru Models
class QuestionOption(BaseModel):
    """Soru option"""
    key: str = Field(..., pattern=r'^[A-E]$')
    text: str = Field(..., min_length=1, max_length=500)


class QuestionGenerate(BaseModel):
    """Soru generation İstek"""
    subject: Subject
    topic: Optional[str] = None
    difficulty: DifficultyLevel = DifficultyLevel.ORTA
    exam_type: YKSExamType = YKSExamType.TYT
    count: int = Field(1, ge=1, le=10)


class QuestionResponse(BaseResponse):
    """Single Soru Yanıt"""
    id: str
    question: str
    options: List[QuestionOption]
    correct_answer: str = Field(..., pattern=r'^[A-E]$')
    explanation: str
    solution_steps: List[str] = Field(default_factory=list)
    common_mistakes: List[str] = Field(default_factory=list)
    tips: Optional[str] = None
    subject: Subject
    topic: str
    difficulty: DifficultyLevel
    exam_type: YKSExamType
    estimated_time: int  # de seconds
    yks_frequency: str  # düşük/orta/yüksek
    created_at: datetime


class QuestionsResponse(BaseResponse):
    """Multiple questions Yanıt"""
    questions: List[QuestionResponse]
    total: int
    subject: Subject
    quiz_mode: bool = False


# Cevap Models
class AnswerSubmit(BaseModel):
    """Cevap submission"""
    student_answer: str = Field(..., pattern=r'^[A-E]$')
    time_spent: int = Field(..., ge=1)  # de seconds
    confidence_level: Optional[int] = Field(None, ge=1, le=5)


class AnswerResult(BaseResponse):
    """Cevap evaluation result"""
    is_correct: bool
    correct_answer: str
    explanation: str
    time_performance: Dict[str, Any]
    score: int
    feedback: str
    next_difficulty: DifficultyLevel
    similar_questions: List[str] = Field(default_factory=list)


# Analitik Models
class SubjectPerformance(BaseModel):
    """Subject Performans Metrikler"""
    subject: Subject
    total_questions: int
    correct_answers: int
    accuracy_rate: float = Field(..., ge=0, le=100)
    average_time: float  # de seconds
    improvement_trend: float  # percentage change
    weak_topics: List[str] = Field(default_factory=list)
    strong_topics: List[str] = Field(default_factory=list)


class PerformanceMetrics(BaseResponse):
    """Comprehensive Performans Analitik"""
    student_id: str
    period: str  # day/week/month/all
    overall_accuracy: float = Field(..., ge=0, le=100)
    total_questions_answered: int
    total_study_time: int  # de minutes
    subjects: List[SubjectPerformance]
    daily_progress: List[Dict[str, Any]] = Field(default_factory=list)
    achievements: List[str] = Field(default_factory=list)
    recommendations: List[str] = Field(default_factory=list)
    estimated_yks_score: Optional[int] = None


# İçerik Models
class ContentSearchRequest(BaseModel):
    """İçerik search İstek"""
    query: str = Field(..., min_length=2, max_length=200)
    subject: Optional[Subject] = None
    topic: Optional[str] = None
    exam_type: YKSExamType = YKSExamType.TYT
    limit: int = Field(5, ge=1, le=20)


class ContentItem(BaseModel):
    """İçerik search result item"""
    id: str
    title: str
    content: str
    subject: Subject
    topic: str
    exam_type: YKSExamType
    relevance_score: float = Field(..., ge=0, le=1)
    content_type: str  # explanation/example/formula/concept


class ContentSearchResponse(BaseResponse):
    """İçerik search results"""
    query: str
    results: List[ContentItem]
    total_found: int
    search_time: float  # de seconds


# Topic Models
class TopicInfo(BaseModel):
    """Topic information"""
    name: str
    subject: Subject
    exam_type: YKSExamType
    question_count: int
    average_difficulty: float
    completion_rate: Optional[float] = None  # for logged-de students


class TopicsResponse(BaseResponse):
    """Topics listing Yanıt"""
    exam_type: YKSExamType
    subject: Optional[Subject]
    topics: List[TopicInfo]
    total: int


# Oturum Models
class ActiveSession(BaseModel):
    """Aktif study Oturum"""
    student_id: str
    session_start: datetime
    current_activity: str
    subject: Optional[Subject]
    topic: Optional[str]
    questions_answered: int
    time_spent: int  # de minutes
    connection_status: str


class ActiveSessionsResponse(BaseResponse):
    """Aktif sessions Yanıt"""
    sessions: List[ActiveSession]
    total_active: int


# Chat Models (extending existing)
class ChatMessage(BaseModel):
    """Chat message"""
    content: str = Field(..., min_length=1, max_length=2000)
    agent_type: Optional[AgentType] = None
    context: Optional[Dict[str, Any]] = Field(default_factory=dict)


class ChatResponse(BaseResponse):
    """Chat Yanıt"""
    message: str
    agent: AgentType
    data: Optional[Dict[str, Any]] = Field(default_factory=dict)
    suggestions: List[str] = Field(default_factory=list)
    next_action: Optional[str] = None


# System Models
class SystemMetrics(BaseResponse):
    """System Metrikler"""
    active_connections: int
    total_students: int
    questions_generated_today: int
    average_response_time: float
    gemini_usage: Dict[str, Any]
    cache_stats: Dict[str, Any]
    database_status: str
    uptime: str


class WebhookPayload(BaseModel):
    """Generic webhook payload"""
    event: str
    data: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.now)
    signature: Optional[str] = None


# Ajan-specific Models
class AgentInfo(BaseModel):
    """Ajan information"""
    type: AgentType
    name: str
    description: str
    status: str
    capabilities: List[str]
    performance_metrics: Optional[Dict[str, Any]] = Field(default_factory=dict)


class AgentListResponse(BaseResponse):
    """Agents listing"""
    agents: List[AgentInfo]
    total: int


# Validation helpers
@validator('email', pre=True, always=True)
def validate_email(cls, v):
    """Doğrula email Biçimlendir"""
    if v and '@' not in v:
        raise ValueError('Invalid email format')
    return v


@validator('target_score', pre=True, always=True)
def validate_target_score(cls, v):
    """Doğrula YKS target score"""
    if v is not None and (v < 100 or v > 560):
        raise ValueError('Target score must be between 100 and 560')
    return v