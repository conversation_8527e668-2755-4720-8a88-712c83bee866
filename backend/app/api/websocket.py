"""
WebSocket API endpoints for real-Zaman Ajan communication
Enables streaming responses ve interactive chat ile YKS agents
"""

from fastapi import WebSocket, WebSocketDisconnect, WebSocketException
from typing import Dict, List, Optional, Any
import json
import logging
import asyncio
from datetime import datetime, timezone

from ..agents import YKSAgentOrchestrator
from ..core.hackathon_optimizations import HackathonOptimizer

logger = logging.getLogger(__name__)

class ConnectionManager:
    """
    Manages WebSocket connections for real-Zaman communication
    Features:
    - Multiple concurrent connections
    - Message broadcasting
    - Connection health monitoring
    - Hata recovery
    """
    
    def __init__(self):
        # Aktif connections: {student_id: WebSocket}
        self.active_connections: Dict[str, WebSocket] = {}
        
        # Connection metadata
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
        
        # Message history for recovery
        self.message_history: Dict[str, List[Dict[str, Any]]] = {}
        
        # Ajan Orkestratör instance (will be set den main.py)
        self.orchestrator: Optional[YKSAgentOrchestrator] = None
        
        logger.info("WebSocket ConnectionManager initialized")
    
    def set_orchestrator(self, orchestrator: YKSAgentOrchestrator):
        """Set the Ajan Orkestratör instance"""
        self.orchestrator = orchestrator
        logger.info("Orchestrator set for WebSocket manager")
    
    async def connect(self, websocket: WebSocket, student_id: str):
        """
        Accept new WebSocket connection
        
        Args:
            websocket: FastAPI WebSocket instance
            student_id: Unique Öğrenci identifier
        """
        try:
            await websocket.accept()
            
            # Store connection
            self.active_connections[student_id] = websocket
            
            # Başlat metadata
            self.connection_metadata[student_id] = {
                "connected_at": datetime.now(timezone.utc).isoformat(),
                "last_activity": datetime.now(timezone.utc).isoformat(),
                "message_count": 0
            }
            
            # Başlat message history
            if student_id not in self.message_history:
                self.message_history[student_id] = []
            
            # Gönder welcome message
            await self.send_welcome_message(student_id)
            
            logger.info(f"Student {student_id} connected via WebSocket")
            
        except Exception as e:
            logger.error(f"Connection error for {student_id}: {e}")
            raise WebSocketException(code=1011, reason=str(e))
    
    def disconnect(self, student_id: str):
        """
        İşle WebSocket disconnection
        
        Args:
            student_id: Öğrenci identifier e Bağlantıyı kes
        """
        if student_id in self.active_connections:
            del self.active_connections[student_id]
            
            # Güncelle metadata
            if student_id in self.connection_metadata:
                self.connection_metadata[student_id]["disconnected_at"] = \
                    datetime.now(timezone.utc).isoformat()
            
            logger.info(f"Student {student_id} disconnected")
    
    async def send_personal_message(self, student_id: str, message: str):
        """
        Gönder message e specific Öğrenci
        
        Args:
            student_id: Target Öğrenci
            message: Message İçerik (JSON Metin)
        """
        if student_id in self.active_connections:
            websocket = self.active_connections[student_id]
            try:
                await websocket.send_text(message)
                
                # Güncelle activity
                self.connection_metadata[student_id]["last_activity"] = \
                    datetime.now(timezone.utc).isoformat()
                
            except Exception as e:
                logger.error(f"Error sending to {student_id}: {e}")
                self.disconnect(student_id)
    
    async def broadcast(self, message: str, exclude: Optional[List[str]] = None):
        """
        Broadcast message e all Bağlı students
        
        Args:
            message: Message e broadcast
            exclude: Liste nin Öğrenci IDs e exclude
        """
        exclude = exclude or []
        disconnected = []
        
        for student_id, websocket in self.active_connections.items():
            if student_id not in exclude:
                try:
                    await websocket.send_text(message)
                except:
                    disconnected.append(student_id)
        
        # Clean yukarı Bağlı değil clients
        for student_id in disconnected:
            self.disconnect(student_id)
    
    @HackathonOptimizer.fast_response
    async def handle_message(self, student_id: str, message: str):
        """
        İşle incoming WebSocket message
        
        Args:
            student_id: Message sender
            message: Raw message İçerik
        """
        try:
            # Ayrıştır message
            data = json.loads(message)
            message_type = data.get("type", "chat")
            content = data.get("content", "")
            
            # Güncelle metadata
            self.connection_metadata[student_id]["message_count"] += 1
            self.connection_metadata[student_id]["last_activity"] = \
                datetime.now(timezone.utc).isoformat()
            
            # Store de history
            self.message_history[student_id].append({
                "type": "user",
                "content": content,
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
            
            # İşle based üzerinde message type
            if message_type == "chat":
                await self.handle_chat_message(student_id, content, data)
            elif message_type == "command":
                await self.handle_command(student_id, content, data)
            elif message_type == "feedback":
                await self.handle_feedback(student_id, content, data)
            elif message_type == "ping":
                await self.handle_ping(student_id)
            else:
                await self.send_error(student_id, f"Unknown message type: {message_type}")
            
        except json.JSONDecodeError:
            await self.send_error(student_id, "Invalid JSON format")
        except Exception as e:
            logger.error(f"Message handling error: {e}")
            await self.send_error(student_id, "İşlem sırasında bir hata oluştu")
    
    async def handle_chat_message(self, student_id: str, content: str, data: Dict[str, Any]):
        """
        İşle chat messages boyunca Ajan Orkestratör
        
        Args:
            student_id: Message sender
            İçerik: Message İçerik
            data: Full message data
        """
        if not self.orchestrator:
            await self.send_error(student_id, "Sistem henüz hazır değil")
            return
        
        try:
            # Gönder typing indicator
            await self.send_typing_indicator(student_id, True, "YKS Genius")
            
            # İşle boyunca Ajan system
            response = await self.orchestrator.process_student_request(
                student_id=student_id,
                message=content,
                request_type=data.get("request_type"),
                context=data.get("context", {})
            )
            
            # Durdur typing indicator
            await self.send_typing_indicator(student_id, False, "YKS Genius")
            
            # Biçimlendir Yanıt once
            formatted_response = self.format_agent_response(response)
            
            # Gönder Yanıt ile streaming - bu will İşle both streaming ve final Yanıt
            await self.send_agent_response_streaming(student_id, formatted_response)
            
            # Store de history
            self.message_history[student_id].append({
                "type": "agent",
                "content": formatted_response,
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
            
        except Exception as e:
            logger.error(f"Chat processing error: {e}")
            await self.send_typing_indicator(student_id, False, "YKS Genius")
            await self.send_error(student_id, "Mesajınız işlenirken bir hata oluştu")
    
    async def handle_command(self, student_id: str, command: str, data: Dict[str, Any]):
        """
        İşle special commands
        
        Args:
            student_id: Command sender
            command: Command Ad
            data: Command data
        """
        commands = {
            "clear_history": self.clear_history,
            "get_agents": self.get_agent_list,
            "get_stats": self.get_student_stats,
            "change_agent": self.change_active_agent
        }
        
        handler = commands.get(command)
        if handler:
            await handler(student_id, data)
        else:
            await self.send_error(student_id, f"Unknown command: {command}")
    
    async def handle_feedback(self, student_id: str, feedback: str, data: Dict[str, Any]):
        """
        İşle user feedback
        
        Args:
            student_id: Feedback sender
            feedback: Feedback İçerik
            data: Feedback data
        """
        # Store feedback
        logger.info(f"Feedback from {student_id}: {feedback}")
        
        # Gönder acknowledgment
        await self.send_message(student_id, {
            "type": "feedback_ack",
            "message": "Geri bildiriminiz için teşekkürler! 🙏"
        })
    
    async def handle_ping(self, student_id: str):
        """İşle ping messages for connection health"""
        await self.send_message(student_id, {
            "type": "pong",
            "timestamp": datetime.now(timezone.utc).isoformat()
        })
    
    async def send_welcome_message(self, student_id: str):
        """Gönder welcome message e newly Bağlı Öğrenci"""
        welcome_data = {
            "type": "welcome",
            "message": "YKS Genius'a hoş geldiniz!",
            "student_id": student_id,
            "agents": [
                {"id": "strategy", "name": "Strateji Uzmanı", "status": "active"},
                {"id": "content", "name": "Konu Anlatım Uzmanı", "status": "active"},
                {"id": "practice", "name": "Pratik Uzmanı", "status": "active"},
                {"id": "analytics", "name": "Analiz Uzmanı", "status": "active"},
                {"id": "mentor", "name": "Motivasyon Koçu", "status": "active"}
            ],
            "tips": [
                "Sorularınızı doğal dille sorabilirsiniz",
                "Ajanlar sizin için en uygun cevabı bulacak",
                "İlerlemeniz otomatik olarak takip ediliyor"
            ]
        }
        
        await self.send_message(student_id, welcome_data)
    
    async def send_typing_indicator(self, student_id: str, is_typing: bool, agent_name: str = "YKS Genius"):
        """Gönder typing indicator"""
        await self.send_message(student_id, {
            "type": "typing",
            "is_typing": is_typing,
            "agent": agent_name
        })
    
    async def send_agent_response(self, student_id: str, response: Dict[str, Any]):
        """Gönder formatted Ajan Yanıt"""
        await self.send_message(student_id, {
            "type": "agent_response",
            **response
        })
    
    async def send_agent_response_streaming(self, student_id: str, response: Dict[str, Any]):
        """Gönder Ajan Yanıt ile character-by-character streaming for realistic typing"""
        import uuid
        stream_id = str(uuid.uuid4())
        agent_name = response.get("agent", "YKS Genius")
        content = response.get("content", "")
        
        # Streaming Yapılandırma
        char_delay = 0.02  # 20ms arasında characters (like ChatGPT)
        word_pause = 0.05  # Extra Duraklat arasında words
        punctuation_pause = 0.1  # Duraklat sonra punctuation
        
        # Gönder Başlat nin stream ile Ajan Bilgi
        await self.send_message(student_id, {
            "type": "stream_start",
            "stream_id": stream_id,
            "agent": agent_name
        })
        
        # Stream character tarafından character
        for i, char in enumerate(content):
            await self.send_message(student_id, {
                "type": "stream_chunk",
                "stream_id": stream_id,
                "agent": agent_name,
                "chunk": char
            })
            
            # Variable delays for more natural typing
            if char in '.!?':
                await asyncio.sleep(punctuation_pause)
            elif char == ' ':
                await asyncio.sleep(word_pause)
            else:
                # Slightly random delay for more human-like typing
                import random
                delay = char_delay * random.uniform(0.8, 1.2)
                await asyncio.sleep(delay)
        
        # Gönder stream end signal
        await self.send_message(student_id, {
            "type": "stream_end",
            "stream_id": stream_id,
            "agent": agent_name
        })
        
        # sonra streaming is complete, Gönder the full Ajan Yanıt ile structured data
        # bu is crucial for questions e be displayed ile interactive components
        await self.send_message(student_id, {
            "type": "agent_response",
            "agent": agent_name,
            "content": content,
            "data": response.get("data", {}),
            "suggestions": response.get("suggestions", []),
            "next_action": response.get("next_action"),
            "timestamp": response.get("timestamp", datetime.now(timezone.utc).isoformat()),
            "metadata": response.get("metadata", {})
        })
    
    async def send_error(self, student_id: str, error_message: str):
        """Gönder Hata message"""
        await self.send_message(student_id, {
            "type": "error",
            "message": error_message
        })
    
    async def send_message(self, student_id: str, data: Dict[str, Any]):
        """Gönder JSON message e Öğrenci"""
        message = json.dumps(data, ensure_ascii=False)
        await self.send_personal_message(student_id, message)
    
    def format_agent_response(self, response: Dict[str, Any]) -> Dict[str, Any]:
        """Biçimlendir Ajan Yanıt for WebSocket transmission"""
        # Ajan Ad mapping for proper display
        agent_name_map = {
            "strategy": "Strateji Uzmanı",
            "content": "Konu Anlatım Uzmanı", 
            "practice": "Pratik Uzmanı",
            "analytics": "Analiz Uzmanı",
            "mentor": "Motivasyon Koçu",
            "unknown": "YKS Genius"
        }
        
        # İşle both Metin Ajan names ve dict Ajan objects
        agent_info = response.get("agent", "unknown")
        if isinstance(agent_info, dict):
            agent_name = agent_info.get("name", "YKS Genius")
            agent_role = agent_info.get("role", "assistant")
        else:
            # Map the Ajan Kimlik e proper Ad
            agent_name = agent_name_map.get(agent_info, agent_info)
            agent_role = "assistant"
        
        # Get message İçerik - prefer 'message' Alan, fallback e 'İçerik'
        content = response.get("message", response.get("content", ""))
        
        # For Pratik questions, keep the Başarılı message but let frontend İşle display
        data = response.get("data", {})
        if data.get("quiz_mode") and data.get("questions"):
            # Add unique IDs e questions eğer missing
            questions = data["questions"]
            for i, q in enumerate(questions):
                if not q.get('id'):
                    q['id'] = f"q_{response.get('timestamp', '')}__{i}"
            
            # Don't Biçimlendir questions de İçerik - let frontend components İşle it
            # Just keep the Başarılı message
            pass
            
        return {
            "agent": agent_name,
            "agent_role": agent_role,
            "content": content,
            "data": data,
            "suggestions": response.get("suggestions", []),
            "next_action": response.get("next_action"),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    
    async def clear_history(self, student_id: str, data: Dict[str, Any]):
        """Temizle message history for Öğrenci"""
        self.message_history[student_id] = []
        await self.send_message(student_id, {
            "type": "history_cleared",
            "message": "Mesaj geçmişi temizlendi"
        })
    
    async def get_agent_list(self, student_id: str, data: Dict[str, Any]):
        """Get Liste nin Mevcut agents"""
        if self.orchestrator:
            agents = self.orchestrator.get_agent_info()
            await self.send_message(student_id, {
                "type": "agent_list",
                "agents": agents
            })
        else:
            await self.send_error(student_id, "Agent sistemi henüz hazır değil")
    
    async def get_student_stats(self, student_id: str, data: Dict[str, Any]):
        """Get Öğrenci İstatistikler"""
        stats = {
            "connection_time": self.connection_metadata.get(student_id, {}).get("connected_at"),
            "message_count": self.connection_metadata.get(student_id, {}).get("message_count", 0),
            "history_length": len(self.message_history.get(student_id, []))
        }
        
        await self.send_message(student_id, {
            "type": "stats",
            "data": stats
        })
    
    async def change_active_agent(self, student_id: str, data: Dict[str, Any]):
        """Change Aktif Ajan preference"""
        agent_id = data.get("agent_id")
        
        # bu would be implemented ile Ajan preference logic
        await self.send_message(student_id, {
            "type": "agent_changed",
            "agent_id": agent_id,
            "message": f"Aktif ajan değiştirildi"
        })
    
    def get_connection_count(self) -> int:
        """Get Sayı nin Aktif connections"""
        return len(self.active_connections)
    
    def get_connection_info(self) -> Dict[str, Any]:
        """Get detailed connection information"""
        return {
            "active_connections": self.get_connection_count(),
            "students": list(self.active_connections.keys()),
            "metadata": self.connection_metadata
        }


# Oluştur global connection manager instance
manager = ConnectionManager()