"""
FastAPI Dependencies for YKS Genius
Dependency injection for authentication, Veritabanı, ve service access
"""

from fastapi import Depends, HTTPException, Header, Query, Request
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
import logging

from .config import get_settings
from .database import get_db, get_database_status
from .vector_store import get_vector_store
from .core.gemini_client import GeminiOptimizer
from .agents.orchestrator import YKSAgentOrchestrator
from .api.websocket import manager as connection_manager

logger = logging.getLogger(__name__)
settings = get_settings()


# Veritabanı Dependencies
async def get_database() -> Session:
    """Get Veritabanı Oturum dependency"""
    try:
        db = get_db()
        yield db
    except Exception as e:
        logger.error(f"Database connection error: {e}")
        raise HTTPException(status_code=503, detail="Database connection failed")
    finally:
        if 'db' in locals():
            db.close()


# Service Dependencies
async def get_gemini_client() -> GeminiOptimizer:
    """Get Gemini client dependency"""
    try:
        # İçe aktar burada e avoid circular imports
        from .main import gemini_client
        
        if not gemini_client:
            raise HTTPException(
                status_code=503,
                detail="Gemini client not initialized"
            )
        return gemini_client
    except Exception as e:
        logger.error(f"Gemini client access error: {e}")
        raise HTTPException(
            status_code=503,
            detail="AI service unavailable"
        )


async def get_vector_store():
    """Get Vektör Deposu dependency"""
    try:
        vector_store = get_vector_store()
        if not vector_store:
            raise HTTPException(
                status_code=503,
                detail="Vector store not initialized"
            )
        return vector_store
    except Exception as e:
        logger.error(f"Vector store access error: {e}")
        raise HTTPException(
            status_code=503,
            detail="Content search service unavailable"
        )


async def get_orchestrator() -> YKSAgentOrchestrator:
    """Get Ajan Orkestratör dependency"""
    try:
        # İçe aktar burada e avoid circular imports
        from .main import orchestrator
        
        if not orchestrator:
            raise HTTPException(
                status_code=503,
                detail="Agent orchestrator not initialized"
            )
        return orchestrator
    except Exception as e:
        logger.error(f"Orchestrator access error: {e}")
        raise HTTPException(
            status_code=503,
            detail="AI agent service unavailable"
        )


async def get_connection_manager():
    """Get WebSocket connection manager dependency"""
    try:
        if not connection_manager:
            raise HTTPException(
                status_code=503,
                detail="Connection manager not initialized"
            )
        return connection_manager
    except Exception as e:
        logger.error(f"Connection manager access error: {e}")
        raise HTTPException(
            status_code=503,
            detail="Real-time service unavailable"
        )


# Authentication Dependencies
async def get_current_student_from_header(
    student_id: Optional[str] = Header(None, alias="X-Student-ID"),
    authorization: Optional[str] = Header(None),
    demo_mode: bool = Query(False, description="Enable demo mode")
) -> Dict[str, Any]:
    """
    Get Akım Öğrenci den headers veya Oluştur demo Öğrenci
    
    For hackathon demo purposes, bu supports:
    1. Demo mode (no authentication Gerekli)
    2. Simple Öğrenci Kimlik header
    3. Future: JWT token authentication
    """
    
    # Demo mode for hackathon presentation
    if demo_mode or settings.hackathon_mode:
        return {
            "id": "demo_student",
            "name": "Demo Öğrenci",
            "email": "<EMAIL>",
            "grade_level": 12,
            "target_university": "İTÜ",
            "target_department": "Bilgisayar Mühendisliği",
            "target_score": 450,
            "is_demo": True
        }
    
    # Simple Öğrenci Kimlik authentication
    if student_id:
        # de a real application, you would Doğrula bu against the Veritabanı
        # For now, return a basic Öğrenci Nesne
        return {
            "id": student_id,
            "name": f"Öğrenci {student_id[-4:]}",
            "email": f"student{student_id[-4:]}@example.com",
            "grade_level": 12,
            "is_demo": False
        }
    
    # JWT token authentication (future implementation)
    if authorization and authorization.startswith("Bearer "):
        token = authorization.split(" ")[1]
        # TODO: Implement JWT validation
        # For now, extract Öğrenci Bilgi den token claims
        try:
            # Placeholder for JWT decoding
            student_data = {
                "id": "authenticated_student",
                "name": "Authenticated Student",
                "email": "<EMAIL>",
                "grade_level": 12,
                "is_demo": False
            }
            return student_data
        except Exception as e:
            logger.error(f"Token validation error: {e}")
            raise HTTPException(
                status_code=401,
                detail="Invalid authentication token"
            )
    
    # No authentication provided
    raise HTTPException(
        status_code=401,
        detail="Authentication required. Use demo_mode=true for testing or provide X-Student-ID header."
    )


async def get_current_student(
    demo_mode: bool = Query(True, description="Enable demo mode"),
    authorization: Optional[str] = Header(None)
) -> Dict[str, Any]:
    """
    Get Akım Öğrenci - simplified for hackathon demo
    """
    # Always return demo Öğrenci for hackathon
    if demo_mode or settings.hackathon_mode:
        return {
            "id": "demo_student",
            "name": "Demo Öğrenci",
            "email": "<EMAIL>",
            "grade_level": 12,
            "target_university": "İTÜ",
            "target_department": "Bilgisayar Mühendisliği",
            "target_score": 450,
            "is_demo": True,
            "is_admin": False
        }
    
    # For non-demo mode (future implementation)
    return {
        "id": "demo_student",
        "name": "Demo Öğrenci", 
        "email": "<EMAIL>",
        "grade_level": 12,
        "is_demo": True,
        "is_admin": False
    }


async def get_optional_student(
    demo_mode: bool = Query(True, description="Enable demo mode"),
    authorization: Optional[str] = Header(None)
) -> Optional[Dict[str, Any]]:
    """Get Akım Öğrenci (İsteğe bağlı - doesn't raise Hata eğer not authenticated)"""
    try:
        return await get_current_student(demo_mode, authorization)
    except HTTPException:
        return None


# Admin Dependencies
async def get_admin_user(
    authorization: Optional[str] = Header(None),
    admin_key: Optional[str] = Header(None, alias="X-Admin-Key")
) -> Dict[str, Any]:
    """
    Get admin user for administrative endpoints
    
    For hackathon demo:
    - Simple admin Anahtar authentication
    - Future: Role-based access control
    """
    
    # Simple admin Anahtar for demo
    if admin_key == settings.admin_key or settings.hackathon_mode:
        return {
            "id": "admin",
            "role": "administrator",
            "permissions": ["read", "write", "admin"]
        }
    
    # JWT-based admin authentication (future)
    if authorization and authorization.startswith("Bearer "):
        # TODO: Implement admin JWT validation
        pass
    
    raise HTTPException(
        status_code=403,
        detail="Administrative access required"
    )


# Validation Dependencies
async def validate_request_size(request: Request):
    """Doğrula İstek Boyut e prevent abuse"""
    content_length = request.headers.get("content-length")
    if content_length:
        content_length = int(content_length)
        max_size = 10 * 1024 * 1024  # 10MB Sınır
        if content_length > max_size:
            raise HTTPException(
                status_code=413,
                detail="Request entity too large"
            )


async def validate_rate_limit(
    student: Optional[Dict[str, Any]] = Depends(get_optional_student),
    request: Request = None
):
    """
    Basic rate limiting dependency
    
    For hackathon demo: Simple in-memory rate limiting
    Production: Redis-based distributed rate limiting
    """
    
    # Skip rate limiting de demo mode
    if settings.hackathon_mode or (student and student.get("is_demo")):
        return True
    
    # Basic rate limiting logic
    client_ip = request.client.host if request else "unknown"
    
    # TODO: Implement proper rate limiting
    # For now, just log the İstek
    logger.info(f"Rate limit check for {client_ip}, student: {student.get('id') if student else 'anonymous'}")
    
    return True


# Health Check Dependencies
async def check_service_health():
    """Check overall service health for dependencies"""
    try:
        # Check Veritabanı
        db_status = get_database_status()
        if db_status.get("status") != "healthy":
            raise HTTPException(
                status_code=503,
                detail="Database service unhealthy"
            )
        
        # Check other services
        # Vektör Deposu, Gemini client, etc.
        
        return {
            "database": "healthy",
            "services": "operational",
            "timestamp": "now"
        }
    except Exception as e:
        logger.error(f"Service health check failed: {e}")
        raise HTTPException(
            status_code=503,
            detail="Service health check failed"
        )


# Önbellek Dependencies
class CacheManager:
    """Simple in-memory Önbellek for development/demo"""
    
    def __init__(self):
        self.cache: Dict[str, Any] = {}
    
    def get(self, key: str) -> Optional[Any]:
        return self.cache.get(key)
    
    def set(self, key: str, value: Any, ttl: int = 300):
        # Simple Önbellek without TTL for demo
        self.cache[key] = value
    
    def delete(self, key: str):
        self.cache.pop(key, None)
    
    def clear(self):
        self.cache.clear()


# Global Önbellek instance
cache_manager = CacheManager()


async def get_cache() -> CacheManager:
    """Get Önbellek manager dependency"""
    return cache_manager


# Pagination Dependencies
class PaginationParams:
    """Pagination parameters"""
    
    def __init__(
        self,
        page: int = Query(1, ge=1, description="Page number"),
        size: int = Query(20, ge=1, le=100, description="Items per page")
    ):
        self.page = page
        self.size = size
        self.offset = (page - 1) * size
        self.limit = size


async def get_pagination(params: PaginationParams = Depends()) -> PaginationParams:
    """Get pagination parameters"""
    return params


# Search Dependencies
class SearchParams:
    """Search parameters"""
    
    def __init__(
        self,
        q: Optional[str] = Query(None, min_length=2, max_length=200, description="Search query"),
        sort_by: str = Query("relevance", description="Sort field"),
        sort_order: str = Query("desc", pattern="^(asc|desc)$", description="Sort order"),
        filters: Optional[str] = Query(None, description="JSON filters")
    ):
        self.query = q
        self.sort_by = sort_by
        self.sort_order = sort_order
        self.filters = filters


async def get_search_params(params: SearchParams = Depends()) -> SearchParams:
    """Get search parameters"""
    return params