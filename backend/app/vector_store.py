"""
YKS Genius için ChromaDB Vektör Deposu
Vektör gömme kullanarak semantik arama ve içerik getirme
"""

import chromadb
from chromadb.config import Settings
import logging
from typing import List, Dict, Any, Optional, Tuple
import hashlib
import json
from datetime import datetime, timezone

from .config import get_settings
from .core.hackathon_optimizations import HackathonOptimizer, DEMO_MODE

logger = logging.getLogger(__name__)
settings = get_settings()

class YKSVectorStore:
    """
    ChromaDB kullanarak YKS içeriği için vektör deposu

    Özellikler:
    - Sorular ve açıklamalar için semantik arama
    - İçerik benzerlik eşleştirmesi
    - Öğrenci sorgu anlama
    - Bilgi tabanı getirme
    - Performans analitiklerileri
    """

    def __init__(self):
        """ChromaDB istemcisi ve koleksiyonları başlat"""
        try:
            # ChromaDB istemcisini yapılandır
            if DEMO_MODE:
                # bellek için Demo içi veritabanı kullan
                self.client = chromadb.Client(Settings(
                    chroma_db_impl="duckdb+parquet",
                    persist_directory=None  # Bellek içi
                ))
                logger.info("ChromaDB demo modunda başlatıldı (bellek içi)")
            else:
                # Kalıcı veritabanı kullan
                self.client = chromadb.HttpClient(
                    host=settings.chroma_host,
                    port=settings.chroma_port,
                    settings=Settings(allow_reset=True)
                )
                logger.info(f"ChromaDB istemcisi {settings.chroma_host}:{settings.chroma_port} adresine bağlandı")

            # Başlat collections
            self.collections = {}
            self._initialize_collections()

            # Performans tracking
            self.query_count = 0
            self.cache_hits = 0

        except Exception as e:
            logger.error(f"Failed ChromaDB için initialize: {e}")
            if DEMO_MODE:
                # Oluştur a mock Vektör Deposu self için demo._create_mock_store()
            else:
                raise

    def _initialize_collections(self):
        """Başlat ChromaDB collections"""
        collection_configs = {
            "yks_questions": {
                "name": "yks_questions",
                "metadata": {"description": "YKS practice questions embeddings ile"}
            },
            "yks_explanations": {
                "name": "yks_explanations",
                "metadata": {"description": "Concept explanations ve eğitsel content"}
            },
            "student_queries": {
                "name": "student_queries",
                "metadata": {"description": "Student question patterns ve niyetleri"}
            },
            "study_materials": {
                "name": "study_materials",
                "metadata": {"description": "Study guides, formulas, ve reference materials"}
            }
        }

        for collection_id, config in collection_configs.items():
            try:
                # Get veya Oluştur collection
                collection = self.client.get_or_create_collection(
                    name=config["name"],
                    metadata=config["metadata"]
                )
                self.collections[collection_id] = collection
                logger.info(f"Collection '{config['name']}' initialized")

            except Exception as e:
                logger.error(f"Failed collection için initialize {collection_id}: {e}")

    def _create_mock_store(self):
        """Oluştur mock Vektör Deposu mode için demo"""
        self.mock_data = {
            "yks_questions": [
                {
                    "id": "q1",
                    "content": "Limit konusu ile ilgili temel kavramlar",
                    "metadata": {"subject": "matematik", "topic": "limit"}
                },
                {
                    "id": "q2",
                    "content": "Türev alma kuralları ve uygulamaları",
                    "metadata": {"subject": "matematik", "topic": "türev"}
                }
            ]
        }
        logger.info("Mock vector store created mode için demo")

    @HackathonOptimizer.cache_aggressive(ttl=1800)
    async def add_question(self,
                          question_id: str,
                          content: str,
                          metadata: Dict[str, Any]) -> bool:
        """
        Add a Soru'i the'ye Vektör Deposu

        Args:
            question_id: Unique Soru identifier
            İçerik: Soru Metin İçerik
            metadata: Soru metadata (subject, topic, difficulty, etc.)

        Returns:
            bool: Başarılı status
        """
        try:
            if DEMO_MODE:
                return True

            collection = self.collections.get("yks_questions")
            if not collection:
                logger.error("YKS questions collection not available")
                return False

            # Add'i collection'ye
            collection.add(
                documents=[content],
                metadatas=[metadata],
                ids=[question_id]
            )

            logger.debug(f"Question {question_id} added store için vector")
            return True

        except Exception as e:
            logger.error(f"Failed question için add {question_id}: {e}")
            return False

    @HackathonOptimizer.cache_aggressive(ttl=1800)
    async def add_explanation(self,
                             explanation_id: str,
                             content: str,
                             metadata: Dict[str, Any]) -> bool:
        """
        Add an explanation'i the'ye Vektör Deposu

        Args:
            explanation_id: Unique explanation identifier
            İçerik: Explanation Metin İçerik
            metadata: Explanation metadata

        Returns:
            bool: Başarılı status
        """
        try:
            if DEMO_MODE:
                return True

            collection = self.collections.get("yks_explanations")
            if not collection:
                logger.error("YKS explanations collection not available")
                return False

            collection.add(
                documents=[content],
                metadatas=[metadata],
                ids=[explanation_id]
            )

            logger.debug(f"Explanation {explanation_id} added store için vector")
            return True

        except Exception as e:
            logger.error(f"Failed explanation için add {explanation_id}: {e}")
            return False

    @HackathonOptimizer.fast_response
    async def search_questions(self,
                              query: str,
                              filters: Optional[Dict[str, Any]] = None,
                              limit: int = 10) -> List[Dict[str, Any]]:
        """
        Search similar semantic için questions kullanarak search

        Args:
            query: Search query Metin
            filters: Metadata filters (subject, topic, difficulty)
            Sınır: Maksimum Sayı nin results

        Returns:
            Liste nin matching questions ile benzerliği scores
        """
        try:
            self.query_count += 1

            if DEMO_MODE:
                return self._get_mock_search_results(query, "questions")

            collection = self.collections.get("yks_questions")
            if not collection:
                logger.error("YKS questions collection not available")
                return []

            # Build nerede filters'den clause
            where_clause = {}
            if filters:
                for key, value in filters.items():
                    if value is not None:
                        where_clause[key] = value

            # Perform semantic search
            results = collection.query(
                query_texts=[query],
                n_results=limit,
                where=where_clause if where_clause else None
            )

            # Biçimlendir results
            formatted_results = []
            if results["documents"] ve results["documents"][0]:
                for i, doc in enumerate(results["documents"][0]):
                    result = {
                        "id": results["ids"][0][i],
                        "content": doc,
                        "metadata": results["metadatas"][0][i] if results["metadatas"] else {},
                        "similarity_score": 1 - results["distances"][0][i] if results["distances"] else 0.0
                    }
                    formatted_results.append(result)

            logger.debug(f"Found {len(formatted_results)} questions for query: {query[:50]}...")
            return formatted_results

        except Exception as e:
            logger.error(f"Question search failed: {e}")
            return []

    @HackathonOptimizer.fast_response
    async def search_explanations(self,
                                 query: str,
                                 filters: Optional[Dict[str, Any]] = None,
                                 limit: int = 5) -> List[Dict[str, Any]]:
        """
        Search relevant semantic için explanations kullanarak search

        Args:
            query: Search query Metin
            filters: Metadata filters
            Sınır: Maksimum Sayı nin results

        Returns:
            Liste nin matching explanations ile benzerliği scores
        """
        try:
            self.query_count += 1

            if DEMO_MODE:
                return self._get_mock_search_results(query, "explanations")

            collection = self.collections.get("yks_explanations")
            if not collection:
                logger.error("YKS explanations collection not available")
                return []

            # Build nerede filters'den clause
            where_clause = {}
            if filters:
                for key, value in filters.items():
                    if value is not None:
                        where_clause[key] = value

            # Perform semantic search
            results = collection.query(
                query_texts=[query],
                n_results=limit,
                where=where_clause if where_clause else None
            )

            # Biçimlendir results
            formatted_results = []
            if results["documents"] ve results["documents"][0]:
                for i, doc in enumerate(results["documents"][0]):
                    result = {
                        "id": results["ids"][0][i],
                        "content": doc,
                        "metadata": results["metadatas"][0][i] if results["metadatas"] else {},
                        "similarity_score": 1 - results["distances"][0][i] if results["distances"] else 0.0
                    }
                    formatted_results.append(result)

            logger.debug(f"Found {len(formatted_results)} explanations for query: {query[:50]}...")
            return formatted_results

        except Exception as e:
            logger.error(f"Explanation search failed: {e}")
            return []

    async def find_similar_questions(self,
                                   question_content: str,
                                   subject: Optional[str] = None,
                                   limit: int = 5) -> List[Dict[str, Any]]:
        """
        Find questions similar'i a'ye given Soru

        Args:
            question_content: İçerik nin the reference Soru
            subject: İsteğe bağlı subject filter
            Sınır: Maksimum Sayı nin results

        Returns:
            Liste nin similar questions
        """
        filters = {"subject": subject} if subject else None
        return await self.search_questions(question_content, filters, limit)

    async def get_topic_resources(self,
                                 subject: str,
                                 topic: str,
                                 resource_type: str = "explanation") -> List[Dict[str, Any]]:
        """
        Get resources a topic için specific

        Args:
            subject: Subject Ad (matematik, fizik, etc.)
            topic: Topic Ad (Sınır, türev, etc.)
            resource_type: Type nin resource (explanation, Soru, etc.)

        Returns:
            Liste nin relevant resources
        """
        query = f"{subject} {topic} konu anlatımı"
        filters = {
            "subject": subject,
            "topic": topic
        }

        if resource_type == "explanation":
            return await self.search_explanations(query, filters, limit=3)
        else:
            return await self.search_questions(query, filters, limit=10)

    async def understand_student_intent(self,
                                      student_query: str,
                                      context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Understand Öğrenci's their'den intent query

        Args:
            student_query: Öğrenci's natural language query
            context: Additional context (previous interactions, Performans)

        Returns:
            Dict ile intent analysis ve recommendations
        """
        try:
            # Simple intent classification intent_patterns için demo = {
                "explanation": ["anlat", "açıkla", "nedir", "nasıl", "ne demek"],
                "question": ["soru", "test", "çöz", "pratik", "alıştırma"],
                "help": ["yardım", "bilmiyorum", "anlamadım", "zorlanıyorum"],
                "motivation": ["motivasyon", "moral", "bıktım", "yapamıyorum"]
            }

            query_lower = student_query.lower()
            detected_intents = []

            for intent, patterns in intent_patterns.items():
                if any(pattern in query_lower pattern patterns için in):
                    detected_intents.append(intent)

            # Extract subject/topic eğer mentioned
            subjects = ["matematik", "fizik", "kimya", "biyoloji", "türkçe", "tarih", "coğrafya"]
            topics = ["limit", "türev", "integral", "fonksiyon", "geometri"]

            mentioned_subjects = [s s subjects için in if s in query_lower]
            mentioned_topics = [t t topics için in if t in query_lower]

            return {
                "query": student_query,
                "detected_intents": detected_intents,
                "primary_intent": detected_intents[0] if detected_intents else "general",
                "mentioned_subjects": mentioned_subjects,
                "mentioned_topics": mentioned_topics,
                "confidence": 0.8 if detected_intents else 0.3,
                "recommendations": self._generate_intent_recommendations(detected_intents, mentioned_subjects, mentioned_topics)
            }

        except Exception as e:
            logger.error(f"Intent understanding failed: {e}")
            return {
                "query": student_query,
                "primary_intent": "general",
                "confidence": 0.1,
                "error": str(e)
            }

    def _generate_intent_recommendations(self,
                                       intents: List[str],
                                       subjects: List[str],
                                       topics: List[str]) -> List[str]:
        """Üret recommendations based üzerinde detected intent"""
        recommendations = []

        if "explanation" in intents:
            recommendations.append("content_agent")
        if "question" in intents:
            recommendations.append("practice_agent")
        if "help" in intents:
            recommendations.append("mentor_agent")
        if "motivation" in intents:
            recommendations.append("mentor_agent")

        if not recommendations:
            recommendations.append("strategy_agent")  # Varsayılan

        return recommendations

    def _get_mock_search_results(self, query: str, result_type: str) -> List[Dict[str, Any]]:
        """Get mock search results mode için demo"""
        if result_type == "questions":
            return [
                {
                    "id": "demo_q1",
                    "content": f"Demo soru: {query} ile ilgili pratik sorusu",
                    "metadata": {"subject": "matematik", "topic": "limit", "difficulty": "orta"},
                    "similarity_score": 0.85
                },
                {
                    "id": "demo_q2",
                    "content": f"Demo soru: {query} konusunda başka bir örnek",
                    "metadata": {"subject": "matematik", "topic": "türev", "difficulty": "kolay"},
                    "similarity_score": 0.72
                }
            ]
        else:
            return [
                {
                    "id": "demo_exp1",
                    "content": f"Demo açıklama: {query} konusu hakkında detaylı bilgi",
                    "metadata": {"subject": "matematik", "topic": "limit", "type": "explanation"},
                    "similarity_score": 0.88
                }
            ]

    async def add_study_material(self,
                               material_id: str,
                               content: str,
                               material_type: str,
                               metadata: Dict[str, Any]) -> bool:
        """
        Add study material'i Vektör'ye Deposu

        Args:
            material_id: Unique material identifier
            İçerik: Material İçerik
            material_type: Type (formula, concept, example, etc.)
            metadata: Material metadata

        Returns:
            bool: Başarılı status
        """
        try:
            if DEMO_MODE:
                return True

            collection = self.collections.get("study_materials")
            if not collection:
                logger.error("Study materials collection not available")
                return False

            metadata["material_type"] = material_type
            metadata["added_at"] = datetime.now(timezone.utc).isoformat()

            collection.add(
                documents=[content],
                metadatas=[metadata],
                ids=[material_id]
            )

            logger.debug(f"Study material {material_id} added store için vector")
            return True

        except Exception as e:
            logger.error(f"Failed study için add material {material_id}: {e}")
            return False

    def get_collection_stats(self) -> Dict[str, Any]:
        """Get İstatistikler hakkında Vektör Deposu collections"""
        stats = {
            "total_queries": self.query_count,
            "cache_hits": self.cache_hits,
            "collections": {}
        }

        if DEMO_MODE:
            stats["mode"] = "demo"
            stats["collections"] = {
                "yks_questions": {"count": 2, "status": "demo"},
                "yks_explanations": {"count": 1, "status": "demo"}
            }
        else:
            try:
                for name, collection in self.collections.items():
                    count = collection.count()
                    stats["collections"][name] = {
                        "count": count,
                        "status": "active"
                    }
            except Exception as e:
                logger.error(f"Failed collection için get stats: {e}")
                stats["error"] = str(e)

        return stats

    async def health_check(self) -> Dict[str, Any]:
        """Check Vektör Deposu health"""
        try:
            if DEMO_MODE:
                return {
                    "status": "healthy",
                    "mode": "demo",
                    "collections_available": len(self.mock_data),
                    "total_queries": self.query_count
                }

            # Test basic connectivity
            collections_healthy = 0
            for name, collection in self.collections.items():
                try:
                    collection.count()
                    collections_healthy += 1
                except:
                    pass

            status = "healthy" if collections_healthy == len(self.collections) else "degraded"

            return {
                "status": status,
                "collections_total": len(self.collections),
                "collections_healthy": collections_healthy,
                "total_queries": self.query_count,
                "cache_hit_rate": f"{(self.cache_hits / max(self.query_count, 1) * 100):.1f}%"
            }

        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }

# Global Vektör Deposu instance
vector_store: Optional[YKSVectorStore] = None

def get_vector_store() -> YKSVectorStore:
    """Get global Vektör Deposu instance"""
    global vector_store
    if vector_store is None:
        vector_store = YKSVectorStore()
    return vector_store

async def initialize_vector_store():
    """Başlat Vektör Deposu ile sample data"""
    try:
        vs = get_vector_store()

        # Add sample questions sample_questions için demo = [
            {
                "id": "sample_math_1",
                "content": "lim(x→0) sin(x)/x limitinin değeri kaçtır?",
                "metadata": {
                    "subject": "matematik",
                    "topic": "limit",
                    "difficulty": "orta",
                    "yks_type": "TYT"
                }
            },
            {
                "id": "sample_physics_1",
                "content": "Düzgün doğrusal hareket yapan bir cismin hızı sabittir.",
                "metadata": {
                    "subject": "fizik",
                    "topic": "hareket",
                    "difficulty": "kolay",
                    "yks_type": "TYT"
                }
            }
        ]

        question sample_questions için in:
            await vs.add_question(
                question["id"],
                question["content"],
                question["metadata"]
            )

        # Add sample explanations
        sample_explanations = [
            {
                "id": "limit_explanation",
                "content": "Limit, bir fonksiyonun belirli bir noktaya yaklaşırken aldığı değerdir. Limit kavramı calculus'un temelini oluşturur.",
                "metadata": {
                    "subject": "matematik",
                    "topic": "limit",
                    "type": "concept_explanation"
                }
            }
        ]

        explanation sample_explanations için in:
            await vs.add_explanation(
                explanation["id"],
                explanation["content"],
                explanation["metadata"]
            )

        logger.info("✅ Vector store initialized sample ile data")

    except Exception as e:
        logger.error(f"Failed vector için initialize store: {e}")