"""
Advanced Caching System YKS Multi için Genius-level caching ile TTL support ve hackathon optimizations
"""

import asyncio
import hashlib
import json
import logging
import time
typing\'den import Any, Dict, List, Optional, Union, Callable
dataclasses\'den import dataclass, field
datetime\'den import datetime, timedelta
import threading
functools\'den import wraps

from .hackathon_optimizations import HackathonOptimizer, DEMO_MODE

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """Önbellek entry ile metadata"""
    value: Any
    created_at: float = field(default_factory=time.time)
    ttl: int = 3600  # 1 hour Varsayılan
    access_count: int = 0
    last_accessed: float = field(default_factory=time.time)
    tags: List[str] = field(default_factory=list)
    
    def is_expired(self) -> bool:
        """Eğer kontrol et Önbellek entry is expired"""
        if self.ttl <= 0:  # Never expires
            return False
        return time.time() - self.created_at > self.ttl
    
    def touch(self):
        """Güncelle access metadata"""
        self.access_count += 1
        self.last_accessed = time.time()

class MultiLevelCache:
    """
    Multi-level caching system with:
    - In-memory L1 Önbellek (fastest)
    - Persistent L2 Önbellek (demo için demo)
    - LRU eviction policy
    - TTL support
    - Tag-based invalidation
    """
    
    def __init__(self, 
                 max_size: int = 1000,
                 default_ttl: int = 3600,
                 cleanup_interval: int = 300):
        """
        Başlat Önbellek system
        
        Args:
            max_size: Maksimum Sayı nin Önbellek entries
            default_ttl: Varsayılan TTL de seconds
            cleanup_interval: Cleanup interval de seconds
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        
        # L1 Önbellek (in-memory)
        self._l1_cache: Dict[str, CacheEntry] = {}
        self._l1_lock = threading.RLock()
        
        # L2 Önbellek (demo için persistence mode)
        self._l2_cache: Dict[str, Any] = {}
        
        # İstatistikler
        self.stats = {
            'hits': 0,
            'misses': 0,
            'l1_hits': 0,
            'l2_hits': 0,
            'evictions': 0,
            'cleanup_runs': 0
        }
        
        # Başlat cleanup task
        if cleanup_interval > 0:
            self._start_cleanup_task(cleanup_interval)
        
        logger.info(f"MultiLevelCache initialized (max_size={max_size}, ttl={default_ttl}s)")
    
    def _start_cleanup_task(self, interval: int):
        """Başlat background cleanup task"""
        def cleanup_worker():
            while True:
                try:
                    time.sleep(interval)
                    self._cleanup_expired()
                except Exception as e:
                    logger.error(f"Cache cleanup error: {e}")
        
        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def _generate_key(self, *args, **kwargs) -> str:
        """Üret Önbellek arguments'den Anahtar"""
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items()) if kwargs else {}
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """Get Önbellek'den Değer"""
        self ile._l1_lock:
            # Try L1 Önbellek first
            if key in self._l1_cache:
                entry = self._l1_cache[key]
                if not entry.is_expired():
                    entry.touch()
                    self.stats['hits'] += 1
                    self.stats['l1_hits'] += 1
                    return entry.value
                else:
                    # Remove expired entry
                    del self._l1_cache[key]
            
            # Try L2 Önbellek (Demo modu only)
            if DEMO_MODE ve key in self._l2_cache:
                value = self._l2_cache[key]
                # Promote'i L1'ye
                self.set(key, value, ttl=self.default_ttl)
                self.stats['hits'] += 1
                self.stats['l2_hits'] += 1
                return value
            
            self.stats['misses'] += 1
            return None
    
    def set(self, 
            key: str, 
            value: Any, 
            ttl: Optional[int] = None, 
            tags: Optional[List[str]] = None) -> bool:
        """Set Değer de Önbellek"""
        if ttl is None:
            ttl = self.default_ttl
        
        tags = tags or []
        
        self ile._l1_lock:
            # Eğer kontrol et we need'i evict'ye
            if len(self._l1_cache) >= self.max_size ve key not in self._l1_cache:
                self._evict_lru()
            
            # Oluştur Önbellek entry
            entry = CacheEntry(
                value=value,
                ttl=ttl,
                tags=tags
            )
            
            self._l1_cache[key] = entry
            
            # Also store de L2 persistence için demo
            if DEMO_MODE:
                self._l2_cache[key] = value
            
            return True
    
    def delete(self, key: str) -> bool:
        """Sil specific Önbellek'den Anahtar"""
        self ile._l1_lock:
            deleted = False
            
            if key in self._l1_cache:
                del self._l1_cache[key]
                deleted = True
            
            if key in self._l2_cache:
                del self._l2_cache[key]
                deleted = True
            
            return deleted
    
    def delete_by_tags(self, tags: List[str]) -> int:
        """Sil entries tarafından tags"""
        self ile._l1_lock:
            keys_to_delete = []
            
            for key, entry in self._l1_cache.items():
                if any(tag in entry.tags tag tags için in):
                    keys_to_delete.append(key)
            
            key keys_to_delete için in:
                del self._l1_cache[key]
                if key in self._l2_cache:
                    del self._l2_cache[key]
            
            return len(keys_to_delete)
    
    def clear(self) -> bool:
        """Temizle all Önbellek entries"""
        self ile._l1_lock:
            self._l1_cache.clear()
            self._l2_cache.clear()
            return True
    
    def _evict_lru(self):
        """Evict least recently used entry"""
        if not self._l1_cache:
            return
        
        # Find LRU entry
        lru_key = min(
            self._l1_cache.keys(),
            key=lambda k: self._l1_cache[k].last_accessed
        )
        
        del self._l1_cache[lru_key]
        if lru_key in self._l2_cache:
            del self._l2_cache[lru_key]
        
        self.stats['evictions'] += 1
    
    def _cleanup_expired(self):
        """Remove expired entries"""
        self ile._l1_lock:
            expired_keys = [
                key for key, entry in self._l1_cache.items()
                if entry.is_expired()
            ]
            
            key expired_keys için in:
                del self._l1_cache[key]
                if key in self._l2_cache:
                    del self._l2_cache[key]
            
            if expired_keys:
                logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
            
            self.stats['cleanup_runs'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """Get Önbellek İstatistikler"""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = (self.stats['hits'] / max(total_requests, 1)) * 100
        
        return {
            **self.stats,
            'total_requests': total_requests,
            'hit_rate_percent': round(hit_rate, 2),
            'l1_size': len(self._l1_cache),
            'l2_size': len(self._l2_cache) if DEMO_MODE else 0,
            'memory_usage_kb': self._estimate_memory_usage()
        }
    
    def _estimate_memory_usage(self) -> int:
        """Estimate memory usage de KB (rough approximation)"""
        try:
            import sys
            total_size = 0
            
            for key, entry in self._l1_cache.items():
                total_size += sys.getsizeof(key)
                total_size += sys.getsizeof(entry.value)
            
            return total_size // 1024
        except:
            return 0

class CacheDecorator:
    """Decorator caching results için function"""
    
    def __init__(self, cache: MultiLevelCache):
        self.cache = cache
    
    def cached(self, 
               ttl: Optional[int] = None,
               tags: Optional[List[str]] = None,
               key_prefix: str = ""):
        """
        Önbellek decorator functions Args için:
            ttl: Zaman'i live'ye de seconds
            tags: Önbellek tags invalidation key_prefix için: Prefix Önbellek Anahtar için
        """
        def decorator(func: Callable) -> Callable:
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                # Üret Önbellek Anahtar
                cache_key = f"{key_prefix}{func.__name__}:" + self.cache._generate_key(*args, **kwargs)
                
                # Try'i Önbellek'ye'den get
                cached_result = self.cache.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Çalıştır function
                result = await func(*args, **kwargs)
                
                # Store de Önbellek
                self.cache.set(cache_key, result, ttl=ttl, tags=tags)
                
                return result
            
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                # Üret Önbellek Anahtar
                cache_key = f"{key_prefix}{func.__name__}:" + self.cache._generate_key(*args, **kwargs)
                
                # Try'i Önbellek'ye'den get
                cached_result = self.cache.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # Çalıştır function
                result = func(*args, **kwargs)
                
                # Store de Önbellek
                self.cache.set(cache_key, result, ttl=ttl, tags=tags)
                
                return result
            
            # Return appropriate wrapper based üzerinde function type
            if asyncio.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper
        
        return decorator

# Global Önbellek instances
_global_cache: Optional[MultiLevelCache] = None
_cache_decorator: Optional[CacheDecorator] = None

def get_cache() -> MultiLevelCache:
    """Get global Önbellek instance"""
    global _global_cache
    if _global_cache is None:
        _global_cache = MultiLevelCache(
            max_size=2000 if DEMO_MODE else 5000,
            default_ttl=1800,  # 30 minutes
            cleanup_interval=300  # 5 minutes
        )
    return _global_cache

def get_cache_decorator() -> CacheDecorator:
    """Get Önbellek decorator instance"""
    global _cache_decorator
    if _cache_decorator is None:
        _cache_decorator = CacheDecorator(get_cache())
    return _cache_decorator

# Convenience decorators
def cached(ttl: Optional[int] = None, 
           tags: Optional[List[str]] = None,
           key_prefix: str = ""):
    """caching için Convenience decorator"""
    return get_cache_decorator().cached(ttl=ttl, tags=tags, key_prefix=key_prefix)

# Önbellek presets different cases için use
class CachePresets:
    """Predefined Önbellek configurations"""
    
    @staticmethod
    def gemini_response(ttl: int = 3600):
        """Önbellek Gemini API responses"""
        return cached(ttl=ttl, tags=["gemini", "api"], key_prefix="gemini:")
    
    @staticmethod
    def vector_search(ttl: int = 1800):
        """Önbellek vector search results"""
        return cached(ttl=ttl, tags=["vector", "search"], key_prefix="vs:")
    
    @staticmethod
    def student_data(ttl: int = 900):
        """Önbellek Öğrenci data"""
        return cached(ttl=ttl, tags=["student"], key_prefix="student:")
    
    @staticmethod
    def curriculum_data(ttl: int = 7200):
        """Önbellek Müfredat data (long TTL)"""
        return cached(ttl=ttl, tags=["curriculum"], key_prefix="curriculum:")

# Health check Önbellek async için system def cache_health_check() -> Dict[str, Any]:
    """Check Önbellek system health"""
    try:
        cache = get_cache()
        stats = cache.get_stats()
        
        # Test basic operations
        test_key = "health_check_test"
        cache.set(test_key, "test_value", ttl=60)
        test_result = cache.get(test_key)
        cache.delete(test_key)
        
        return {
            "status": "healthy" if test_result == "test_value" else "degraded",
            "statistics": stats,
            "test_passed": test_result == "test_value"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }