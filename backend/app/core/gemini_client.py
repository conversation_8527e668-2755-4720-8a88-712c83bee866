"""
YKS Genius için optimize edilmiş Gemini API istemcisi
API çağrıları, önbellekleme ve hız sınırlamasını yönetir
"""

import os
import hashlib
import time
import asyncio
from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime, timedelta
import json
import logging

import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold

from ..config import get_settings
from .hackathon_optimizations import HackathonOptimizer, DEMO_MODE, HACKATHON_MODE

logger = logging.getLogger(__name__)
settings = get_settings()

class GeminiOptimizer:
    """
    Optimized Gemini client for hackathon demo
    Features:
    - Smart model selection (Flash vs Pro)
    - Aggressive caching
    - Rate limit protection
    - Batch processing
    - Demo mode fallbacks
    """
    
    def __init__(self):
        # Configure Gemini
        if settings.gemini_api_key and settings.gemini_api_key != "demo-key-for-hackathon":
            genai.configure(api_key=settings.gemini_api_key)
            
            # Initialize models
            self.flash_model = genai.GenerativeModel(settings.gemini_model_flash)
            self.pro_model = genai.GenerativeModel(settings.gemini_model_pro)
        else:
            # Demo mode - no real API key
            logger.warning("Gemini API key not configured - running in demo mode")
            self.flash_model = None
            self.pro_model = None
        
        # Cache and rate limiting
        self.cache: Dict[str, Tuple[str, float]] = {}
        self.daily_calls = 0
        self.last_reset = datetime.now()
        self.call_times: List[float] = []
        
        # Hackathon optimizations
        self.optimizer = HackathonOptimizer()
        
        # Safety settings
        self.safety_settings = {
            HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
            HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
        }
        
        logger.info("Gemini client initialized with hackathon optimizations")
    
    @HackathonOptimizer.cache_aggressive(ttl=3600)
    @HackathonOptimizer.rate_limit_protection(calls_per_minute=30)
    @HackathonOptimizer.fast_response
    async def generate(
        self, 
        prompt: str, 
        context: Optional[str] = None,
        force_model: Optional[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 1000
    ) -> str:
        """
        Generate response from Gemini with optimizations
        
        Args:
            prompt: Main prompt
            context: Additional context
            force_model: Force specific model (flash/pro)
            temperature: Generation temperature
            max_tokens: Maximum tokens to generate
            
        Returns:
            Generated text response
        """
        # Check daily limit
        if self._should_reset_daily_limit():
            self.daily_calls = 0
            self.last_reset = datetime.now()
        
        if self.daily_calls >= settings.max_daily_gemini_calls:
            logger.warning("Daily Gemini call limit reached")
            if DEMO_MODE or HACKATHON_MODE:
                return self._get_demo_response(prompt)
            raise Exception("Daily API limit reached")
        
        # Create full prompt
        full_prompt = self._create_full_prompt(prompt, context)
        
        # Check cache
        cache_key = self._get_cache_key(full_prompt)
        cached = self._get_from_cache(cache_key)
        if cached:
            logger.info("Cache hit for Gemini request")
            return cached
        
        # Select model
        model = self._select_model(prompt, force_model)
        
        # Check if we're in demo mode or models not initialized
        if not model:
            logger.warning("No model available, using demo response")
            response = self._get_demo_response(prompt)
            self._add_to_cache(cache_key, response)
            return response
        
        try:
            # Generate response
            response = await self._generate_with_model(
                model, 
                full_prompt,
                temperature,
                max_tokens
            )
            
            # Cache response
            self._add_to_cache(cache_key, response)
            
            # Update counters
            self.daily_calls += 1
            
            return response
            
        except Exception as e:
            logger.error(f"Gemini generation failed: {e}")
            # Always fallback to demo response on error
            logger.warning("Falling back to demo response due to error")
            return self._get_demo_response(prompt)
    
    async def generate_batch(
        self, 
        prompts: List[str], 
        context: Optional[str] = None
    ) -> List[str]:
        """
        Batch process multiple prompts efficiently
        
        Args:
            prompts: List of prompts
            context: Shared context for all prompts
            
        Returns:
            List of responses
        """
        # Check for cached responses first
        responses = []
        uncached_prompts = []
        uncached_indices = []
        
        for i, prompt in enumerate(prompts):
            cache_key = self._get_cache_key(prompt + (context or ""))
            cached = self._get_from_cache(cache_key)
            if cached:
                responses.append(cached)
            else:
                responses.append(None)
                uncached_prompts.append(prompt)
                uncached_indices.append(i)
        
        # Process uncached prompts
        if uncached_prompts:
            # Batch into single request if possible
            if len(uncached_prompts) <= 3:
                batch_prompt = self._create_batch_prompt(uncached_prompts, context)
                batch_response = await self.generate(batch_prompt)
                parsed_responses = self._parse_batch_response(batch_response)
                
                for i, resp in enumerate(parsed_responses):
                    if i < len(uncached_indices):
                        responses[uncached_indices[i]] = resp
            else:
                # Process individually with rate limiting
                for i, prompt in enumerate(uncached_prompts):
                    resp = await self.generate(prompt, context)
                    responses[uncached_indices[i]] = resp
                    await asyncio.sleep(0.1)  # Small delay between calls
        
        return responses
    
    def _create_full_prompt(self, prompt: str, context: Optional[str]) -> str:
        """Create full prompt with context"""
        if context:
            return f"Context:\n{context}\n\nRequest:\n{prompt}"
        return prompt
    
    def _get_cache_key(self, prompt: str) -> str:
        """Generate cache key from prompt"""
        return hashlib.md5(prompt.encode()).hexdigest()
    
    def _get_from_cache(self, key: str) -> Optional[str]:
        """Get value from cache if not expired"""
        if key in self.cache:
            value, timestamp = self.cache[key]
            if time.time() - timestamp < settings.cache_ttl:
                return value
            else:
                del self.cache[key]
        return None
    
    def _add_to_cache(self, key: str, value: str):
        """Add value to cache with timestamp"""
        self.cache[key] = (value, time.time())
        
        # Limit cache size
        if len(self.cache) > 1000:
            # Remove oldest entries
            sorted_cache = sorted(self.cache.items(), key=lambda x: x[1][1])
            self.cache = dict(sorted_cache[-500:])
    
    def _select_model(self, prompt: str, force_model: Optional[str]) -> genai.GenerativeModel:
        """Select appropriate model based on complexity"""
        if force_model:
            return self.flash_model if force_model == "flash" else self.pro_model
        
        # Assess complexity
        complexity = self._assess_complexity(prompt)
        
        # Use Flash for simple queries, Pro for complex
        if complexity < 0.7:
            logger.debug("Using Gemini Flash for simple query")
            return self.flash_model
        else:
            logger.debug("Using Gemini Pro for complex query")
            return self.pro_model
    
    def _assess_complexity(self, prompt: str) -> float:
        """Assess prompt complexity (0-1)"""
        # Simple heuristics for complexity
        complexity = 0.0
        
        # Length factor
        if len(prompt) > 500:
            complexity += 0.3
        elif len(prompt) > 200:
            complexity += 0.2
        
        # Keyword factors
        complex_keywords = [
            "analyze", "explain in detail", "compare", "evaluate",
            "create comprehensive", "design", "algorithm", "strategy"
        ]
        for keyword in complex_keywords:
            if keyword in prompt.lower():
                complexity += 0.2
        
        # Question type factors
        if "?" in prompt and prompt.count("?") > 2:
            complexity += 0.2
        
        return min(complexity, 1.0)
    
    async def _generate_with_model(
        self,
        model: genai.GenerativeModel,
        prompt: str,
        temperature: float,
        max_tokens: int
    ) -> str:
        """Generate response with specific model"""
        generation_config = genai.types.GenerationConfig(
            temperature=temperature,
            max_output_tokens=max_tokens,
            top_p=0.95,
            top_k=40
        )
        
        response = await model.generate_content_async(
            prompt,
            generation_config=generation_config,
            safety_settings=self.safety_settings
        )
        
        return response.text
    
    def _create_batch_prompt(self, prompts: List[str], context: Optional[str]) -> str:
        """Create a single prompt for batch processing"""
        batch_prompt = "Please answer the following questions separately:\n\n"
        
        if context:
            batch_prompt += f"Context for all questions:\n{context}\n\n"
        
        for i, prompt in enumerate(prompts, 1):
            batch_prompt += f"Question {i}: {prompt}\n\n"
        
        batch_prompt += "Please format your response with clear separators between answers."
        
        return batch_prompt
    
    def _parse_batch_response(self, response: str) -> List[str]:
        """Parse batch response into individual responses"""
        # Simple parsing - can be improved
        parts = response.split("\n\n")
        responses = []
        
        current_response = ""
        for part in parts:
            if part.startswith("Question") and current_response:
                responses.append(current_response.strip())
                current_response = ""
            else:
                current_response += part + "\n"
        
        if current_response:
            responses.append(current_response.strip())
        
        return responses
    
    def _should_reset_daily_limit(self) -> bool:
        """Check if daily limit should be reset"""
        return datetime.now() - self.last_reset > timedelta(days=1)
    
    def _get_demo_response(self, prompt: str) -> str:
        """Get demo response for fallback"""
        demo_responses = {
            "merhaba": "Merhaba! Ben YKS Genius, senin kişisel YKS koçun. Bugün sana nasıl yardımcı olabilirim? Matematik mi çalışalım, yoksa motivasyona mı ihtiyacın var?",
            "selam": "Selam! YKS yolculuğunda yanındayım. Hangi konuda destek istiyorsun? Çalışma planı hazırlayabilirim veya soru çözebiliriz!",
            "çalışma": "Senin için kişiselleştirilmiş bir çalışma planı hazırladım!\n\n• Matematik: Günde 2 saat (Limit, Türev)\n• Fizik: Günde 1.5 saat (Newton Yasaları)\n• Kimya: Günde 1 saat (Periyodik Tablo)\n\nBaşarıya giden yolda yanındayım!",
            "soru": "İşte senin için harika bir matematik sorusu:\n\nf(x) = x² + 3x - 4 fonksiyonunun x = 2 noktasındaki türevini bulunuz.\n\nİpucu: f'(x) = 2x + 3",
            "açıkla": "Bu konuyu sana detaylıca açıklayayım:\n\nLimit, bir fonksiyonun belirli bir noktaya yaklaşırken aldığı değerdir. Örneğin lim(x→2) x² = 4\n\nDaha fazla örnek ister misin?",
            "limit": "Limit konusunu açıklıyorum!\n\nLimit, bir fonksiyonun belirli bir x değerine yaklaşırken aldığı değerdir.\n\nÖrnek: lim(x→3) (x²-9)/(x-3)\n\nBu limiti çözmek için:\n1. Doğrudan yerine koyarsak 0/0 belirsizliği\n2. Payı çarpanlarına ayırırız: (x-3)(x+3)/(x-3)\n3. Sadeleştiririz: x+3\n4. x=3 yerine koyarız: 3+3 = 6\n\nDaha fazla örnek çözelim mi?",
            "analiz": "İlerleme analizin:\n\nSon 7 günde %15 gelişme\nMatematik: %78 başarı\nEn çok geliştiğin konu: Türev\nOdaklanman gereken: İntegral",
            "motivasyon": "Harika gidiyorsun!\n\nBugün 45 soru çözdün ve %82 başarı oranın var. Bu tempoda devam edersen hedefine kesinlikle ulaşacaksın!\n\nSenin başarına inanıyorum!",
            "yardım": "YKS'de başarılı olman için buradayım!\n\nNasıl yardımcı olabilirim?\n• Konu anlatımı\n• Soru çözümü\n• Çalışma planı\n• Motivasyon desteği",
            "plan": "Haftalık çalışma planın hazır!\n\nPazartesi: Matematik (Limit)\nSalı: Fizik (Hareket)\nÇarşamba: Kimya (Mol Kavramı)\nPerşembe: Biyoloji (Hücre)\nCuma: Türkçe (Paragraf)",
            "help": "YKS başarın için buradayım! İstediğin konuda sana yardımcı olabilirim."
        }
        
        prompt_lower = prompt.lower()
        for key, response in demo_responses.items():
            if key in prompt_lower:
                return response
        
        return "Merhaba! Ben YKS Genius, senin kişisel YKS koçun. Sana nasıl yardımcı olabilirim?"
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get current usage statistics"""
        cache_hit_rate = 0
        if self.cache:
            # Estimate based on current session
            cache_hit_rate = len(self.cache) / max(self.daily_calls, 1) * 100
        
        return {
            "daily_calls": self.daily_calls,
            "daily_limit": settings.max_daily_gemini_calls,
            "cache_size": len(self.cache),
            "cache_hit_rate": f"{cache_hit_rate:.1f}%",
            "last_reset": self.last_reset.isoformat()
        }