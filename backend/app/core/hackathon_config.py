"""
Hackathon-specific Yapılandırma for YKS Genius
Simplified ve optimized for BTK Akademi AI Hackathon 2025
"""

# Hackathon Requirements Summary:
# 1. Must use Gemini as primary LLM ✓
# 2. Education themed (YKS exam prep) ✓
# 3. Use AI agents (LangGraph) ✓
# 4. Focus üzerinde solving real user problems ✓
# 5. Clean code ve good architecture ✓
# 6. Demo video Gerekli (1 minute) ✓

# Core Features for Hackathon Demo:
DEMO_FEATURES = {
    "çalışma_planı": {
        "enabled": True,
        "description": "Kişiselleştirilmiş YKS çalışma planı",
        "agent": "strategy",
        "demo_time": 10  # seconds
    },
    "konu_anlatımı": {
        "enabled": True,
        "description": "YKS konularını anlaşılır anlatım",
        "agent": "content",
        "demo_time": 15
    },
    "soru_çözümü": {
        "enabled": True,
        "description": "Seviyeye uygun pratik sorular",
        "agent": "practice",
        "demo_time": 15
    },
    "performans_analizi": {
        "enabled": True,
        "description": "İlerleme takibi ve öneriler",
        "agent": "analytics",
        "demo_time": 10
    },
    "motivasyon_desteği": {
        "enabled": True,
        "description": "Motivasyon ve stres yönetimi",
        "agent": "mentor",
        "demo_time": 10
    }
}

# Simplified Ajan Messages for Demo
DEMO_AGENT_MESSAGES = {
    "strategy": [
        "Merhaba! Senin için özel bir YKS çalışma planı hazırlayacağım.",
        "Hangi alanlarda zorlanıyorsun? Matematik mi, Türkçe mi?",
        "Sınava kaç gün kaldı? Günde kaç saat çalışabilirsin?"
    ],
    "content": [
        "Hangi konuyu anlatmamı istersin?",
        "Bu konuyu sana en basit şekilde açıklayacağım!",
        "Örnek sorularla pekiştirelim mi?"
    ],
    "practice": [
        "Hadi pratik yapalım! Hangi dersten soru çözmek istersin?",
        "Seviyene uygun sorular hazırlıyorum...",
        "Doğru cevap! Harikasın!"
    ],
    "analytics": [
        "Performansını analiz ediyorum... 📊",
        "Son 1 haftada %15 ilerleme kaydetmişsin!",
        "En çok Matematik'te gelişme var, tebrikler!"
    ],
    "mentor": [
        "Hey, nasılsın? Stresli mi hissediyorsun? 🤗",
        "Unutma, her gün küçük adımlarla hedefe yaklaşıyorsun!",
        "Başaracaksın, sana inanıyorum! 💪"
    ]
}

# Simplified prompts for consistent Turkish responses
TURKISH_SYSTEM_PROMPTS = {
    "base": """
Sen YKS sınavına hazırlanan öğrencilere yardımcı olan bir AI asistansın.

KURALLAR:
1. Her zaman Türkçe konuş
2. Öğrenciye "sen" diye hitap et
3. Samimi ve motive edici ol
4. Kısa ve net açıklamalar yap
5. Uygun yerlerde emoji kullan
6. YKS'ye özel tavsiyeler ver
""",
    
    "demo_mode": """
Bu bir demo/sunum modu. Hızlı ve etkileyici yanıtlar ver.
Gerçekçi ama kısa tut. Maksimum 2-3 cümle.
"""
}

# Simplified routing keywords
ROUTING_KEYWORDS = {
    "strategy": ["plan", "program", "çalışma", "strateji", "haftalık", "günlük"],
    "content": ["anlat", "açıkla", "konu", "ders", "öğret", "nedir"],
    "practice": ["soru", "test", "pratik", "çöz", "deneme", "alıştırma"],
    "analytics": ["performans", "ilerleme", "analiz", "rapor", "başarı", "istatistik"],
    "mentor": ["motivasyon", "stres", "sıkıl", "yapamıyorum", "zor", "moral"]
}

# Demo Öğrenci profiles for testing
DEMO_STUDENTS = {
    "beginner": {
        "name": "Ayşe",
        "level": "Başlangıç",
        "weak_subjects": ["Matematik", "Fizik"],
        "strong_subjects": ["Türkçe", "Tarih"],
        "daily_hours": 4,
        "days_until_exam": 180
    },
    "intermediate": {
        "name": "Mehmet",
        "level": "Orta",
        "weak_subjects": ["Kimya"],
        "strong_subjects": ["Matematik", "Türkçe"],
        "daily_hours": 6,
        "days_until_exam": 120
    },
    "advanced": {
        "name": "Zeynep",
        "level": "İleri",
        "weak_subjects": ["Geometri"],
        "strong_subjects": ["Fizik", "Kimya", "Biyoloji"],
        "daily_hours": 8,
        "days_until_exam": 60
    }
}

# Yanıt Zaman limits for smooth demo
DEMO_RESPONSE_TIMES = {
    "instant": 0.5,  # seconds
    "fast": 1.0,
    "normal": 2.0,
    "thinking": 3.0
}

# Simplified YKS exam structure
YKS_STRUCTURE = {
    "TYT": {
        "name": "Temel Yeterlilik Testi",
        "weight": 0.4,
        "subjects": {
            "Türkçe": 40,
            "Matematik": 40,
            "Sosyal Bilimler": 20,
            "Fen Bilimleri": 20
        }
    },
    "AYT": {
        "name": "Alan Yeterlilik Testi",
        "weight": 0.6,
        "types": {
            "SAY": "Sayısal",
            "EA": "Eşit Ağırlık",
            "SÖZ": "Sözel"
        }
    }
}

# Hata messages de Turkish
ERROR_MESSAGES = {
    "no_gemini_key": "⚠️ Gemini API anahtarı bulunamadı! Lütfen .env dosyasına ekleyin.",
    "agent_error": "😅 Küçük bir sorun oldu, hemen düzeltiyorum...",
    "rate_limit": "🚦 Çok hızlı gidiyoruz! Biraz yavaşlayalım...",
    "network_error": "🌐 İnternet bağlantısını kontrol eder misin?",
    "unknown_error": "🤔 Beklenmeyen bir durum oldu, tekrar dener misin?"
}

# Başarılı messages for positive reinforcement
SUCCESS_MESSAGES = [
    "Harika gidiyorsun! 🎉",
    "İşte bu! Devam et! 💪",
    "Süpersin! 🌟",
    "Başarıya bir adım daha yaklaştın! 🎯",
    "Muhteşem bir ilerleme! 🚀"
]