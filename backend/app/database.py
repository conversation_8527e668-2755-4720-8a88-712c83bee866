"""
Database connection and session management for YKS Genius
PostgreSQL with SQLAlchemy ORM
"""

from sqlalchemy import create_engine, event
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
import logging
from contextlib import contextmanager
from typing import Generator

from .config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

# Create SQLAlchemy engine with connection pooling
engine = create_engine(
    settings.database_url,
    poolclass=QueuePool,
    pool_size=settings.db_pool_size,
    max_overflow=settings.db_max_overflow,
    pool_pre_ping=True,  # Verify connections before use
    pool_recycle=3600,   # Recycle connections every hour
    echo=settings.debug   # Log SQL statements in debug mode
)

# Create session factory
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine
)

# Create declarative base for models
Base = declarative_base()

# Database dependency for FastAPI
def get_db() -> Generator[Session, None, None]:
    """
    Database session dependency for FastAPI endpoints
    Provides automatic session cleanup
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        logger.error(f"Database session error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

@contextmanager
def get_db_context() -> Generator[Session, None, None]:
    """
    Context manager for database sessions
    Use in non-FastAPI contexts
    """
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception as e:
        logger.error(f"Database transaction error: {e}")
        db.rollback()
        raise
    finally:
        db.close()

async def init_database():
    """
    Initialize database tables and relationships
    Called during application startup
    """
    try:
        logger.info("Initializing database tables...")
        
        # Import all models to ensure they're registered
        from .models import student, question, session, progress, agent_interaction
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        
        logger.info("✅ Database tables initialized successfully")
        
        # Load sample data for demo
        await load_demo_data()
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise

async def load_demo_data():
    """
    Load sample data for hackathon demo
    """
    try:
        with get_db_context() as db:
            # Check if demo data already exists
            from .models.student import Student
            
            existing_student = db.query(Student).filter(
                Student.email == "<EMAIL>"
            ).first()
            
            if existing_student:
                logger.info("Demo data already exists, skipping...")
                return
            
            logger.info("Loading demo data...")
            
            # Create demo student
            demo_student = Student(
                name="Demo Öğrenci",
                email="<EMAIL>",
                grade_level=12,
                target_university="Boğaziçi Üniversitesi",
                target_department="Bilgisayar Mühendisliği",
                study_preferences={
                    "preferred_study_time": "evening",
                    "learning_style": "visual",
                    "difficulty_preference": "adaptive",
                    "subjects_of_interest": ["matematik", "fizik", "kimya"]
                }
            )
            db.add(demo_student)
            
            # Create sample questions
            from .models.question import Question
            
            sample_questions = [
                Question(
                    subject="matematik",
                    topic="limit",
                    difficulty="orta",
                    yks_type="TYT",
                    content="lim(x→2) (x² - 4)/(x - 2) ifadesinin değeri kaçtır?",
                    options={
                        "A": "0",
                        "B": "2", 
                        "C": "4",
                        "D": "∞",
                        "E": "tanımsız"
                    },
                    correct_answer="C",
                    explanation="Limit kuralları kullanılarak hesaplanır",
                    tags=["limit", "cebirsel_limit", "belirsizlik"]
                ),
                Question(
                    subject="fizik",
                    topic="hareket",
                    difficulty="kolay",
                    yks_type="TYT", 
                    content="5 m/s hızla hareket eden bir cisim 3 saniyede kaç metre yol alır?",
                    options={
                        "A": "8 m",
                        "B": "15 m",
                        "C": "20 m", 
                        "D": "25 m",
                        "E": "30 m"
                    },
                    correct_answer="B",
                    explanation="Yol = Hız × Zaman = 5 × 3 = 15 m",
                    tags=["düzgün_hareket", "hız", "zaman"]
                )
            ]
            
            for question in sample_questions:
                db.add(question)
            
            db.commit()
            logger.info("✅ Demo data loaded successfully")
            
    except Exception as e:
        logger.error(f"Failed to load demo data: {e}")
        # Don't raise - demo data is not critical

def check_database_connection() -> bool:
    """
    Check if database connection is working
    Returns True if connection is successful
    """
    try:
        with engine.connect() as connection:
            connection.execute("SELECT 1")
        return True
    except Exception as e:
        logger.error(f"Database connection check failed: {e}")
        return False

# Event listeners for connection management
@event.listens_for(engine, "connect")
def receive_connect(dbapi_connection, connection_record):
    """Log successful database connections"""
    logger.debug("Database connection established")

@event.listens_for(engine, "checkout")
def receive_checkout(dbapi_connection, connection_record, connection_proxy):
    """Log connection checkout from pool"""
    logger.debug("Database connection checked out from pool")

@event.listens_for(engine, "checkin")
def receive_checkin(dbapi_connection, connection_record):
    """Log connection checkin to pool"""
    logger.debug("Database connection returned to pool")

# Health check function
def get_database_status() -> dict:
    """
    Get database status information for health checks
    """
    try:
        is_connected = check_database_connection()
        
        # Get connection pool status
        pool_status = {
            "size": engine.pool.size(),
            "checked_in": engine.pool.checkedin(),
            "checked_out": engine.pool.checkedout(),
        }
        
        return {
            "status": "healthy" if is_connected else "unhealthy",
            "connected": is_connected,
            "pool_status": pool_status,
            "database_url": settings.database_url.split("@")[1] if "@" in settings.database_url else "configured"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "connected": False,
            "error": str(e)
        }