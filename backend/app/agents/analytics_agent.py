"""
<PERSON><PERSON><PERSON> - <PERSON><PERSON> için öğrenci performansını takip eder ve analiz eder
İçgörüler, il<PERSON><PERSON>e takibi ve performans tahminleri sağlar
"""

import json
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timezone, timedelta
import logging
import statistics
from collections import defaultdict

from .base_agent import BaseAgent, AgentRole, AgentResponse
from ..core.hackathon_optimizations import HackathonOptimizer, DEMO_MODE, DemoDataGenerator

logger = logging.getLogger(__name__)

class AnalyticsAgent(BaseAgent):
    """
    Ana<PERSON>ik <PERSON> responsible for:
    - Tracking <PERSON><PERSON><PERSON><PERSON>e
    - Analyzing Performans patterns
    - Predicting Başarılı rates
    - Identifying improvement areas
    - Generating Performans reports
    """
    
    def __init__(self, gemini_client, database=None):
        super().__init__(
            name="<PERSON><PERSON><PERSON>",
            role=AgentRole.ANALYTICS,
            gemini_client=gemini_client,
            description="YKS performansını analiz edip öneriler sunarım"
        )
        
        self.database = database  # For storing/retrieving Performans data
        self.capabilities = [
            "track_progress",
            "analyze_performance",
            "predict_success",
            "identify_weaknesses",
            "generate_report",
            "compare_peers"
        ]
        
        # Performans Metrikler e track
        self.metrics = {
            "accuracy": [],
            "speed": [],
            "consistency": [],
            "improvement_rate": [],
            "study_time": [],
            "topic_mastery": {}
        }
        
        # YKS score calculation weights
        self.score_weights = {
            "TYT": {"weight": 0.4, "max_score": 500},
            "AYT": {"weight": 0.6, "max_score": 500}
        }
    
    async def process(self, state: Dict[str, Any]) -> AgentResponse:
        """İşle Analitik-related requests"""
        request_type = state.get("request_type", "")
        student_data = state.get("student_data", {})
        
        try:
            if request_type == "track_progress":
                return await self._track_progress(student_data)
            elif request_type == "analyze_performance":
                return await self._analyze_performance(student_data)
            elif request_type == "predict_success":
                return await self._predict_success(student_data)
            elif request_type == "identify_weaknesses":
                return await self._identify_weaknesses(student_data)
            elif request_type == "generate_report":
                return await self._generate_report(student_data)
            elif request_type == "compare_peers":
                return await self._compare_with_peers(student_data)
            else:
                return await self._general_analytics_help(state)
                
        except Exception as e:
            logger.error(f"Analytics Agent error: {e}")
            return self.create_response(
                success=False,
                data={},
                message=f"Analytics error: {str(e)}"
            )
    
    def can_handle(self, request_type: str, state: Dict[str, Any]) -> bool:
        """Check eğer bu Ajan can İşle the İstek"""
        analytics_keywords = [
            "ilerleme", "progress", "analiz", "performans",
            "başarı", "istatistik", "rapor", "gelişim",
            "karşılaştır", "tahmin", "öngörü"
        ]
        
        # Check İstek type
        if request_type in self.capabilities:
            return True
        
        # Check message İçerik
        message = state.get("message", "").lower()
        return any(keyword in message for keyword in analytics_keywords)
    
    @HackathonOptimizer.fast_response
    async def _track_progress(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Track Öğrenci's learning İlerleme"""
        
        student_id = student_data.get("id", "")
        timeframe = student_data.get("timeframe", "week")  # week, month, all
        
        # Get historical data
        historical_data = await self._get_historical_data(student_id, timeframe)
        
        # Hesapla İlerleme Metrikler
        progress_metrics = self._calculate_progress_metrics(historical_data)
        
        # Use Gemini for insights
        prompt = f"""
        Öğrencinin ilerleme verilerini analiz et ve yorum yap.
        
        İlerleme Metrikleri:
        {json.dumps(progress_metrics, ensure_ascii=False)}
        
        Zaman aralığı: {timeframe}
        
        Analiz et:
        1. Genel ilerleme trendi (artış/düşüş/stabil)
        2. En çok gelişim gösteren alanlar
        3. Gelişim hızı değerlendirmesi
        4. Motivasyon ve tutarlılık analizi
        5. Önümüzdeki dönem tahminleri
        
        Somut verilerle destekle ve motive edici bir dille yaz.
        """
        
        analysis = await self.call_gemini(prompt)
        
        # Oluştur İlerleme visualization data
        progress_data = {
            "metrics": progress_metrics,
            "analysis": analysis,
            "charts": self._create_progress_charts(historical_data),
            "milestones": self._identify_milestones(historical_data),
            "streak": self._calculate_study_streak(historical_data),
            "badges": self._award_badges(progress_metrics)
        }
        
        return self.create_response(
            success=True,
            data=progress_data,
            message="İlerleme analiziniz hazır! 📈",
            next_action="show_progress_dashboard"
        )
    
    async def _analyze_performance(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Comprehensive Performans analysis"""
        
        # Collect all Performans data
        performance_data = await self._collect_performance_data(student_data)
        
        # Hesapla Anahtar Metrikler
        metrics = {
            "overall_accuracy": self._calculate_accuracy(performance_data),
            "subject_performance": self._analyze_by_subject(performance_data),
            "topic_mastery": self._analyze_by_topic(performance_data),
            "time_efficiency": self._analyze_time_efficiency(performance_data),
            "difficulty_progression": self._analyze_difficulty_progression(performance_data),
            "consistency_score": self._calculate_consistency(performance_data)
        }
        
        # Üret insights ile Gemini
        prompt = f"""
        Detaylı performans analizi yap.
        
        Performans Verileri:
        {json.dumps(Metrikler, ensure_ascii=False)}
        
        Analiz konuları:
        1. Güçlü ve zayıf yönler
        2. Öğrenme hızı ve verimliliği
        3. Zaman yönetimi başarısı
        4. Konu bazında derinlemesine analiz
        5. Gelişim potansiyeli ve öneriler
        
        Veriye dayalı, objektif ve yapıcı bir analiz sun.
        Her tespit için somut örnekler ver.
        """
        
        detailed_analysis = await self.call_gemini(prompt)
        
        # Structure the analysis
        analysis_data = {
            "metrics": metrics,
            "detailed_analysis": detailed_analysis,
            "strengths": self._identify_strengths(metrics),
            "weaknesses": self._identify_weaknesses_from_metrics(metrics),
            "recommendations": self._generate_recommendations(metrics),
            "performance_score": self._calculate_overall_score(metrics)
        }
        
        return self.create_response(
            success=True,
            data=analysis_data,
            message="Detaylı performans analiziniz hazır! 🔍",
            next_action="show_detailed_analysis"
        )
    
    async def _predict_success(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Predict YKS Başarılı probability"""
        
        # Get comprehensive Öğrenci data
        current_performance = student_data.get("performance", {})
        study_habits = student_data.get("study_habits", {})
        time_until_exam = student_data.get("days_until_exam", 180)
        
        # Hesapla prediction factors
        prediction_factors = {
            "current_level": self._assess_current_level(current_performance),
            "improvement_rate": self._calculate_improvement_rate(student_data),
            "consistency": self._assess_consistency(study_habits),
            "time_efficiency": self._assess_time_efficiency(student_data),
            "coverage": self._assess_topic_coverage(current_performance)
        }
        
        # Use Gemini for prediction
        prompt = f"""
        YKS başarı tahminlemesi yap.
        
        Öğrenci Verileri:
        - Mevcut performans: {json.dumps(current_performance, ensure_ascii=False)}
        - Sınava kalan süre: {time_until_exam} gün
        - Tahmin faktörleri: {json.dumps(prediction_factors, ensure_ascii=False)}
        
        Tahminle:
        1. Mevcut gidişatla başarı olasılığı (%)
        2. Hedef puan aralığı (TYT ve AYT için ayrı)
        3. Sıralama tahmini
        4. Güçlü olduğu üniversite/bölüm türleri
        5. Başarıyı artırmak için kritik faktörler
        
        Gerçekçi ve motive edici bir tahmin sun.
        """
        
        prediction_analysis = await self.call_gemini(prompt)
        
        # Hesapla Başarılı probability
        success_probability = self._calculate_success_probability(prediction_factors)
        
        # Oluştur prediction data
        prediction_data = {
            "success_probability": success_probability,
            "predicted_scores": self._predict_scores(prediction_factors, time_until_exam),
            "ranking_estimate": self._estimate_ranking(success_probability),
            "university_matches": self._find_university_matches(prediction_factors),
            "improvement_potential": self._calculate_improvement_potential(
                prediction_factors, 
                time_until_exam
            ),
            "critical_factors": self._identify_critical_factors(prediction_factors),
            "detailed_prediction": prediction_analysis
        }
        
        return self.create_response(
            success=True,
            data=prediction_data,
            message="YKS başarı tahmininiz hazır! 🎯",
            next_action="show_success_prediction"
        )
    
    async def _identify_weaknesses(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Identify ve analyze weak areas"""
        
        performance_data = await self._collect_performance_data(student_data)
        
        # Analyze weaknesses de multiple levels
        weaknesses = {
            "subject_weaknesses": self._find_weak_subjects(performance_data),
            "topic_weaknesses": self._find_weak_topics(performance_data),
            "skill_weaknesses": self._find_weak_skills(performance_data),
            "time_management_issues": self._analyze_time_issues(performance_data),
            "consistency_problems": self._analyze_consistency_issues(performance_data)
        }
        
        # Get root cause analysis den Gemini
        prompt = f"""
        Öğrencinin zayıf alanlarını derinlemesine analiz et.
        
        Tespit edilen zayıflıklar:
        {json.dumps(weaknesses, ensure_ascii=False)}
        
        Analiz et:
        1. Her zayıflığın kök nedenleri
        2. Zayıflıklar arası ilişkiler
        3. Öncelik sıralaması (hangisi en kritik)
        4. Gelişim stratejileri
        5. Gerçekçi iyileşme süresi tahminleri
        
        Yapıcı ve çözüm odaklı bir yaklaşım kullan.
        """
        
        root_cause_analysis = await self.call_gemini(prompt)
        
        # Oluştur improvement plan
        improvement_plan = self._create_improvement_plan_from_weaknesses(weaknesses)
        
        weakness_data = {
            "identified_weaknesses": weaknesses,
            "root_cause_analysis": root_cause_analysis,
            "improvement_plan": improvement_plan,
            "priority_matrix": self._create_priority_matrix(weaknesses),
            "estimated_improvement_time": self._estimate_improvement_time(weaknesses)
        }
        
        return self.create_response(
            success=True,
            data=weakness_data,
            message="Zayıf alan analizi tamamlandı! 🔍",
            next_action="show_improvement_plan"
        )
    
    async def _generate_report(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Üret comprehensive Performans report"""
        
        report_type = student_data.get("report_type", "weekly")
        
        # Gather all necessary data
        report_data = {
            "student_info": student_data,
            "performance_summary": await self._get_performance_summary(student_data),
            "progress_metrics": await self._get_progress_metrics(student_data),
            "subject_analysis": await self._get_subject_analysis(student_data),
            "time_analysis": await self._get_time_analysis(student_data),
            "prediction": await self._get_success_prediction(student_data)
        }
        
        # Üret report ile Gemini
        prompt = f"""
        {report_type} YKS performans raporu oluştur.
        
        Rapor Verileri:
        {json.dumps(report_data, ensure_ascii=False)}
        
        Rapor bölümleri:
        1. Özet (önemli başarılar ve gelişmeler)
        2. Detaylı performans analizi
        3. Konu bazında ilerleme
        4. Zaman yönetimi analizi
        5. Gelecek dönem hedefleri
        6. Öneriler ve aksiyon planı
        
        Profesyonel, veri odaklı ve motive edici bir rapor hazırla.
        Görsel öğeler için öneriler ekle (grafikler, tablolar).
        """
        
        report_content = await self.call_gemini(prompt)
        
        # Structure the report
        structured_report = {
            "report_type": report_type,
            "generated_at": datetime.now(timezone.utc).isoformat(),
            "summary": self._extract_summary(report_content),
            "detailed_content": report_content,
            "visualizations": self._create_report_visualizations(report_data),
            "key_metrics": self._extract_key_metrics(report_data),
            "action_items": self._extract_action_items(report_content),
            "next_report_date": self._calculate_next_report_date(report_type)
        }
        
        return self.create_response(
            success=True,
            data=structured_report,
            message=f"{report_type.capitalize()} raporunuz hazır! 📊",
            next_action="download_report"
        )
    
    async def _compare_with_peers(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Compare Performans ile peer group"""
        
        # de demo mode, Üret realistic peer data
        if DEMO_MODE:
            peer_data = self._generate_demo_peer_data()
        else:
            peer_data = await self._get_peer_data(student_data)
        
        # Hesapla comparative Metrikler
        comparison = {
            "student_metrics": await self._get_student_metrics(student_data),
            "peer_average": self._calculate_peer_average(peer_data),
            "percentile_rank": self._calculate_percentile_rank(student_data, peer_data),
            "relative_strengths": self._find_relative_strengths(student_data, peer_data),
            "areas_behind": self._find_areas_behind(student_data, peer_data)
        }
        
        # Üret comparison analysis
        prompt = f"""
        Öğrenciyi akranlarıyla karşılaştır.
        
        Karşılaştırma Verileri:
        {json.dumps(comparison, ensure_ascii=False)}
        
        Analiz et:
        1. Genel sıralama ve pozisyon
        2. Akranlarına göre güçlü yönler
        3. Geliştirilmesi gereken alanlar
        4. Motivasyon mesajları
        5. Rekabet stratejileri
        
        Yapıcı ve motive edici bir dille yaz.
        Kıyaslama yaparken cesaretlendirici ol.
        """
        
        peer_analysis = await self.call_gemini(prompt)
        
        comparison_data = {
            "comparison_metrics": comparison,
            "analysis": peer_analysis,
            "ranking_chart": self._create_ranking_chart(comparison),
            "competitive_advantages": self._identify_competitive_advantages(comparison),
            "improvement_targets": self._set_competitive_targets(comparison)
        }
        
        return self.create_response(
            success=True,
            data=comparison_data,
            message="Akran karşılaştırması hazır! 👥",
            next_action="show_peer_comparison"
        )
    
    async def _general_analytics_help(self, state: Dict[str, Any]) -> AgentResponse:
        """Provide general Analitik assistance"""
        
        message = state.get("message", "")
        context = self.get_memory_context()
        
        prompt = f"""
        YKS analiz uzmanı olarak öğrenciye yardım et.
        
        Öğrenci sorusu: {message}
        
        Önceki analizler:
        {context}
        
        Yardım konuları:
        1. Performans metrikleri açıklama
        2. İlerleme takibi
        3. Başarı tahminleri
        4. Veri yorumlama
        
        Veriye dayalı, anlaşılır tavsiyeler ver.
        """
        
        response = await self.call_gemini(prompt)
        
        return self.create_response(
            success=True,
            data={"advice": response},
            message="Analiz tavsiyeleri hazır! 📈"
        )
    
    # Helper methods for data collection ve analysis
    
    async def _get_historical_data(
        self, 
        student_id: str, 
        timeframe: str
    ) -> Dict[str, Any]:
        """Get historical Performans data"""
        # Would query Veritabanı
        # For now, Üret demo data
        if DEMO_MODE:
            return DemoDataGenerator.generate_student_progress()
        
        return {
            "student_id": student_id,
            "timeframe": timeframe,
            "data_points": []
        }
    
    def _calculate_progress_metrics(self, historical_data: Dict) -> Dict[str, Any]:
        """Hesapla İlerleme Metrikler den historical data"""
        return {
            "overall_improvement": 23.5,
            "weekly_average": 85,
            "consistency_score": 0.82,
            "momentum": "positive",
            "projected_improvement": 35
        }
    
    def _create_progress_charts(self, data: Dict) -> List[Dict]:
        """Oluştur chart data for İlerleme visualization"""
        return [
            {
                "type": "line",
                "title": "Genel İlerleme",
                "data": {"weeks": [1, 2, 3, 4], "scores": [65, 70, 78, 85]}
            },
            {
                "type": "bar",
                "title": "Konu Bazında Gelişim",
                "data": {"subjects": ["Mat", "Fen", "Tür"], "improvement": [25, 18, 22]}
            }
        ]
    
    def _identify_milestones(self, data: Dict) -> List[Dict]:
        """Identify achieved milestones"""
        return [
            {"date": "2024-01-15", "achievement": "İlk 90+ puan"},
            {"date": "2024-01-22", "achievement": "7 günlük çalışma serisi"},
            {"date": "2024-01-28", "achievement": "Matematik'te %80 başarı"}
        ]
    
    def _calculate_study_streak(self, data: Dict) -> int:
        """Hesapla consecutive study days"""
        return data.get("study_streak", 15)
    
    def _award_badges(self, metrics: Dict) -> List[Dict]:
        """Award achievement badges"""
        badges = []
        
        if metrics.get("overall_improvement", 0) > 20:
            badges.append({"name": "Hızlı Gelişim", "icon": "🚀"})
        
        if metrics.get("consistency_score", 0) > 0.8:
            badges.append({"name": "İstikrarlı Çalışma", "icon": "💎"})
        
        return badges
    
    async def _collect_performance_data(self, student_data: Dict) -> Dict:
        """Collect all Performans data"""
        # Would aggregate den multiple sources
        return {
            "questions_solved": 1247,
            "accuracy_by_subject": {
                "matematik": 0.78,
                "fizik": 0.65,
                "kimya": 0.71,
                "biyoloji": 0.82
            },
            "time_data": {
                "average_per_question": 1.8,
                "total_study_hours": 124
            }
        }
    
    def _calculate_accuracy(self, data: Dict) -> float:
        """Hesapla overall accuracy"""
        accuracies = data.get("accuracy_by_subject", {}).values()
        return statistics.mean(accuracies) if accuracies else 0.0
    
    def _analyze_by_subject(self, data: Dict) -> Dict[str, Dict]:
        """Analyze Performans tarafından subject"""
        return {
            subject: {
                "accuracy": acc,
                "trend": "improving" if acc > 0.7 else "needs_work",
                "questions_solved": int(data.get("questions_solved", 0) / 4)
            }
            for subject, acc in data.get("accuracy_by_subject", {}).items()
        }
    
    def _analyze_by_topic(self, data: Dict) -> Dict[str, float]:
        """Analyze Performans tarafından topic"""
        # Would have detailed topic analysis
        return {
            "Limit ve Süreklilik": 0.82,
            "Türev": 0.75,
            "İntegral": 0.68
        }
    
    def _analyze_time_efficiency(self, data: Dict) -> Dict[str, Any]:
        """Analyze Zaman management efficiency"""
        avg_time = data.get("time_data", {}).get("average_per_question", 2.0)
        
        return {
            "average_time": avg_time,
            "efficiency_score": min(1.0, 1.5 / avg_time),
            "status": "efficient" if avg_time < 2 else "needs_improvement"
        }
    
    def _analyze_difficulty_progression(self, data: Dict) -> Dict[str, Any]:
        """Analyze nasıl Öğrenci handles difficulty progression"""
        return {
            "easy_success": 0.95,
            "medium_success": 0.75,
            "hard_success": 0.45,
            "adaptation_rate": 0.7
        }
    
    def _calculate_consistency(self, data: Dict) -> float:
        """Hesapla Performans consistency"""
        # Would Hesapla variance de Performans
        return 0.82
    
    def _identify_strengths(self, metrics: Dict) -> List[str]:
        """Identify Öğrenci strengths"""
        strengths = []
        
        if metrics.get("overall_accuracy", 0) > 0.8:
            strengths.append("Yüksek doğruluk oranı")
        
        subject_perf = metrics.get("subject_performance", {})
        for subject, data in subject_perf.items():
            if data.get("accuracy", 0) > 0.85:
                strengths.append(f"{subject.capitalize()}'de güçlü")
        
        return strengths
    
    def _identify_weaknesses_from_metrics(self, metrics: Dict) -> List[str]:
        """Identify weaknesses den Metrikler"""
        weaknesses = []
        
        if metrics.get("time_efficiency", {}).get("status") == "needs_improvement":
            weaknesses.append("Zaman yönetimi")
        
        subject_perf = metrics.get("subject_performance", {})
        for subject, data in subject_perf.items():
            if data.get("accuracy", 0) < 0.6:
                weaknesses.append(f"{subject.capitalize()} konularında eksiklik")
        
        return weaknesses
    
    def _generate_recommendations(self, metrics: Dict) -> List[str]:
        """Üret personalized recommendations"""
        recommendations = []
        
        # Zaman-based recommendations
        if metrics.get("time_efficiency", {}).get("status") == "needs_improvement":
            recommendations.append("Günlük 10 hızlı soru çözümü yapın")
        
        # Subject-based recommendations
        weak_subjects = [
            s for s, d in metrics.get("subject_performance", {}).items() 
            if d.get("accuracy", 0) < 0.7
        ]
        
        for subject in weak_subjects:
            recommendations.append(f"{subject.capitalize()} için günde 1 saat ayırın")
        
        return recommendations
    
    def _calculate_overall_score(self, metrics: Dict) -> float:
        """Hesapla overall Performans score"""
        factors = [
            metrics.get("overall_accuracy", 0),
            metrics.get("time_efficiency", {}).get("efficiency_score", 0),
            metrics.get("consistency_score", 0)
        ]
        
        return round(statistics.mean(factors) * 100, 1)
    
    def _assess_current_level(self, performance: Dict) -> float:
        """Assess Akım Performans level"""
        if not performance:
            return 0.5
        
        return statistics.mean(performance.values()) / 100
    
    def _calculate_improvement_rate(self, student_data: Dict) -> float:
        """Hesapla rate nin improvement"""
        # Would Hesapla den historical data
        return 0.15  # 15% improvement rate
    
    def _assess_consistency(self, study_habits: Dict) -> float:
        """Assess study consistency"""
        return study_habits.get("consistency_score", 0.7)
    
    def _assess_time_efficiency(self, student_data: Dict) -> float:
        """Assess Zaman efficiency"""
        return 0.75
    
    def _assess_topic_coverage(self, performance: Dict) -> float:
        """Assess Müfredat coverage"""
        # Would check nasıl many topics have been studied
        return 0.6
    
    def _calculate_success_probability(self, factors: Dict) -> float:
        """Hesapla overall Başarılı probability"""
        weights = {
            "current_level": 0.3,
            "improvement_rate": 0.25,
            "consistency": 0.2,
            "time_efficiency": 0.15,
            "coverage": 0.1
        }
        
        probability = sum(
            factors.get(factor, 0) * weight 
            for factor, weight in weights.items()
        )
        
        return round(probability * 100, 1)
    
    def _predict_scores(self, factors: Dict, days_until_exam: int) -> Dict[str, Dict]:
        """Predict exam scores"""
        current_level = factors.get("current_level", 0.5)
        improvement_rate = factors.get("improvement_rate", 0.1)
        
        # Simple projection
        projected_level = min(1.0, current_level + (improvement_rate * days_until_exam / 30))
        
        return {
            "TYT": {
                "min": int(projected_level * 300),
                "max": int(projected_level * 400),
                "expected": int(projected_level * 350)
            },
            "AYT": {
                "min": int(projected_level * 250),
                "max": int(projected_level * 350),
                "expected": int(projected_level * 300)
            }
        }
    
    def _estimate_ranking(self, success_probability: float) -> Dict[str, int]:
        """Estimate ranking based üzerinde Başarılı probability"""
        # Rough estimation
        total_students = 2_250_000
        
        percentile = success_probability
        ranking = int(total_students * (1 - percentile / 100))
        
        return {
            "estimated_rank": ranking,
            "percentile": percentile,
            "total_students": total_students
        }
    
    def _find_university_matches(self, factors: Dict) -> List[Dict]:
        """Find matching universities based üzerinde predicted Performans"""
        # Would match ile university requirements
        return [
            {"name": "Boğaziçi Üniversitesi", "program": "Bilgisayar Müh.", "match": 85},
            {"name": "ODTÜ", "program": "Elektrik-Elektronik Müh.", "match": 82},
            {"name": "İTÜ", "program": "Makine Müh.", "match": 78}
        ]
    
    def _calculate_improvement_potential(
        self, 
        factors: Dict, 
        days_until_exam: int
    ) -> Dict[str, Any]:
        """Hesapla improvement Potansiyel"""
        current = factors.get("current_level", 0.5)
        max_improvement = min(0.4, days_until_exam / 365)  # Max 40% improvement
        
        return {
            "current_level": current * 100,
            "potential_level": (current + max_improvement) * 100,
            "improvement_percentage": max_improvement * 100,
            "required_daily_hours": max(2, 6 * (1 - current))
        }
    
    def _identify_critical_factors(self, factors: Dict) -> List[Dict]:
        """Identify critical Başarılı factors"""
        critical = []
        
        if factors.get("coverage", 0) < 0.7:
            critical.append({
                "factor": "Müfredat Tamamlama",
                "importance": "critical",
                "action": "Eksik konuları hızla tamamlayın"
            })
        
        if factors.get("consistency", 0) < 0.6:
            critical.append({
                "factor": "Düzenli Çalışma",
                "importance": "high",
                "action": "Günlük çalışma rutini oluşturun"
            })
        
        return critical
    
    def _find_weak_subjects(self, data: Dict) -> List[Dict]:
        """Find weak subjects"""
        weak = []
        
        for subject, accuracy in data.get("accuracy_by_subject", {}).items():
            if accuracy < 0.6:
                weak.append({
                    "subject": subject,
                    "current_accuracy": accuracy,
                    "target_accuracy": 0.8,
                    "gap": 0.8 - accuracy
                })
        
        return sorted(weak, key=lambda x: x["gap"], reverse=True)
    
    def _find_weak_topics(self, data: Dict) -> List[Dict]:
        """Find weak topics within subjects"""
        # Would analyze topic-level data
        return [
            {"topic": "İntegral", "accuracy": 0.45, "importance": "high"},
            {"topic": "Organik Kimya", "accuracy": 0.52, "importance": "medium"}
        ]
    
    def _find_weak_skills(self, data: Dict) -> List[str]:
        """Find weak cognitive skills"""
        return ["Hızlı problem çözme", "Grafik yorumlama", "Muhakeme"]
    
    def _analyze_time_issues(self, data: Dict) -> Dict[str, Any]:
        """Analyze Zaman management issues"""
        avg_time = data.get("time_data", {}).get("average_per_question", 2.0)
        
        return {
            "status": "slow" if avg_time > 2 else "acceptable",
            "average_time": avg_time,
            "target_time": 1.5,
            "improvement_needed": max(0, avg_time - 1.5)
        }
    
    def _analyze_consistency_issues(self, data: Dict) -> Dict[str, Any]:
        """Analyze consistency problems"""
        return {
            "daily_variation": 0.25,  # 25% variation
            "weekly_pattern": "inconsistent",
            "best_days": ["Pazartesi", "Çarşamba"],
            "worst_days": ["Cuma", "Cumartesi"]
        }
    
    def _create_improvement_plan_from_weaknesses(
        self, 
        weaknesses: Dict
    ) -> Dict[str, Any]:
        """Oluştur improvement plan den identified weaknesses"""
        return {
            "immediate_actions": [
                "Günde 2 saat zayıf konulara odaklan",
                "Hız pratiği için günlük 10 soru"
            ],
            "weekly_goals": [
                "Her zayıf konudan 50 soru çöz",
                "Zaman limitli deneme yap"
            ],
            "monthly_targets": [
                "Zayıf konularda %20 gelişim",
                "Ortalama çözüm süresi 1.5 dk"
            ]
        }
    
    def _create_priority_matrix(self, weaknesses: Dict) -> List[Dict]:
        """Oluştur priority matrix for weaknesses"""
        # Prioritize based üzerinde impact ve effort
        return [
            {
                "weakness": "İntegral",
                "impact": "high",
                "effort": "medium",
                "priority": 1
            },
            {
                "weakness": "Zaman yönetimi",
                "impact": "high",
                "effort": "low",
                "priority": 2
            }
        ]
    
    def _estimate_improvement_time(self, weaknesses: Dict) -> Dict[str, int]:
        """Estimate Zaman needed for improvement"""
        return {
            "subject_weaknesses": 30,  # days
            "skill_weaknesses": 45,
            "time_management": 14,
            "total_estimated": 60
        }
    
    async def _get_performance_summary(self, student_data: Dict) -> Dict:
        """Get Performans summary"""
        return {
            "overall_score": 75.5,
            "trend": "improving",
            "key_achievements": ["Matematik'te %15 gelişim", "100+ soru çözümü"]
        }
    
    async def _get_progress_metrics(self, student_data: Dict) -> Dict:
        """Get İlerleme Metrikler"""
        return self._calculate_progress_metrics(await self._get_historical_data(
            student_data.get("id", ""),
            "month"
        ))
    
    async def _get_subject_analysis(self, student_data: Dict) -> Dict:
        """Get subject-wise analysis"""
        perf_data = await self._collect_performance_data(student_data)
        return self._analyze_by_subject(perf_data)
    
    async def _get_time_analysis(self, student_data: Dict) -> Dict:
        """Get Zaman analysis"""
        perf_data = await self._collect_performance_data(student_data)
        return self._analyze_time_efficiency(perf_data)
    
    async def _get_success_prediction(self, student_data: Dict) -> Dict:
        """Get Başarılı prediction summary"""
        factors = {
            "current_level": 0.7,
            "improvement_rate": 0.15,
            "consistency": 0.8,
            "time_efficiency": 0.75,
            "coverage": 0.6
        }
        
        return {
            "probability": self._calculate_success_probability(factors),
            "key_factors": self._identify_critical_factors(factors)
        }
    
    def _extract_summary(self, report_content: str) -> str:
        """Extract summary den report"""
        # Would use NLP e extract
        return "Genel performansınız iyi seviyede. Son hafta %12 gelişim gösterdiniz."
    
    def _create_report_visualizations(self, data: Dict) -> List[Dict]:
        """Oluştur visualization suggestions for report"""
        return [
            {"type": "line_chart", "data": "progress_over_time", "title": "İlerleme Grafiği"},
            {"type": "radar_chart", "data": "subject_performance", "title": "Konu Dağılımı"},
            {"type": "heatmap", "data": "daily_performance", "title": "Günlük Performans"}
        ]
    
    def _extract_key_metrics(self, data: Dict) -> Dict[str, Any]:
        """Extract Anahtar Metrikler den report data"""
        return {
            "accuracy": 78.5,
            "questions_solved": 523,
            "study_hours": 28,
            "improvement": 12.3
        }
    
    def _extract_action_items(self, report_content: str) -> List[str]:
        """Extract action items den report"""
        return [
            "Matematik'te integral konusuna odaklan",
            "Günlük çözüm süresini 2 dakikaya düşür",
            "Cumartesi günleri ek 2 saat çalış"
        ]
    
    def _calculate_next_report_date(self, report_type: str) -> str:
        """Hesapla next report Tarih"""
        days_map = {
            "daily": 1,
            "weekly": 7,
            "monthly": 30
        }
        
        days = days_map.get(report_type, 7)
        next_date = datetime.now(timezone.utc) + timedelta(days=days)
        
        return next_date.isoformat()
    
    def _generate_demo_peer_data(self) -> List[Dict]:
        """Üret demo peer data for comparison"""
        return [
            {"student_id": f"peer_{i}", "score": random.randint(60, 95)}
            for i in range(100)
        ]
    
    async def _get_peer_data(self, student_data: Dict) -> List[Dict]:
        """Get real peer data"""
        # Would query Veritabanı for similar students
        return self._generate_demo_peer_data()
    
    async def _get_student_metrics(self, student_data: Dict) -> Dict:
        """Get Öğrenci's Metrikler for comparison"""
        return {
            "overall_score": 78,
            "matematik": 82,
            "fizik": 71,
            "kimya": 75,
            "biyoloji": 84
        }
    
    def _calculate_peer_average(self, peer_data: List[Dict]) -> Dict:
        """Hesapla peer Ortalama Metrikler"""
        scores = [p.get("score", 0) for p in peer_data]
        
        return {
            "average_score": statistics.mean(scores),
            "median_score": statistics.median(scores),
            "std_dev": statistics.stdev(scores) if len(scores) > 1 else 0
        }
    
    def _calculate_percentile_rank(
        self, 
        student_data: Dict, 
        peer_data: List[Dict]
    ) -> float:
        """Hesapla Öğrenci's percentile rank"""
        student_score = 78  # Would get den student_data
        peer_scores = [p.get("score", 0) for p in peer_data]
        
        below_count = sum(1 for score in peer_scores if score < student_score)
        percentile = (below_count / len(peer_scores)) * 100
        
        return round(percentile, 1)
    
    def _find_relative_strengths(
        self, 
        student_data: Dict, 
        peer_data: List[Dict]
    ) -> List[str]:
        """Find areas nerede Öğrenci excels compared e peers"""
        return ["Biyoloji'de üst %20", "Tutarlılıkta üst %15"]
    
    def _find_areas_behind(
        self, 
        student_data: Dict, 
        peer_data: List[Dict]
    ) -> List[str]:
        """Find areas nerede Öğrenci is arkasında peers"""
        return ["Fizik'te ortalama altı", "Hız konusunda gelişim gerekli"]
    
    def _create_ranking_chart(self, comparison: Dict) -> Dict:
        """Oluştur ranking visualization data"""
        return {
            "type": "percentile",
            "student_rank": comparison.get("percentile_rank", 0),
            "quartiles": [25, 50, 75],
            "labels": ["Alt %25", "Orta %50", "Üst %25"]
        }
    
    def _identify_competitive_advantages(self, comparison: Dict) -> List[str]:
        """Identify competitive advantages"""
        return comparison.get("relative_strengths", [])
    
    def _set_competitive_targets(self, comparison: Dict) -> Dict[str, Any]:
        """Set competitive improvement targets"""
        current_percentile = comparison.get("percentile_rank", 50)
        
        return {
            "current_rank": current_percentile,
            "target_rank": min(current_percentile + 15, 90),
            "timeline": "3 ay",
            "required_improvements": [
                "Fizik'te %10 artış",
                "Günlük +1 saat çalışma"
            ]
        }