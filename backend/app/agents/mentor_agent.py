"""
Mentor <PERSON> - <PERSON><PERSON><PERSON><PERSON>, duygusal destek ve çalışma rehberliği sağlar
YKS öğrencileri için sanal koç görevi gö<PERSON>ü<PERSON>
"""

from typing import Dict, List, Any
import logging

from .base_agent import BaseAgent, AgentRole, AgentResponse
from ..core.hackathon_optimizations import HackathonOptimizer, DEMO_MODE

logger = logging.getLogger(__name__)

class MentorAgent(BaseAgent):
    """
    Mentor <PERSON> responsible for:
    - Providing motivation ve encouragement
    - Managing stress ve anxiety
    - Offering study tips ve techniques
    - Creating positive mindset
    - Celebrating achievements
    - Providing emotional support
    """
    
    def __init__(self, gemini_client):
        super().__init__(
            name="Motivasyon Koçu",
            role=AgentRole.MENTOR,
            gemini_client=gemini_client,
            description="YKS yolculuğunda motivasyon ve destek sağlarım"
        )
        
        self.capabilities = [
            "provide_motivation",
            "manage_stress",
            "study_techniques",
            "celebrate_success",
            "emotional_support",
            "mindset_coaching"
        ]
        
        # Motivational quote categories
        self.quote_categories = {
            "başarı": ["success", "achievement", "accomplishment"],
            "azim": ["perseverance", "determination", "persistence"],
            "odaklanma": ["focus", "concentration", "attention"],
            "güven": ["confidence", "self-belief", "trust"],
            "gelişim": ["growth", "improvement", "progress"]
        }
        
        # Study technique categories
        self.study_techniques = {
            "pomodoro": "25 dakika çalış, 5 dakika mola",
            "feynman": "Öğrendiğini başkasına anlatıyormuş gibi açıkla",
            "active_recall": "Kitabı kapat ve hatırlamaya çalış",
            "spaced_repetition": "Konuları belirli aralıklarla tekrar et",
            "mind_mapping": "Kavramları görsel olarak bağla"
        }
        
        # Emotional states ve responses
        self.emotional_responses = {
            "stressed": self._stress_management_response,
            "unmotivated": self._motivation_boost_response,
            "anxious": self._anxiety_relief_response,
            "tired": self._energy_management_response,
            "confident": self._confidence_reinforcement_response,
            "struggling": self._struggle_support_response
        }
    
    async def process(self, state: Dict[str, Any]) -> AgentResponse:
        """İşle Mentor-related requests"""
        request_type = state.get("request_type", "")
        student_data = state.get("student_data", {})
        
        try:
            if request_type == "provide_motivation":
                return await self._provide_motivation(student_data)
            elif request_type == "manage_stress":
                return await self._manage_stress(student_data)
            elif request_type == "study_techniques":
                return await self._suggest_study_techniques(student_data)
            elif request_type == "celebrate_success":
                return await self._celebrate_achievement(student_data)
            elif request_type == "emotional_support":
                return await self._provide_emotional_support(student_data)
            elif request_type == "mindset_coaching":
                return await self._coach_mindset(student_data)
            else:
                return await self._general_mentoring(state)
                
        except Exception as e:
            logger.error(f"Mentor Agent error: {e}")
            return self.create_response(
                success=False,
                data={},
                message=f"Mentoring error: {str(e)}"
            )
    
    def can_handle(self, request_type: str, state: Dict[str, Any]) -> bool:
        """Check eğer bu Ajan can İşle the İstek"""
        mentor_keywords = [
            "motivasyon", "moral", "stres", "endişe", "kaygı",
            "yorgun", "başaramıyorum", "zor", "yardım", "destek",
            "nasıl çalışmalı", "verimli", "odaklan", "motive"
        ]
        
        # Check İstek type
        if request_type in self.capabilities:
            return True
        
        # Check message İçerik
        message = state.get("message", "").lower()
        
        # Check emotional indicators
        if self._detect_emotional_need(message):
            return True
        
        return any(keyword in message for keyword in mentor_keywords)
    
    @HackathonOptimizer.fast_response
    async def _provide_motivation(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Provide personalized motivation"""
        
        # Get Öğrenci context
        performance = student_data.get("performance", {})
        recent_activity = student_data.get("recent_activity", {})
        goals = student_data.get("goals", {})
        mood = self._assess_mood(student_data)
        
        # Üret motivational İçerik ile Gemini
        prompt = f"""
        YKS öğrencisine kişiselleştirilmiş motivasyon mesajı oluştur.
        
        Öğrenci Durumu:
        - Performans: {json.dumps(Performans, ensure_ascii=False)}
        - Son aktiviteler: {json.dumps(recent_activity, ensure_ascii=False)}
        - Hedefler: {json.dumps(goals, ensure_ascii=False)}
        - Ruh hali: {mood}
        
        Motivasyon mesajı özellikleri:
        1. Kişiye özel ve samimi
        2. Somut başarılarına atıf yap
        3. Gerçekçi ve ulaşılabilir hedefler sun
        4. Güçlü yönlerini vurgula
        5. İlham verici ama abartısız
        6. Eylem odaklı tavsiyeler içer
        
        Mesaj 3 bölümden oluşsun:
        - Takdir ve başarı vurgusu
        - Mevcut durum değerlendirmesi
        - İleriye dönük motivasyon ve hedefler
        
        Samimi, destekleyici ve enerjik bir dille yaz.
        """
        
        motivation_message = await self.call_gemini(prompt)
        
        # Add personalized elements
        motivation_data = {
            "main_message": motivation_message,
            "daily_quote": self._get_daily_quote(mood),
            "achievement_highlights": self._highlight_achievements(performance, recent_activity),
            "power_words": self._get_power_words(mood),
            "visualization_exercise": self._create_visualization(goals),
            "action_steps": self._create_action_steps(performance),
            "motivation_level": self._calculate_motivation_boost(student_data)
        }
        
        # Add memory
        self.add_to_memory(self.create_message(
            f"Provided motivation for student in {mood} mood"
        ))
        
        return self.create_response(
            success=True,
            data=motivation_data,
            message="Motivasyon desteğin hazır! 💪🎯",
            next_action="apply_motivation"
        )
    
    async def _manage_stress(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Help Yönet exam stress ve anxiety"""
        
        stress_level = student_data.get("stress_level", "medium")
        stress_triggers = student_data.get("stress_triggers", [])
        
        prompt = f"""
        YKS stresi yaşayan öğrenciye yardım et.
        
        Stres seviyesi: {stress_level}
        Stres kaynakları: {', '.join(stress_triggers) eğer stress_triggers aksi takdirde 'Genel sınav kaygısı'}
        
        Stres yönetimi planı oluştur:
        1. Anlık rahatlama teknikleri (nefes, kas gevşetme)
        2. Orta vadeli stres azaltma stratejileri
        3. Uzun vadeli stres yönetimi alışkanlıkları
        4. Sınav gününe özel teknikler
        5. Destek kaynakları ve öneriler
        
        Teknikler:
        - Bilimsel temelli olsun
        - Uygulaması kolay olsun
        - YKS'ye özel adaptasyonlar içersin
        - Hemen uygulanabilir olsun
        
        Sakinleştirici, güven verici bir dille yaz.
        """
        
        stress_management = await self.call_gemini(prompt)
        
        # Oluştur comprehensive stress management plan
        stress_data = {
            "immediate_techniques": self._get_immediate_stress_relief(),
            "management_plan": stress_management,
            "breathing_exercises": self._create_breathing_exercises(),
            "relaxation_techniques": self._get_relaxation_techniques(),
            "cognitive_strategies": self._get_cognitive_strategies(stress_triggers),
            "daily_stress_busters": self._create_daily_stress_busters(),
            "emergency_kit": self._create_stress_emergency_kit()
        }
        
        return self.create_response(
            success=True,
            data=stress_data,
            message="Stres yönetim planın hazır!",
            next_action="practice_techniques"
        )
    
    async def _suggest_study_techniques(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Suggest personalized study techniques"""
        
        learning_style = student_data.get("learning_style", "visual")
        subjects = student_data.get("subjects", [])
        challenges = student_data.get("challenges", [])
        
        prompt = f"""
        YKS öğrencisi için kişiselleştirilmiş çalışma teknikleri öner.
        
        Öğrenme stili: {learning_style}
        Çalıştığı dersler: {', '.join(subjects) eğer subjects aksi takdirde 'Tüm dersler'}
        Zorluklar: {', '.join(challenges) eğer challenges aksi takdirde 'Genel'}
        
        Öneriler:
        1. Öğrenme stiline uygun teknikler
        2. Her ders için özel stratejiler
        3. Zaman yönetimi teknikleri
        4. Verimlilik artırıcı yöntemler
        5. Motivasyon koruma teknikleri
        6. Tekrar ve pekiştirme yöntemleri
        
        Her teknik için:
        - Nasıl uygulanacağını açıkla
        - Neden etkili olduğunu belirt
        - Örnek uygulama göster
        - Sonuç takibi nasıl yapılır
        
        Pratik, uygulanabilir ve YKS'ye özel öneriler sun.
        """
        
        study_techniques = await self.call_gemini(prompt)
        
        # Oluştur structured technique guide
        technique_data = {
            "personalized_techniques": study_techniques,
            "technique_library": self._get_technique_library(learning_style),
            "subject_specific": self._get_subject_techniques(subjects),
            "productivity_hacks": self._get_productivity_hacks(),
            "memory_techniques": self._get_memory_techniques(),
            "focus_strategies": self._get_focus_strategies(),
            "weekly_plan": self._create_technique_schedule(student_data)
        }
        
        return self.create_response(
            success=True,
            data=technique_data,
            message="Kişisel çalışma tekniklerin hazır! 📚",
            next_action="try_techniques"
        )
    
    async def _celebrate_achievement(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Celebrate Öğrenci achievements"""
        
        achievement = student_data.get("achievement", {})
        achievement_type = achievement.get("type", "general")
        details = achievement.get("details", {})
        
        prompt = f"""
        Öğrencinin başarısını kutla!
        
        Başarı: {achievement_type}
        Detaylar: {json.dumps(details, ensure_ascii=False)}
        
        Kutlama mesajı:
        1. Başarıyı takdir et ve önemini vurgula
        2. Emeğini ve çabasını öv
        3. Bu başarının gelecekteki etkilerini açıkla
        4. Motivasyonu artıracak bağlantılar kur
        5. Sonraki hedefler için ilham ver
        
        Coşkulu, samimi ve gurur verici bir kutlama yap.
        Emojiler kullan ama abartma.
        """
        
        celebration_message = await self.call_gemini(prompt)
        
        # Oluştur celebration package
        celebration_data = {
            "message": celebration_message,
            "achievement_badge": self._create_achievement_badge(achievement_type),
            "success_analysis": self._analyze_success_factors(achievement),
            "next_milestone": self._suggest_next_milestone(achievement),
            "share_prompt": self._create_share_message(achievement),
            "reward_suggestions": self._suggest_rewards()
        }
        
        # Record achievement
        self.add_to_memory(self.create_message(
            f"Celebrated {achievement_type} achievement"
        ))
        
        return self.create_response(
            success=True,
            data=celebration_data,
            message="Tebrikler! Başarını kutluyorum! 🎉🏆",
            next_action="share_achievement"
        )
    
    async def _provide_emotional_support(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Provide emotional support ve understanding"""
        
        emotional_state = student_data.get("emotional_state", "neutral")
        concerns = student_data.get("concerns", [])
        
        prompt = f"""
        Duygusal destek ihtiyacı olan YKS öğrencisine yardım et.
        
        Duygusal durum: {emotional_state}
        Endişeler: {', '.join(concerns) eğer concerns aksi takdirde 'Genel'}
        
        Duygusal destek içeriği:
        1. Empati ve anlayış göster
        2. Duygularını normalize et
        3. Benzer deneyimlerden örnekler ver
        4. Baş etme stratejileri öner
        5. Umut ve güven aşıla
        6. Somut destek kaynakları sun
        
        Yaklaşım:
        - Yargılamadan dinle ve anla
        - Duyguları küçümseme
        - Gerçekçi ama umut verici ol
        - Profesyonel yardım gerekiyorsa yönlendir
        
        Sıcak, anlayışlı ve destekleyici bir dille yaz.
        """
        
        emotional_support = await self.call_gemini(prompt)
        
        # Determine emotional Yanıt Strateji
        response_strategy = self.emotional_responses.get(
            emotional_state, 
            self._general_support_response
        )
        
        support_data = {
            "support_message": emotional_support,
            "coping_strategies": response_strategy(),
            "self_care_tips": self._get_self_care_tips(),
            "support_resources": self._get_support_resources(),
            "daily_affirmations": self._create_affirmations(emotional_state),
            "check_in_schedule": self._create_check_in_plan()
        }
        
        return self.create_response(
            success=True,
            data=support_data,
            message="Yanındayım, birlikte başaracağız! 💙",
            next_action="apply_support"
        )
    
    async def _coach_mindset(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Coach for Başarılı mindset"""
        
        current_mindset = student_data.get("mindset_assessment", {})
        goals = student_data.get("goals", {})
        
        prompt = f"""
        YKS başarısı için zihinsel koçluk yap.
        
        Mevcut zihniyet: {json.dumps(current_mindset, ensure_ascii=False)}
        Hedefler: {json.dumps(goals, ensure_ascii=False)}
        
        Zihinsel koçluk programı:
        1. Mevcut düşünce kalıplarını analiz et
        2. Sınırlayıcı inançları tespit et
        3. Güçlendirici inançlar oluştur
        4. Başarı zihniyeti stratejileri
        5. Görselleştirme teknikleri
        6. Pozitif iç konuşma örnekleri
        
        Koçluk yaklaşımı:
        - Bilimsel temelli (growth mindset)
        - Uygulanabilir egzersizler
        - Ölçülebilir hedefler
        - Sürdürülebilir değişim
        
        Güçlendirici, dönüştürücü bir dille yaz.
        """
        
        mindset_coaching = await self.call_gemini(prompt)
        
        coaching_data = {
            "coaching_plan": mindset_coaching,
            "mindset_exercises": self._create_mindset_exercises(),
            "belief_transformation": self._create_belief_work(current_mindset),
            "visualization_scripts": self._create_visualization_scripts(goals),
            "success_habits": self._suggest_success_habits(),
            "progress_tracking": self._create_mindset_tracker(),
            "21_day_challenge": self._create_mindset_challenge()
        }
        
        return self.create_response(
            success=True,
            data=coaching_data,
            message="Başarı zihniyeti koçluğun hazır! 🧠✨",
            next_action="start_mindset_work"
        )
    
    async def _general_mentoring(self, state: Dict[str, Any]) -> AgentResponse:
        """Provide general mentoring support"""
        
        message = state.get("message", "")
        context = self.get_memory_context()
        
        # Detect emotional tone ve need
        emotional_need = self._analyze_emotional_need(message)
        
        prompt = f"""
        YKS mentoru olarak öğrenciye yardım et.
        
        Öğrenci mesajı: {message}
        Duygusal ihtiyaç: {emotional_need}
        
        Önceki konuşmalar:
        {context}
        
        Mentor desteği:
        1. Mesajı dikkatle dinle ve anla
        2. Duygusal ihtiyacına cevap ver
        3. Pratik ve uygulanabilir tavsiyeler sun
        4. Motivasyon ve güven aşıla
        5. Somut adımlar öner
        
        Samimi, destekleyici ve umut verici ol.
        """
        
        response = await self.call_gemini(prompt)
        
        return self.create_response(
            success=True,
            data={
                "mentoring": response,
                "emotional_support": emotional_need,
                "resources": self._get_relevant_resources(message),
                "next_steps": self._suggest_next_steps(emotional_need)
            },
            message="Sana yardımcı olmak için buradayım! 🤝"
        )
    
    # Helper methods for emotional ve motivational support
    
    def _detect_emotional_need(self, message: str) -> bool:
        """Detect eğer message indicates emotional need"""
        emotional_indicators = [
            "yapamıyorum", "başaramıyorum", "zor", "stres",
            "endişe", "korku", "yalnız", "bıktım", "yoruldum",
            "motivasyon", "moral", "destek", "yardım"
        ]
        
        message_lower = message.lower()
        return any(indicator in message_lower for indicator in emotional_indicators)
    
    def _assess_mood(self, student_data: Dict[str, Any]) -> str:
        """Assess Öğrenci's Akım mood"""
        # Would use sentiment analysis veya explicit mood data
        recent_performance = student_data.get("performance", {})
        
        avg_performance = sum(recent_performance.values()) / len(recent_performance) \
            if recent_performance else 50
        
        if avg_performance > 80:
            return "confident"
        elif avg_performance > 60:
            return "motivated"
        elif avg_performance > 40:
            return "struggling"
        else:
            return "stressed"
    
    def _get_daily_quote(self, mood: str) -> Dict[str, str]:
        """Get inspirational quote based üzerinde mood"""
        quotes = {
            "confident": {
                "quote": "Başarı, hazırlık ve fırsatın buluşmasıdır.",
                "author": "Seneca"
            },
            "motivated": {
                "quote": "Büyük işler güçle değil, sebatla başarılır.",
                "author": "Samuel Johnson"
            },
            "struggling": {
                "quote": "Düşmek başarısızlık değil, kalkmamaktır.",
                "author": "Konfüçyüs"
            },
            "stressed": {
                "quote": "Fırtına geçtiğinde, nasıl ayakta kaldığını unutursun.",
                "author": "Haruki Murakami"
            }
        }
        
        return quotes.get(mood, quotes["motivated"])
    
    def _highlight_achievements(
        self, 
        performance: Dict[str, float], 
        recent_activity: Dict[str, Any]
    ) -> List[str]:
        """Highlight recent achievements"""
        achievements = []
        
        # Performans-based achievements
        for subject, score in performance.items():
            if score > 80:
                achievements.append(f"{subject.capitalize()}'de %{int(score)} başarı!")
        
        # Activity-based achievements
        if recent_activity.get("daily_streak", 0) > 7:
            achievements.append(f"{recent_activity['daily_streak']} günlük çalışma serisi!")
        
        if recent_activity.get("questions_solved", 0) > 100:
            achievements.append(f"{recent_activity['questions_solved']} soru çözümü!")
        
        return achievements[:5]  # Top 5 achievements
    
    def _get_power_words(self, mood: str) -> List[str]:
        """Get Güç words for motivation"""
        power_words_map = {
            "confident": ["Güçlü", "Başarılı", "Kararlı", "Lider", "Kazanan"],
            "motivated": ["Azimli", "Hırslı", "Odaklı", "İstekli", "Enerjik"],
            "struggling": ["Dirençli", "Mücadeleci", "Güçlenen", "Gelişen", "Öğrenen"],
            "stressed": ["Sakin", "Kontrollü", "Dengeli", "Güvende", "Destekli"]
        }
        
        return power_words_map.get(mood, power_words_map["motivated"])
    
    def _create_visualization(self, goals: Dict[str, Any]) -> Dict[str, str]:
        """Oluştur visualization exercise"""
        return {
            "title": "Başarı Görselleştirmesi",
            "steps": [
                "Gözlerini kapat ve derin bir nefes al",
                "Sınav günü kendini sakin ve hazır hissettiğini hayal et",
                f"Hedefin olan {goals.get('target_university', 'üniversiteye')} yerleştiğini gör",
                "Ailenin ve arkadaşlarının mutluluğunu hisset",
                "Bu duyguyu içinde sakla ve çalışırken hatırla"
            ],
            "duration": "5 dakika",
            "frequency": "Günde 2 kez"
        }
    
    def _create_action_steps(self, performance: Dict[str, float]) -> List[str]:
        """Oluştur actionable motivation steps"""
        steps = []
        
        # Find weakest subject
        if performance:
            weakest = min(performance.items(), key=lambda x: x[1])
            steps.append(f"Bugün {weakest[0]}'den 10 soru çöz")
        
        steps.extend([
            "Kendine güzel bir mola ödülü ver",
            "Bugünkü bir başarını yaz",
            "Yarın için tek bir hedef belirle"
        ])
        
        return steps
    
    def _calculate_motivation_boost(self, student_data: Dict[str, Any]) -> int:
        """Hesapla motivation boost percentage"""
        # Based üzerinde various factors
        base_boost = 70
        
        if student_data.get("recent_success", False):
            base_boost += 15
        
        mood = self._assess_mood(student_data)
        mood_boosts = {
            "confident": 10,
            "motivated": 5,
            "struggling": -10,
            "stressed": -15
        }
        
        base_boost += mood_boosts.get(mood, 0)
        
        return max(0, min(100, base_boost))
    
    def _get_immediate_stress_relief(self) -> List[Dict[str, str]]:
        """Get immediate stress relief techniques"""
        return [
            {
                "name": "4-7-8 Nefes Tekniği",
                "steps": "4 saniye nefes al, 7 saniye tut, 8 saniye ver",
                "duration": "2 dakika"
            },
            {
                "name": "5-4-3-2-1 Duyusal Teknik",
                "steps": "5 şey gör, 4 şey dokun, 3 ses duy, 2 koku al, 1 tat",
                "duration": "3 dakika"
            },
            {
                "name": "Progresif Kas Gevşemesi",
                "steps": "Her kas grubunu 5 saniye ger, 15 saniye gevşet",
                "duration": "10 dakika"
            }
        ]
    
    def _create_breathing_exercises(self) -> List[Dict[str, Any]]:
        """Oluştur breathing exercises"""
        return [
            {
                "name": "Kare Nefes",
                "pattern": [4, 4, 4, 4],
                "description": "4 al, 4 tut, 4 ver, 4 bekle"
            },
            {
                "name": "Karın Nefesi",
                "pattern": "Diyafram",
                "description": "Karnını şişirerek derin nefes"
            }
        ]
    
    def _get_relaxation_techniques(self) -> List[Dict[str, str]]:
        """Get relaxation techniques"""
        return [
            {"technique": "Meditasyon", "app": "Headspace veya Calm"},
            {"technique": "Yoga", "video": "10 dakikalık YKS yogası"},
            {"technique": "Müzik Terapisi", "playlist": "Odaklanma müzikleri"}
        ]
    
    def _get_cognitive_strategies(self, triggers: List[str]) -> List[str]:
        """Get cognitive strategies for stress"""
        strategies = [
            "Olumsuz düşünceleri yeniden çerçevele",
            "'Ya olursa' yerine 'Olduğunda ne yaparım' diye düşün",
            "Başarısızlık değil, öğrenme fırsatı olarak gör"
        ]
        
        # Add trigger-specific strategies
        if "zaman" in str(triggers).lower():
            strategies.append("Zamanı kontrol edemezsin ama kullanımını planlayabilirsin")
        
        return strategies
    
    def _create_daily_stress_busters(self) -> Dict[str, List[str]]:
        """Oluştur daily stress management activities"""
        return {
            "sabah": ["10 dakika meditasyon", "Güne teşekkürle başla"],
            "öğle": ["Kısa yürüyüş", "Derin nefes molası"],
            "akşam": ["Günlük yazma", "Rahatlama müziği"]
        }
    
    def _create_stress_emergency_kit(self) -> Dict[str, Any]:
        """Oluştur stress emergency kit"""
        return {
            "items": [
                "Favori motivasyon sözlerin",
                "Rahatlatıcı müzik listesi",
                "Acil durum nefes kartı",
                "Destekleyici arkadaş numaraları"
            ],
            "apps": ["Calm", "Spotify", "Forest"],
            "quick_relief": "30 saniye göz kapat ve 'Başarabilirim' de"
        }
    
    def _analyze_emotional_need(self, message: str) -> str:
        """Analyze specific emotional need"""
        message_lower = message.lower()
        
        if any(word in message_lower for word in ["stres", "kaygı", "endişe"]):
            return "anxiety_support"
        elif any(word in message_lower for word in ["motivasyon", "moral", "bıktım"]):
            return "motivation_boost"
        elif any(word in message_lower for word in ["yapamıyorum", "başaramıyorum"]):
            return "confidence_building"
        elif any(word in message_lower for word in ["yorgun", "tüken"]):
            return "energy_management"
        else:
            return "general_support"
    
    def _get_technique_library(self, learning_style: str) -> Dict[str, List[str]]:
        """Get study techniques tarafından learning style"""
        libraries = {
            "visual": [
                "Mind mapping",
                "Renkli notlar",
                "Diyagramlar",
                "Flashcard'lar"
            ],
            "auditory": [
                "Sesli okuma",
                "Podcast dinleme",
                "Kendi kendine anlatma",
                "Müzikle çalışma"
            ],
            "kinesthetic": [
                "Yürürken tekrar",
                "El hareketleriyle öğrenme",
                "Pratik uygulamalar",
                "Rol yapma"
            ]
        }
        
        return libraries.get(learning_style, libraries["visual"])
    
    def _get_subject_techniques(self, subjects: List[str]) -> Dict[str, List[str]]:
        """Get subject-specific study techniques"""
        techniques = {}
        
        for subject in subjects:
            if "matematik" in subject.lower():
                techniques[subject] = [
                    "Problem tiplerini kategorize et",
                    "Formül kartları oluştur",
                    "Günlük 5 farklı tip soru çöz"
                ]
            elif "türkçe" in subject.lower():
                techniques[subject] = [
                    "Günlük paragraf analizi",
                    "Kelime defteri tut",
                    "Hızlı okuma pratiği"
                ]
        
        return techniques
    
    def _get_productivity_hacks(self) -> List[Dict[str, str]]:
        """Get productivity hacks"""
        return [
            {
                "hack": "2 Dakika Kuralı",
                "description": "2 dakikadan kısa işleri hemen yap"
            },
            {
                "hack": "Parkinson Yasası",
                "description": "Kendine sıkı zaman limitleri koy"
            },
            {
                "hack": "Batching",
                "description": "Benzer görevleri grupla"
            }
        ]
    
    def _get_memory_techniques(self) -> List[Dict[str, Any]]:
        """Get memory enhancement techniques"""
        return [
            {
                "technique": "Anı Sarayı",
                "description": "Bilgiyi mekânsal olarak yerleştir",
                "best_for": "Listeler ve sıralamalar"
            },
            {
                "technique": "Akronim",
                "description": "Baş harflerden kelime oluştur",
                "best_for": "Kısa listeler"
            },
            {
                "technique": "Hikaye Yöntemi",
                "description": "Bilgiyi hikayeleştir",
                "best_for": "Bağlantılı kavramlar"
            }
        ]
    
    def _get_focus_strategies(self) -> List[str]:
        """Get focus improvement strategies"""
        return [
            "Telefonu başka odaya koy",
            "Web sitesi engelleyici kullan",
            "Beyaz gürültü dinle",
            "Çalışma alanını düzenle",
            "Tek göreve odaklan"
        ]
    
    def _create_technique_schedule(self, student_data: Dict[str, Any]) -> Dict[str, Any]:
        """Oluştur weekly technique implementation schedule"""
        return {
            "Pazartesi": "Pomodoro + Mind mapping",
            "Salı": "Active recall + Flashcards",
            "Çarşamba": "Feynman tekniği",
            "Perşembe": "Spaced repetition",
            "Cuma": "Problem solving strategies",
            "Cumartesi": "Karma teknikler",
            "Pazar": "Gözden geçirme ve planlama"
        }
    
    def _create_achievement_badge(self, achievement_type: str) -> Dict[str, str]:
        """Oluştur achievement badge"""
        badges = {
            "milestone": {"icon": "🏆", "title": "Kilometre Taşı"},
            "streak": {"icon": "🔥", "title": "Ateşli Seri"},
            "improvement": {"icon": "📈", "title": "Süper Gelişim"},
            "mastery": {"icon": "⭐", "title": "Uzmanlaşma"},
            "general": {"icon": "🎯", "title": "Başarı"}
        }
        
        return badges.get(achievement_type, badges["general"])
    
    def _analyze_success_factors(self, achievement: Dict[str, Any]) -> List[str]:
        """Analyze ne led e Başarılı"""
        return [
            "Düzenli çalışma",
            "Doğru strateji",
            "Motivasyon",
            "Destek sistemi"
        ]
    
    def _suggest_next_milestone(self, achievement: Dict[str, Any]) -> Dict[str, Any]:
        """Suggest next achievement target"""
        return {
            "target": "Bir sonraki hedefiniz",
            "timeline": "2 hafta",
            "steps": ["Adım 1", "Adım 2", "Adım 3"]
        }
    
    def _create_share_message(self, achievement: Dict[str, Any]) -> str:
        """Oluştur shareable achievement message"""
        return f"YKS yolculuğumda yeni bir başarı! {achievement.get('type', 'Hedefime')} ulaştım! 🎯"
    
    def _suggest_rewards(self) -> List[str]:
        """Suggest self-reward ideas"""
        return [
            "Favori yemeğini ye",
            "1 saat oyun/dizi izle",
            "Arkadaşlarınla görüntülü konuş",
            "Güzel bir yürüyüşe çık",
            "Kendine küçük bir hediye al"
        ]
    
    def _stress_management_response(self) -> List[str]:
        """Yanıt for stress management"""
        return [
            "Nefes egzersizleri yap",
            "5 dakika ara ver",
            "Durumu yeniden değerlendir",
            "Destek al"
        ]
    
    def _motivation_boost_response(self) -> List[str]:
        """Yanıt for motivation boost"""
        return [
            "Küçük bir başarı hedefi koy",
            "Geçmiş başarılarını hatırla",
            "5 dakika hayal kur",
            "Enerjik müzik dinle"
        ]
    
    def _anxiety_relief_response(self) -> List[str]:
        """Yanıt for anxiety relief"""
        return [
            "4-7-8 nefes tekniği",
            "Şu anı odaklan",
            "Endişelerini yaz",
            "Güvenli alan görselleştirmesi"
        ]
    
    def _energy_management_response(self) -> List[str]:
        """Yanıt for Enerji management"""
        return [
            "20 dakika kestir",
            "Hafif egzersiz yap",
            "Su iç ve sağlıklı atıştır",
            "Çalışma planını gözden geçir"
        ]
    
    def _confidence_reinforcement_response(self) -> List[str]:
        """Yanıt for confidence reinforcement"""
        return [
            "Güçlü yönlerini listele",
            "Küçük zaferler kutla",
            "Pozitif iç konuşma yap",
            "Başarı görselleştirmesi"
        ]
    
    def _struggle_support_response(self) -> List[str]:
        """Yanıt for struggle support"""
        return [
            "Zorluğu parçalara ayır",
            "Yardım iste",
            "Farklı yaklaşım dene",
            "Kendine şefkatli ol"
        ]
    
    def _general_support_response(self) -> List[str]:
        """General support Yanıt"""
        return [
            "Dinleniyorum ve anlıyorum",
            "Yanındayım",
            "Birlikte çözüm bulacağız",
            "Sen güçlüsün"
        ]
    
    def _get_self_care_tips(self) -> List[str]:
        """Get self-care tips"""
        return [
            "Günde 7-8 saat uyku",
            "Düzenli beslenme",
            "Günlük 30 dakika hareket",
            "Sosyal bağlantıları koru",
            "Hobiler için zaman ayır"
        ]
    
    def _get_support_resources(self) -> Dict[str, List[str]]:
        """Get support resources"""
        return {
            "online": [
                "YKS destek forumları",
                "Çevrimiçi danışmanlık",
                "Motivasyon videoları"
            ],
            "apps": [
                "Headspace (meditasyon)",
                "Forest (odaklanma)",
                "Daylio (ruh hali takibi)"
            ],
            "offline": [
                "Okul rehberlik servisi",
                "Aile ve arkadaş desteği",
                "Spor aktiviteleri"
            ]
        }
    
    def _create_affirmations(self, emotional_state: str) -> List[str]:
        """Oluştur daily affirmations"""
        affirmations_map = {
            "stressed": [
                "Sakinim ve kontrolüm bende",
                "Her zorluğun üstesinden gelebilirim",
                "Nefesim beni sakinleştirir"
            ],
            "unmotivated": [
                "Her gün bir adım daha yakınım",
                "Başarı benim hakkım",
                "Potansiyelim sınırsız"
            ],
            "anxious": [
                "Güvendeyim ve hazırım",
                "Endişelerim geçici, gücüm kalıcı",
                "Şu ana odaklanıyorum"
            ],
            "default": [
                "Başarabilirim ve başaracağım",
                "Her gün daha güçlüyüm",
                "Hayallerime layığım"
            ]
        }
        
        return affirmations_map.get(emotional_state, affirmations_map["default"])
    
    def _create_check_in_plan(self) -> Dict[str, str]:
        """Oluştur emotional check-de plan"""
        return {
            "frequency": "Günde 3 kez",
            "times": ["Sabah", "Öğle", "Akşam"],
            "questions": [
                "Şu an nasıl hissediyorum?",
                "Neye ihtiyacım var?",
                "Bugün için minnettarım çünkü..."
            ],
            "action": "Durumuna göre destek tekniği uygula"
        }
    
    def _create_mindset_exercises(self) -> List[Dict[str, Any]]:
        """Oluştur mindset transformation exercises"""
        return [
            {
                "exercise": "Başarı Günlüğü",
                "description": "Her gün 3 başarını yaz",
                "duration": "5 dakika",
                "benefit": "Özgüven artışı"
            },
            {
                "exercise": "Güç Pozları",
                "description": "2 dakika güçlü duruş",
                "duration": "2 dakika",
                "benefit": "Hormon dengesi"
            },
            {
                "exercise": "Gelecek Mektubu",
                "description": "Başarılı geleceğinden kendine mektup",
                "duration": "15 dakika",
                "benefit": "Vizyon netliği"
            }
        ]
    
    def _create_belief_work(self, current_mindset: Dict[str, Any]) -> Dict[str, List[str]]:
        """Oluştur belief transformation work"""
        return {
            "limiting_beliefs": [
                "Yeteri kadar akıllı değilim",
                "Çok geç kaldım",
                "Başkaları daha iyi"
            ],
            "empowering_beliefs": [
                "Her gün gelişiyorum",
                "Kendi hızımda ilerliyorum",
                "Benzersiz güçlerim var"
            ],
            "transformation_steps": [
                "Eski inancı fark et",
                "Kanıt ara (gerçek mi?)",
                "Yeni inancı oluştur",
                "Her gün tekrarla"
            ]
        }
    
    def _create_visualization_scripts(self, goals: Dict[str, Any]) -> List[Dict[str, str]]:
        """Oluştur visualization scripts"""
        return [
            {
                "title": "Sınav Günü Başarısı",
                "script": "Kendini sınav günü sakin ve hazır görüyorsun...",
                "duration": "10 dakika"
            },
            {
                "title": "Hedef Üniversite",
                "script": f"{goals.get('target_university', 'Hayalindeki üniversitede')} yürüyorsun...",
                "duration": "15 dakika"
            }
        ]
    
    def _suggest_success_habits(self) -> List[Dict[str, str]]:
        """Suggest Başarılı-building habits"""
        return [
            {
                "habit": "Sabah Rutini",
                "components": ["Meditasyon", "Hedef okuma", "Egzersiz"],
                "benefit": "Güne güçlü başlangıç"
            },
            {
                "habit": "Akşam Değerlendirmesi",
                "components": ["Günü gözden geçir", "Yarını planla", "Şükret"],
                "benefit": "Sürekli gelişim"
            }
        ]
    
    def _create_mindset_tracker(self) -> Dict[str, Any]:
        """Oluştur mindset İlerleme tracker"""
        return {
            "metrics": [
                "Özgüven seviyesi (1-10)",
                "Motivasyon (1-10)",
                "Stres yönetimi (1-10)",
                "Odaklanma (1-10)"
            ],
            "frequency": "Haftalık",
            "reflection_prompts": [
                "Bu hafta en büyük mindset kazanımım?",
                "Hangi inanç değişti?",
                "Gelecek hafta neye odaklanacağım?"
            ]
        }
    
    def _create_mindset_challenge(self) -> Dict[str, Any]:
        """Oluştur 21-day mindset challenge"""
        return {
            "name": "21 Günde Başarı Zihniyeti",
            "weeks": {
                "week1": {
                    "focus": "Farkındalık",
                    "daily_tasks": ["Düşünce günlüğü", "Meditasyon", "Olumlu iç konuşma"]
                },
                "week2": {
                    "focus": "Dönüşüm",
                    "daily_tasks": ["İnanç çalışması", "Görselleştirme", "Güç pozları"]
                },
                "week3": {
                    "focus": "Pekiştirme",
                    "daily_tasks": ["Başarı kutlama", "Minnet pratiği", "Gelecek planlama"]
                }
            },
            "reward": "Kendine güvenen, motive bir YKS savaşçısı!"
        }
    
    def _get_relevant_resources(self, message: str) -> List[Dict[str, str]]:
        """Get relevant resources based üzerinde message İçerik"""
        resources = []
        message_lower = message.lower()
        
        if "stres" in message_lower:
            resources.append({
                "type": "video",
                "title": "YKS Stresi ile Başa Çıkma",
                "link": "YouTube'da ara"
            })
        
        if "motivasyon" in message_lower:
            resources.append({
                "type": "playlist",
                "title": "YKS Motivasyon Müzikleri",
                "link": "Spotify'da ara"
            })
        
        return resources
    
    def _suggest_next_steps(self, emotional_need: str) -> List[str]:
        """Suggest next steps based üzerinde emotional need"""
        steps_map = {
            "anxiety_support": [
                "5 dakika nefes egzersizi yap",
                "Endişelerini yaz",
                "Bir arkadaşınla konuş"
            ],
            "motivation_boost": [
                "Küçük bir hedef belirle",
                "10 dakika sevdiğin müziği dinle",
                "Bir başarını kutla"
            ],
            "confidence_building": [
                "Güçlü yönlerini listele",
                "Kolay bir soruyla başla",
                "Kendine güzel bir şey söyle"
            ],
            "general_support": [
                "Derin bir nefes al",
                "5 dakika ara ver",
                "Kendine nazik ol"
            ]
        }
        
        return steps_map.get(emotional_need, steps_map["general_support"])