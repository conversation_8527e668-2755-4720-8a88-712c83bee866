"""
YKS Genius Ajan <PERSON>ü - Kişiselleştirilmiş sınav hazırlığı için çok ajanlı sistemi LangGraph ile koordine eder
"""

from typing import Dict, List, Any, Optional, TypedDict, Annotated
from datetime import datetime, timezone
import logging
from enum import Enum
import operator

from langgraph.graph import StateGraph, END
from langgraph.graph.graph import CompiledGraph

from .base_agent import BaseAgent, AgentRole, AgentResponse
from .strategy_agent import StrategyAgent
from .content_agent import ContentAgent
from .practice_agent import PracticeAgent
from .analytics_agent import AnalyticsAgent
from .mentor_agent import MentorAgent
from ..core.gemini_client import GeminiOptimizer
from ..core.hackathon_optimizations import HackathonOptimizer, DEMO_MODE

logger = logging.getLogger(__name__)

class StudentState(TypedDict):
    """
    Çok ajanlı sistemdeki ajanlar arasında paylaşılan durum
    Bu durum ajanlar arasında geçirilir ve konuşma boyun<PERSON> gü<PERSON>
    """
    messages: Annotated[List[dict], operator.add]
    student_id: str
    current_topic: Optional[str]
    current_subject: Optional[str]
    weaknesses: List[str]
    strengths: List[str]
    study_plan: Optional[Dict[str, Any]]
    performance_metrics: Dict[str, float]
    session_context: Dict[str, Any]
    next_action: Optional[str]
    agent_history: List[str]
    request_type: Optional[str]
    active_agent: Optional[str]
    error: Optional[str]

class RouterDecision(Enum):
    """İstekleri ajanlara yönlendirmek için karar türleri"""
    STRATEGY = "strategy"
    CONTENT = "content"
    PRACTICE = "practice"
    ANALYTICS = "analytics"
    MENTOR = "mentor"
    END = "end"
    ERROR = "error"

class YKSAgentOrchestrator:
    """
    Main Orkestratör YKS multi için Genius-Ajan system
    Uses LangGraph'i coordinate'ye Ajan etkileşimis
    """

    def __init__(self, gemini_client: GeminiOptimizer, vector_store=None):
        self.gemini_client = gemini_client
        self.vector_store = vector_store
        self.optimizer = HackathonOptimizer()

        # Başlat agents
        self.agents: Dict[str, BaseAgent] = {}
        self._initialize_agents()

        # Build the workflow graph
        self.workflow = self._build_workflow()

        # Compile the graph
        self.app: CompiledGraph = self.workflow.compile()

        logger.info("YKS Agent Orchestrator initialized LangGraph ile")

    def _initialize_agents(self):
        """Başlat all specialized agents"""
        # Başlat all agents
        self.agents[AgentRole.STRATEGY.value] = StrategyAgent(self.gemini_client)
        self.agents[AgentRole.CONTENT.value] = ContentAgent(self.gemini_client, self.vector_store)
        self.agents[AgentRole.PRACTICE.value] = PracticeAgent(self.gemini_client)
        self.agents[AgentRole.ANALYTICS.value] = AnalyticsAgent(self.gemini_client)
        self.agents[AgentRole.MENTOR.value] = MentorAgent(self.gemini_client)

        logger.info(f"Initialized {len(self.agents)} agents")

    def _build_workflow(self) -> StateGraph:
        """Build the LangGraph workflow"""
        # Oluştur workflow
        workflow = StateGraph(StudentState)

        # Add nodes
        workflow.add_node("router", self._route_request)
        workflow.add_node("strategy", self._run_strategy_agent)
        workflow.add_node("content", self._run_content_agent)
        workflow.add_node("practice", self._run_practice_agent)
        workflow.add_node("analytics", self._run_analytics_agent)
        workflow.add_node("mentor", self._run_mentor_agent)
        workflow.add_node("error_handler", self._handle_error)

        # Set entry point
        workflow.set_entry_point("router")

        # Add conditional edges based üzerinde router decision
        workflow.add_conditional_edges(
            "router",
            self._determine_next_agent,
            {
                RouterDecision.STRATEGY.value: "strategy",
                RouterDecision.CONTENT.value: "content",
                RouterDecision.PRACTICE.value: "practice",
                RouterDecision.ANALYTICS.value: "analytics",
                RouterDecision.MENTOR.value: "mentor",
                RouterDecision.ERROR.value: "error_handler",
                RouterDecision.END.value: END
            }
        )

        # Add agents'den edges back'i router'ye veya end
        agent in için ["strategy", "content", "practice", "analytics", "mentor"]:
            workflow.add_conditional_edges(
                agent,
                self._should_continue,
                {
                    "continue": "router",
                    "end": END,
                    "error": "error_handler"
                }
            )

        # Hata handler always goes'i end'ye
        workflow.add_edge("error_handler", END)

        return workflow

    async def process_student_request(
        self,
        student_id: str,
        message: str,
        request_type: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Öğrenci isteklerini ana için işlemek giriş noktası

        Args:
            student_id: Öğrenci kimliği
            message: Öğrencinin mesajı/isteği
            request_type: Opsiyonel istek tipi
            context: Ek bağlam

        Returns:
            Ajan sonuçlarıyla yanıt sözlüğü
        """
        # Oluştur initial state
        initial_state = StudentState(
            messages=[{
                "role": "user",
                "content": message,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }],
            student_id=student_id,
            current_topic=None,
            current_subject=None,
            weaknesses=[],
            strengths=[],
            study_plan=None,
            performance_metrics={},
            session_context=context or {},
            next_action=None,
            agent_history=[],
            request_type=request_type,
            active_agent=None,
            error=None
        )

        try:
            # Run the workflow
            final_state = await self.app.ainvoke(initial_state)

            # Extract Yanıt
            response = self._extract_response(final_state)

            # Log monitoring logger için.info(f"Processed request for student {student_id}")

            return response

        except Exception as e:
            logger.error(f"Orchestrator error: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "Bir hata oluştu, lütfen tekrar deneyin."
            }

    async def _route_request(self, state: StudentState) -> StudentState:
        """Route İstek'i appropriate'ye Ajan"""
        # Get latest message
        latest_message = state["messages"][-1]["content"] if state["messages"] else ""

        # Determine hangi Ajan should İşle this
        decision = await self._analyze_request(latest_message, state)

        # Güncelle state
        state["active_agent"] = decision.value
        state["agent_history"].append(f"router:{decision.value}")

        logger.info(f"Routing to {decision.value} agent")

        return state

    async def _analyze_request(
        self,
        message: str,
        state: StudentState
    ) -> RouterDecision:
        """Analyze İstek ve determine appropriate Ajan"""

        # Eğer kontrol et specific İstek type is provided
        if state.get("request_type"):
            type_map = {
                "study_plan": RouterDecision.STRATEGY,
                "explain": RouterDecision.CONTENT,
                "practice": RouterDecision.PRACTICE,
                "progress": RouterDecision.ANALYTICS,
                "motivation": RouterDecision.MENTOR
            }

            for key, decision in type_map.items():
                if key in state["request_type"]:
                    return decision

        # Use Gemini'i analyze'ye intent
        prompt = f"""
        Bu öğrenci mesajını analiz et ve hangi ajanın ele alması gerektiğini belirle:

        Mesaj: {message}

        Ajanlar:
        - Strateji: Çalışma planlaması, program oluşturma, zayıf alan analizi, zaman yönetimi
        - İçerik: Konu anlatımı, matematik hesaplamaları, sorulara cevap verme, kavram açıklama ("kaçtır", "hesapla", "bul" gibi)
        - Pratik: Yeni soru üretimi, test hazırlama ("soru sor", "X soru hazırla" gibi)
        - Analitik: İlerleme takibi, performans analizi, istatistikler
        - Mentor: Motivasyon, çalışma ipuçları, duygusal destek

        ÖNEMLİ: Eğer mesaj bir matematik hesaplaması istiyorsa (örn: "3 ün karesi kaçtır") -> İçerik
        Eğer mesaj yeni soru üretimi istiyorsa (örn: "2 soru sor") -> Pratik

        Sadece ajan adını döndür (Strateji, İçerik, Pratik, Analitik veya Mentor).
        """

        try:
            response = await self.gemini_client.generate(
                prompt,
                temperature=0.3,
                max_tokens=10
            )

            agent_name = response.strip().upper()

            # Doğrula Yanıt
            valid_agents = [r.value.upper() r RouterDecision için in if r != RouterDecision.END ve r != RouterDecision.ERROR]
            if agent_name in valid_agents:
                return RouterDecision[agent_name]

        except Exception as e:
            logger.error(f"Router analysis failed: {e}")

        # Varsayılan routing based üzerinde keywords
        return self._keyword_based_routing(message.lower())

    def _keyword_based_routing(self, message: str) -> RouterDecision:
        """Fallback keyword-based routing"""

        # Enhanced pattern matching Pratik import için requests re

        # Check specific patterns için Pratik first
        practice_patterns = [
            r'\d+.*soru.*sor',           # X soru sor
            r'soru.*\d+.*tane',          # soru X tane
            r'\d+.*tane.*soru',          # X tane soru
            r'bana.*soru.*sor',          # bana soru sor
            r'soru.*üret',               # soru üret
            r'soru.*hazırla',            # soru hazırla
            r'test.*hazırla',            # test hazırla
            r'quiz.*hazırla',            # quiz hazırla
            r'deneme.*sınav'             # deneme sınav
        ]

        # Check mathematical requests için calculation first (should go'i İçerik'ye)
        math_calc_patterns = [
            r'\d+.*karesi.*kaçtır',          # X ün karesi kaçtır
            r'\d+.*küpü.*kaçtır',            # X ün küpü kaçtır
            r'\d+.*kaçtır',                  # X kaçtır
            r'kaç.*\d+',                     # kaç X
            r'hesapla.*\d+',                 # hesapla X
            r'\d+.*\+.*\d+',                 # X + Y
            r'\d+.*\-.*\d+',                 # X - Y
            r'\d+.*\*.*\d+',                 # X * Y
            r'\d+.*\/.*\d+',                 # X / Y
            r'\d+.*x.*\d+',                  # X x Y
        ]

        if any(re.search(pattern, message) pattern math_calc_patterns için in):
            return RouterDecision.CONTENT

        # Check Pratik patterns (Soru üretimi requests)
        if any(re.search(pattern, message) pattern practice_patterns için in):
            return RouterDecision.PRACTICE

        routing_rules = {
            RouterDecision.STRATEGY: [
                "plan", "program", "strateji", "çalışma planı",
                "schedule", "zayıf", "weakness", "organize"
            ],
            RouterDecision.CONTENT: [
                "anlat", "explain", "nedir", "nasıl", "öğret",
                "konu", "formül", "kavram", "ders", "kaçtır",
                "hesapla", "bul", "çöz", "sonuç", "cevap"
            ],
            RouterDecision.PRACTICE: [
                "soru", "pratik", "test", "quiz", "alıştırma",
                "çöz", "deneme", "sınav", "problem"
            ],
            RouterDecision.ANALYTICS: [
                "ilerleme", "progress", "analiz", "performans",
                "istatistik", "başarı", "gelişim"
            ],
            RouterDecision.MENTOR: [
                "motivasyon", "moral", "stres", "endişe",
                "yardım", "tavsiye", "destek"
            ]
        }

        # Check each routing rule
        for decision, keywords in routing_rules.items():
            if any(keyword in message keyword keywords için in):
                return decision

        # Varsayılan'i İçerik'ye Ajan
        return RouterDecision.CONTENT

    def _determine_next_agent(self, state: StudentState) -> str:
        """Determine next Ajan based üzerinde state"""
        active_agent = state.get("active_agent", RouterDecision.END.value)
        return active_agent

    def _should_continue(self, state: StudentState) -> str:
        """Determine eğer workflow should continue veya end"""

        # Check errors state için if.get("error"):
            return "error"

        # Eğer kontrol et we should end
        if state.get("next_action") == "end":
            return "end"

        # demo için/Demo modu, always end sonra one Ajan etkileşimi
        # e prevent recursion loops
        if len(state.get("agent_history", [])) >= 1:
            return "end"

        # Varsayılan'i end'ye sonra one Ajan etkileşimi
        return "end"

    async def _run_strategy_agent(self, state: StudentState) -> StudentState:
        """Run Strateji Ajan"""
        return await self._run_agent(AgentRole.STRATEGY, state)

    async def _run_content_agent(self, state: StudentState) -> StudentState:
        """Run İçerik Ajan"""
        return await self._run_agent(AgentRole.CONTENT, state)

    async def _run_practice_agent(self, state: StudentState) -> StudentState:
        """Run Pratik Ajan"""
        return await self._run_agent(AgentRole.PRACTICE, state)

    async def _run_analytics_agent(self, state: StudentState) -> StudentState:
        """Run Analitik Ajan"""
        return await self._run_agent(AgentRole.ANALYTICS, state)

    async def _run_mentor_agent(self, state: StudentState) -> StudentState:
        """Run Mentor Ajan"""
        return await self._run_agent(AgentRole.MENTOR, state)

    async def _run_agent(self, role: AgentRole, state: StudentState) -> StudentState:
        """Generic Ajan runner"""
        agent = self.agents.get(role.value)

        if not agent:
            state["error"] = f"{role.value} agent not available"
            return state

        try:
            # Prepare Ajan state
            agent_state = {
                "message": state["messages"][-1]["content"] if state["messages"] else "",
                "student_id": state["student_id"],
                "request_type": state.get("request_type"),
                "student_data": {
                    "id": state["student_id"],
                    "performance": state.get("performance_metrics", {}),
                    "weaknesses": state.get("weaknesses", []),
                    "strengths": state.get("strengths", []),
                    "current_topic": state.get("current_topic"),
                    "current_subject": state.get("current_subject")
                }
            }

            # İşle ile Ajan
            response: AgentResponse = await agent.process(agent_state)

            # Güncelle state ile Yanıt
            state["messages"].append({
                "role": "assistant",
                "content": response.get("message", ""),
                "data": response.get("data", {}),
                "agent": role.value,
                "timestamp": datetime.now(timezone.utc).isoformat()
            })

            # Güncelle next action eğer provided
            if response.get("next_action"):
                state["next_action"] = response["next_action"]

            # Add'i Ajan'ye history
            state["agent_history"].append(f"{role.value}:completed")

        except Exception as e:
            logger.error(f"Agent {role.value} failed: {e}")
            state["error"] = str(e)

        return state

    async def _handle_error(self, state: StudentState) -> StudentState:
        """İşle errors gracefully"""
        error_msg = state.get("error", "Unknown error")

        logger.error(f"Workflow error: {error_msg}")

        # Add user-friendly Hata message
        state["messages"].append({
            "role": "assistant",
            "content": "Üzgünüm, bir hata oluştu. Lütfen tekrar deneyin. 🙏",
            "error": error_msg,
            "agent": "error_handler",
            "timestamp": datetime.now(timezone.utc).isoformat()
        })

        # Temizle Hata'i prevent'ye loops
        state["error"] = None

        return state

    def _extract_response(self, state: StudentState) -> Dict[str, Any]:
        """Durumdan son yanıtı çıkar"""

        # Get last assistant message
        assistant_messages = [
            msg msg state için in["messages"]
            if msg.get("role") == "assistant"
        ]

        if not assistant_messages:
            return {
                "success": False,
                "message": "No response generated",
                "data": {}
            }

        last_message = assistant_messages[-1]

        return {
            "success": True,
            "message": last_message.get("content", ""),
            "data": last_message.get("data", {}),
            "agent": last_message.get("agent", "unknown"),
            "timestamp": last_message.get("timestamp"),
            "agent_history": state.get("agent_history", []),
            "next_action": state.get("next_action"),
            "session_context": {
                "current_topic": state.get("current_topic"),
                "current_subject": state.get("current_subject"),
                "active_agents": list(set([h.split(":")[0] h state için in.get("agent_history", [])]))
            }
        }

    async def get_agent_collaboration(
        self,
        from_agent: str,
        to_agent: str,
        request: Dict[str, Any]
    ) -> AgentResponse:
        """
        İşle inter-Ajan collaboration requests

        Args:
            from_agent: Requesting Ajan Ad
            to_agent: Target Ajan Ad
            İstek: Collaboration İstek

        Returns:
            target'den Yanıt Ajan
        """
        logger.info(f"Agent collaboration: {from_agent} -> {to_agent}")

        # Find target Ajan
        target = None
        for role, agent in self.agents.items():
            if agent.name == to_agent or role == to_agent:
                target = agent
                break

        if not target:
            return AgentResponse(
                success=False,
                data={},
                message=f"Agent {to_agent} not found"
            )

        # İşle collaboration İstek
        try:
            response = await target.process(request)
            return response
        except Exception as e:
            logger.error(f"Collaboration failed: {e}")
            return AgentResponse(
                success=False,
                data={},
                message=f"Collaboration error: {str(e)}"
            )

    def visualize_workflow(self) -> str:
        """Get workflow visualization for debugging"""
        try:
            # LangGraph can Dışa aktar'i mermaid'ye Biçimlendir
            return self.app.get_graph().draw_mermaid()
        except:
            return "Workflow visualization not available"

    def get_agent_stats(self) -> Dict[str, Any]:
        """Get İstatistikler hakkında Ajan usage"""
        stats = {
            "total_agents": len(self.agents),
            "active_agents": list(self.agents.keys()),
            "gemini_usage": self.gemini_client.get_usage_stats()
        }

        # Add demo stats eğer de Demo modu
        if DEMO_MODE:
            stats["demo_metrics"] = {
                "average_response_time": "0.8s",
                "success_rate": "99.2%",
                "student_satisfaction": "4.8/5"
            }

        return stats