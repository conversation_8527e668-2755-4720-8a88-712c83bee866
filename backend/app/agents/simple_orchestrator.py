"""
Simplified YKS Genius Agent Orchestrator for Hackathon
Focused on demo-ability and core features
"""

import asyncio
from typing import Dict, List, Any, Optional
import logging
import random

from .base_agent import BaseAgent, AgentRole
from .strategy_agent import StrategyAgent
from .content_agent import ContentAgent
from .practice_agent import PracticeAgent
from .analytics_agent import AnalyticsAgent
from .mentor_agent import MentorAgent
from ..core.hackathon_config import ROUTING_KEYWORDS, DEMO_AGENT_MESSAGES
from ..core.hackathon_optimizations import HackathonOptimizer

logger = logging.getLogger(__name__)

class SimpleOrchestrator:
    """
    Simplified orchestrator for hackathon demo
    Focuses on smooth routing and Turkish responses
    """
    
    def __init__(self, gemini_client):
        self.gemini_client = gemini_client
        self.optimizer = HackathonOptimizer()
        
        # Initialize all agents
        self.agents = {
            "strategy": StrategyAgent(gemini_client),
            "content": ContentAgent(gemini_client),
            "practice": PracticeAgent(gemini_client),
            "analytics": AnalyticsAgent(gemini_client),
            "mentor": <PERSON>tor<PERSON><PERSON>(gemini_client)
        }
        
        # Simple conversation history
        self.conversation_history = []
        
        logger.info("🚀 YKS Genius Orchestrator başlatıldı!")
    
    async def process_message(self, message: str, student_id: str = "demo") -> Dict[str, Any]:
        """
        Process user message and route to appropriate agent
        Simplified for hackathon demo
        """
        try:
            # Add to history
            self.conversation_history.append({
                "role": "user",
                "content": message,
                "student_id": student_id
            })
            
            # Route to appropriate agent
            agent_name = self._route_message(message)
            agent = self.agents.get(agent_name)
            
            if not agent:
                return self._create_error_response("Uygun uzman bulunamadı")
            
            # Create simple state for agent
            state = {
                "message": message,
                "student_id": student_id,
                "history": self.conversation_history[-5:],  # Last 5 messages
                "request_type": self._determine_request_type(message)
            }
            
            # Get response from agent
            response = await agent.process(state)
            
            # Add agent response to history
            self.conversation_history.append({
                "role": agent_name,
                "content": response.get("message", ""),
                "data": response.get("data", {})
            })
            
            # Format response for frontend
            return {
                "success": True,
                "agent": agent_name,
                "agent_name": agent.name,
                "message": response.get("message", ""),
                "data": response.get("data", {}),
                "suggestions": self._get_suggestions(agent_name)
            }
            
        except Exception as e:
            logger.error(f"Orchestrator error: {e}")
            return self._create_error_response(str(e))
    
    def _route_message(self, message: str) -> str:
        """
        Simple keyword-based routing for demo
        """
        message_lower = message.lower()
        
        # Check each agent's keywords
        for agent_name, keywords in ROUTING_KEYWORDS.items():
            for keyword in keywords:
                if keyword in message_lower:
                    logger.info(f"Mesaj '{agent_name}' ajanına yönlendiriliyor")
                    return agent_name
        
        # Default routing based on common patterns
        if any(word in message_lower for word in ["merhaba", "selam", "başla"]):
            return "mentor"
        elif any(word in message_lower for word in ["ne", "nasıl", "nedir"]):
            return "content"
        else:
            return "strategy"  # Default to strategy
    
    def _determine_request_type(self, message: str) -> str:
        """
        Determine specific request type from message
        """
        message_lower = message.lower()
        
        # Strategy types
        if "plan" in message_lower or "program" in message_lower:
            return "create_study_plan"
        elif "zayıf" in message_lower:
            return "analyze_weaknesses"
        
        # Content types
        elif "anlat" in message_lower:
            return "explain_concept"
        elif "örnek" in message_lower:
            return "provide_examples"
        
        # Practice types
        elif "soru" in message_lower:
            return "generate_question"
        elif "test" in message_lower or "deneme" in message_lower:
            return "create_quiz"
        
        # Analytics types
        elif "performans" in message_lower or "ilerleme" in message_lower:
            return "track_progress"
        
        # Mentor types
        elif "motivasyon" in message_lower or "moral" in message_lower:
            return "provide_motivation"
        elif "stres" in message_lower:
            return "manage_stress"
        
        return "general"
    
    def _get_suggestions(self, agent_name: str) -> List[str]:
        """
        Get quick action suggestions based on current agent
        """
        suggestions = {
            "strategy": [
                "📅 Haftalık plan oluştur",
                "📊 Zayıf alanları analiz et",
                "⏰ Günlük program düzenle"
            ],
            "content": [
                "📚 Limit konusunu anlat",
                "🔍 Türev örnekleri göster",
                "💡 İntegral formülleri"
            ],
            "practice": [
                "📝 Matematik sorusu sor",
                "🎯 5 soruluk test oluştur",
                "✍️ TYT deneme sınavı"
            ],
            "analytics": [
                "📈 Haftalık performansım",
                "🎯 Hangi konulara odaklanmalıyım",
                "📊 Başarı grafiğim"
            ],
            "mentor": [
                "💪 Motivasyon lazım",
                "😰 Stres yönetimi tavsiyeleri",
                "🎯 Başarı hikayeleri"
            ]
        }
        
        return suggestions.get(agent_name, [])
    
    def _create_error_response(self, error_msg: str) -> Dict[str, Any]:
        """
        Create user-friendly error response in Turkish
        """
        friendly_messages = [
            "Ups! Küçük bir sorun oldu 😅 Tekrar dener misin?",
            "Bir dakika, hemen hallediyorum! 🛠️",
            "Sanırım bir şeyler ters gitti, ama merak etme! 💪"
        ]
        
        return {
            "success": False,
            "agent": "system",
            "agent_name": "Sistem",
            "message": random.choice(friendly_messages),
            "error": error_msg,
            "suggestions": ["🔄 Tekrar dene", "💬 Farklı bir soru sor", "🏠 Ana menüye dön"]
        }
    
    async def get_welcome_message(self, student_id: str = "demo") -> Dict[str, Any]:
        """
        Get personalized welcome message
        """
        welcome_messages = [
            "Merhaba! 👋 Ben YKS Genius, senin kişisel YKS koçunum! Nasıl yardımcı olabilirim?",
            "Hoş geldin! 🎯 YKS yolculuğunda yanındayım. Ne yapmak istersin?",
            "Selam! 📚 Bugün hangi konuda çalışmak istersin?"
        ]
        
        return {
            "success": True,
            "agent": "mentor",
            "agent_name": "Motivasyon Koçu",
            "message": random.choice(welcome_messages),
            "data": {
                "quick_actions": [
                    {"label": "📅 Çalışma Planı Oluştur", "action": "Bana haftalık çalışma planı oluştur"},
                    {"label": "📚 Konu Anlatımı", "action": "Matematik limit konusunu anlat"},
                    {"label": "📝 Pratik Yap", "action": "Bana 5 matematik sorusu sor"},
                    {"label": "📊 Performans Analizi", "action": "Son performansımı göster"},
                    {"label": "💪 Motivasyon", "action": "Motivasyona ihtiyacım var"}
                ]
            },
            "suggestions": self._get_suggestions("mentor")
        }
    
    def get_demo_conversation(self) -> List[Dict[str, Any]]:
        """
        Get a demo conversation for hackathon presentation
        """
        return [
            {
                "role": "user",
                "content": "Merhaba, YKS'ye hazırlanıyorum ve matematik konusunda zorlanıyorum"
            },
            {
                "role": "mentor",
                "agent_name": "Motivasyon Koçu",
                "content": "Merhaba! 👋 Matematik konusunda sana yardımcı olabilirim. Merak etme, birlikte üstesinden geleceğiz! 💪"
            },
            {
                "role": "user",
                "content": "Bana haftalık çalışma planı oluşturur musun?"
            },
            {
                "role": "strategy",
                "agent_name": "Strateji Uzmanı",
                "content": "Tabii ki! Senin için özel bir haftalık plan hazırlıyorum. Matematik'e ekstra ağırlık vereceğim!",
                "data": {
                    "plan": {
                        "Pazartesi": ["09:00-11:00 Limit ve Süreklilik", "14:00-16:00 Problem Çözümü"],
                        "Salı": ["09:00-11:00 Türev Alma Kuralları", "14:00-16:00 Türkçe Paragraf"],
                        # ... etc
                    }
                }
            }
        ]