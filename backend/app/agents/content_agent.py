"""
Konu Anlatım Ajanı - YKS konuları için kişiselleştirilmiş eğitim içeriği sunar
YKS müfredat verisiyle RAG kullanır ve öğrenci seviyesine uyarlar
"""

import json
from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

from .base_agent import BaseAgent, AgentRole, AgentResponse
from ..core.hackathon_optimizations import HackathonOptimizer, DEMO_MODE

logger = logging.getLogger(__name__)

class ContentAgent(BaseAgent):
    """
    Content Agent responsible for:
    - Delivering subject-specific content
    - Explaining concepts at appropriate difficulty
    - Providing examples and visual explanations
    - Retrieving relevant curriculum content
    - Adapting explanations to student level
    """
    
    def __init__(self, gemini_client, vector_store=None):
        super().__init__(
            name="Konu Anlatım Uzmanı",
            role=AgentRole.CONTENT,
            gemini_client=gemini_client,
            description="YKS konularını anlaş<PERSON><PERSON>ır şekilde anlatırım"
        )
        
        self.vector_store = vector_store  # ChromaDB for RAG
        self.capabilities = [
            "explain_concept",
            "provide_examples",
            "deliver_lesson",
            "summarize_topic",
            "visual_explanation",
            "formula_derivation"
        ]
        
        # YKS subject expertise areas
        self.subjects = {
            "matematik": ["limit", "türev", "integral", "fonksiyonlar", "geometri"],
            "fizik": ["mekanik", "elektrik", "optik", "modern fizik", "dalgalar"],
            "kimya": ["organik", "anorganik", "kimyasal hesaplamalar", "asit-baz"],
            "biyoloji": ["hücre", "genetik", "sistemler", "ekoloji", "evrim"],
            "turkce": ["paragraf", "dil bilgisi", "anlam bilgisi", "edebiyat"],
            "tarih": ["osmanlı", "cumhuriyet", "islam", "türk tarihi"],
            "cografya": ["fiziki", "beşeri", "türkiye", "bölgesel"],
            "geometri": ["üçgenler", "dörtgenler", "çember", "analitik", "katı cisimler"]
        }
        
        # Content templates for different types
        self.content_templates = {
            "concept_explanation": self._get_concept_template(),
            "problem_solving": self._get_problem_template(),
            "visual_learning": self._get_visual_template(),
            "summary": self._get_summary_template()
        }
    
    async def process(self, state: Dict[str, Any]) -> AgentResponse:
        """Process content-related requests"""
        request_type = state.get("request_type", "")
        topic = state.get("topic", "")
        subject = state.get("subject", "")
        difficulty = state.get("difficulty", "medium")
        
        try:
            if request_type == "explain_concept":
                return await self._explain_concept(topic, subject, difficulty)
            elif request_type == "provide_examples":
                return await self._provide_examples(topic, subject, difficulty)
            elif request_type == "deliver_lesson":
                return await self._deliver_lesson(topic, subject, difficulty)
            elif request_type == "summarize_topic":
                return await self._summarize_topic(topic, subject)
            else:
                return await self._general_content_help(state)
                
        except Exception as e:
            logger.error(f"Content Agent error: {e}")
            return self.create_response(
                success=False,
                data={},
                message=f"Content delivery error: {str(e)}"
            )
    
    def can_handle(self, request_type: str, state: Dict[str, Any]) -> bool:
        """Check if this agent can handle the request"""
        content_keywords = [
            "explain", "anlat", "nedir", "nasıl", "örnek",
            "ders", "konu", "formül", "açıkla", "öğret"
        ]
        
        # Check request type
        if request_type in self.capabilities:
            return True
        
        # Check message content
        message = state.get("message", "").lower()
        
        # Check for subject keywords
        for subject in self.subjects.keys():
            if subject in message:
                return True
        
        return any(keyword in message for keyword in content_keywords)
    
    @HackathonOptimizer.fast_response
    async def _explain_concept(
        self, 
        topic: str, 
        subject: str, 
        difficulty: str
    ) -> AgentResponse:
        """Explain a concept with appropriate depth"""
        
        # Retrieve relevant content from vector store if available
        context = ""
        if self.vector_store:
            context = await self._retrieve_from_curriculum(topic, subject)
        
        # Get student's current understanding level
        student_level = await self._assess_student_level(topic)
        
        prompt = f"""
        YKS {subject} konusu için {topic} kavramını açıkla.
        
        Öğrenci seviyesi: {student_level}
        İstenen zorluk: {difficulty}
        
        {f"Müfredat bilgisi: {context}" if context else ""}
        
        Açıklama şunları içermeli:
        1. Temel tanım (basit dille)
        2. Neden önemli olduğu
        3. YKS'de nasıl sorulduğu
        4. Temel formüller (varsa)
        5. Görsel veya analoji önerisi
        6. Dikkat edilmesi gerekenler
        
        Açıklama seviyesi: {difficulty}
        - easy: Temel ve sade anlatım
        - medium: Detaylı açıklama ve örnekler
        - hard: İleri seviye ve istisnalar
        
        Türkçe olarak, öğrenci dostu bir dille açıkla.
        """
        
        explanation = await self.call_gemini(prompt)
        
        # Structure the response
        structured_content = self._structure_explanation(explanation, topic)
        
        # Add related topics
        structured_content["related_topics"] = self._get_related_topics(topic, subject)
        
        # Add memory
        self.add_to_memory(self.create_message(
            f"Explained {topic} in {subject} at {difficulty} level"
        ))
        
        return self.create_response(
            success=True,
            data=structured_content,
            message=f"{topic} konusu açıklandı 📖",
            next_action="show_examples"
        )
    
    async def _provide_examples(
        self, 
        topic: str, 
        subject: str, 
        difficulty: str
    ) -> AgentResponse:
        """Provide examples for a topic"""
        
        prompt = f"""
        YKS {subject} - {topic} konusu için örnekler ver.
        
        Zorluk seviyesi: {difficulty}
        
        Şunları içer:
        1. Basit örnek (herkesin anlayabileceği)
        2. Orta seviye örnek (YKS standardı)
        3. Zor örnek (ayırt edici soru)
        
        Her örnek için:
        - Soru metni
        - Adım adım çözüm
        - Alternatif çözüm yolu (varsa)
        - Yaygın hatalar
        - İpucu
        
        Türkçe olarak, net ve anlaşılır şekilde.
        """
        
        examples = await self.call_gemini(prompt)
        
        structured_examples = self._structure_examples(examples)
        
        return self.create_response(
            success=True,
            data=structured_examples,
            message=f"{topic} için örnekler hazır 🎯",
            next_action="practice_questions"
        )
    
    async def _deliver_lesson(
        self, 
        topic: str, 
        subject: str, 
        difficulty: str
    ) -> AgentResponse:
        """Deliver a complete lesson on a topic"""
        
        # Create comprehensive lesson plan
        lesson_components = await self._create_lesson_components(topic, subject, difficulty)
        
        prompt = f"""
        YKS {subject} - {topic} konusu için kapsamlı ders içeriği oluştur.
        
        Ders bileşenleri:
        {json.dumps(lesson_components, ensure_ascii=False)}
        
        Ders planı:
        1. Giriş ve motivasyon (neden öğrenmeliyiz?)
        2. Temel kavramlar ve tanımlar
        3. Detaylı açıklamalar
        4. Örnekler ve uygulamalar
        5. YKS'de nasıl çıkar?
        6. Pratik ipuçları
        7. Özet ve ana noktalar
        
        Öğrenci seviyesine uygun, interaktif ve ilgi çekici bir ders hazırla.
        Türkçe olarak.
        """
        
        lesson_content = await self.call_gemini(prompt)
        
        # Structure as interactive lesson
        structured_lesson = {
            "topic": topic,
            "subject": subject,
            "duration": "45 dakika",
            "sections": self._parse_lesson_sections(lesson_content),
            "interactive_elements": self._create_interactive_elements(topic),
            "practice_problems": lesson_components.get("practice_problems", []),
            "summary_points": lesson_components.get("key_points", [])
        }
        
        return self.create_response(
            success=True,
            data=structured_lesson,
            message=f"{topic} dersi hazır 🎓",
            next_action="start_lesson"
        )
    
    async def _summarize_topic(self, topic: str, subject: str) -> AgentResponse:
        """Create a concise summary of a topic"""
        
        prompt = f"""
        YKS {subject} - {topic} konusunun özeti:
        
        Özet şunları içermeli:
        1. Ana kavramlar (madde madde)
        2. Önemli formüller
        3. YKS'de çıkma şekilleri
        4. Ezber yerine mantık
        5. Hızlı hatırlama teknikleri
        6. En çok yapılan hatalar
        
        Kısa, net ve akılda kalıcı bir özet hazırla.
        Görsel hafıza teknikleri öner.
        
        Türkçe olarak, bullet points kullan.
        """
        
        summary = await self.call_gemini(prompt)
        
        structured_summary = {
            "topic": topic,
            "subject": subject,
            "key_points": self._extract_key_points(summary),
            "formulas": self._extract_formulas(summary),
            "memory_tips": self._create_memory_tips(topic),
            "quick_review": self._create_quick_review(summary)
        }
        
        return self.create_response(
            success=True,
            data=structured_summary,
            message=f"{topic} özeti hazır 📝",
            next_action="save_summary"
        )
    
    async def _general_content_help(self, state: Dict[str, Any]) -> AgentResponse:
        """Provide general content help"""
        
        message = state.get("message", "")
        context = self.get_memory_context()
        
        # Try to identify subject and topic
        identified = self._identify_subject_topic(message)
        
        prompt = f"""
        YKS içerik uzmanı olarak öğrenciye yardım et.
        
        Öğrenci sorusu: {message}
        {f"Konu: {identified['subject']} - {identified['topic']}" if identified else ""}
        
        Önceki konuşmalar:
        {context}
        
        Yanıt:
        1. Soruyu net anla ve cevapla
        2. YKS müfredatına uygun ol
        3. Örneklerle destekle
        4. Seviyeye uygun anlat
        5. Ek kaynak öner
        
        Türkçe, samimi ve yardımsever bir dille.
        """
        
        response = await self.call_gemini(prompt)
        
        return self.create_response(
            success=True,
            data={
                "content": response,
                "identified_topic": identified,
                "suggested_next": self._suggest_next_topics(identified)
            },
            message="İşte açıklaman 💡"
        )
    
    async def _retrieve_from_curriculum(self, topic: str, subject: str) -> str:
        """Retrieve relevant content from vector store"""
        if not self.vector_store:
            return ""
        
        try:
            # Query vector store
            results = self.vector_store.query(
                query_texts=[f"{subject} {topic} YKS"],
                n_results=3
            )
            
            # Combine results
            context = "\n".join([
                doc for doc in results['documents'][0]
            ]) if results['documents'] else ""
            
            return context
        except Exception as e:
            logger.error(f"Vector store query failed: {e}")
            return ""
    
    async def _assess_student_level(self, topic: str) -> str:
        """Assess student's understanding level"""
        # In real implementation, this would check student's history
        # For now, return medium
        return "orta"
    
    def _identify_subject_topic(self, message: str) -> Optional[Dict[str, str]]:
        """Identify subject and topic from message"""
        message_lower = message.lower()
        
        for subject, topics in self.subjects.items():
            if subject in message_lower:
                for topic in topics:
                    if topic in message_lower:
                        return {"subject": subject, "topic": topic}
                # Subject found but no specific topic
                return {"subject": subject, "topic": "genel"}
        
        return None
    
    def _get_related_topics(self, topic: str, subject: str) -> List[str]:
        """Get related topics for deeper learning"""
        # In real implementation, this would use a knowledge graph
        related_map = {
            "limit": ["süreklilik", "türev", "belirsizlik"],
            "türev": ["limit", "integral", "optimizasyon"],
            "integral": ["türev", "alan", "hacim"],
            "mekanik": ["kinematik", "dinamik", "enerji"],
            "elektrik": ["akım", "direnç", "manyetizma"]
        }
        
        return related_map.get(topic, [])
    
    def _structure_explanation(self, explanation: str, topic: str) -> Dict[str, Any]:
        """Structure explanation into organized format"""
        return {
            "topic": topic,
            "definition": self._extract_definition(explanation),
            "importance": self._extract_importance(explanation),
            "key_concepts": self._extract_concepts(explanation),
            "formulas": self._extract_formulas(explanation),
            "visual_aids": self._suggest_visuals(topic),
            "common_mistakes": self._extract_mistakes(explanation),
            "yks_tips": self._extract_tips(explanation)
        }
    
    def _structure_examples(self, examples_text: str) -> Dict[str, Any]:
        """Structure examples into organized format"""
        # This would parse the Gemini response
        # For now, return structured format
        return {
            "easy_example": {
                "question": "Basit örnek soru...",
                "solution": "Adım adım çözüm...",
                "tips": "İpucu..."
            },
            "medium_example": {
                "question": "Orta seviye soru...",
                "solution": "Detaylı çözüm...",
                "alternative": "Alternatif yol..."
            },
            "hard_example": {
                "question": "Zor soru...",
                "solution": "Kapsamlı çözüm...",
                "common_errors": "Sık yapılan hatalar..."
            }
        }
    
    def _create_lesson_components(
        self, 
        topic: str, 
        subject: str, 
        difficulty: str
    ) -> Dict[str, Any]:
        """Create components for a complete lesson"""
        return {
            "learning_objectives": [
                f"{topic} kavramını anlamak",
                f"{topic} ile ilgili problemleri çözmek",
                "YKS soru tiplerini tanımak"
            ],
            "prerequisites": self._get_prerequisites(topic, subject),
            "time_allocation": {
                "introduction": 5,
                "main_content": 25,
                "examples": 10,
                "practice": 5
            },
            "practice_problems": 3,
            "key_points": 5
        }
    
    def _get_prerequisites(self, topic: str, subject: str) -> List[str]:
        """Get prerequisite topics"""
        prereq_map = {
            "türev": ["limit", "fonksiyonlar"],
            "integral": ["türev", "alan hesabı"],
            "elektrik": ["akım", "potansiyel"],
            "genetik": ["hücre", "DNA"]
        }
        
        return prereq_map.get(topic, [])
    
    def _create_interactive_elements(self, topic: str) -> List[Dict[str, str]]:
        """Create interactive elements for engagement"""
        return [
            {"type": "quiz", "title": f"{topic} Mini Quiz"},
            {"type": "simulation", "title": f"{topic} Simülasyonu"},
            {"type": "practice", "title": "Hemen Deneyelim"}
        ]
    
    def _parse_lesson_sections(self, content: str) -> List[Dict[str, str]]:
        """Parse lesson content into sections"""
        # Would parse actual content
        return [
            {"title": "Giriş", "content": "...", "duration": 5},
            {"title": "Ana Konu", "content": "...", "duration": 25},
            {"title": "Örnekler", "content": "...", "duration": 10},
            {"title": "Özet", "content": "...", "duration": 5}
        ]
    
    def _extract_key_points(self, text: str) -> List[str]:
        """Extract key points from text"""
        # Would use NLP to extract
        return [
            "Ana kavram 1",
            "Ana kavram 2",
            "Önemli formül",
            "Kritik nokta"
        ]
    
    def _extract_formulas(self, text: str) -> List[Dict[str, str]]:
        """Extract formulas from text"""
        return [
            {"formula": "F = ma", "description": "Newton'un 2. yasası"},
            {"formula": "v = v₀ + at", "description": "Hız formülü"}
        ]
    
    def _create_memory_tips(self, topic: str) -> List[str]:
        """Create memory tips for topic"""
        return [
            "Görsel hafıza: Formülü şekille ilişkilendir",
            "Kelime oyunu: Baş harflerden cümle oluştur",
            "Pratik: Her gün 5 soru çöz"
        ]
    
    def _create_quick_review(self, summary: str) -> Dict[str, Any]:
        """Create quick review format"""
        return {
            "flash_cards": ["Kavram 1", "Kavram 2"],
            "mnemonics": "Hatırlama teknikleri",
            "one_minute_summary": "60 saniyede özet"
        }
    
    def _suggest_next_topics(self, identified: Optional[Dict]) -> List[str]:
        """Suggest next topics to study"""
        if not identified:
            return ["Hangi konuda yardım istersen yardımcı olabilirim"]
        
        return self._get_related_topics(
            identified.get("topic", ""), 
            identified.get("subject", "")
        )
    
    def _extract_definition(self, text: str) -> str:
        """Extract definition from text"""
        # Would use NLP
        return "Kavramın temel tanımı..."
    
    def _extract_importance(self, text: str) -> str:
        """Extract importance from text"""
        return "YKS'de çok çıkar çünkü..."
    
    def _extract_concepts(self, text: str) -> List[str]:
        """Extract key concepts"""
        return ["Kavram 1", "Kavram 2", "Kavram 3"]
    
    def _suggest_visuals(self, topic: str) -> List[Dict[str, str]]:
        """Suggest visual aids"""
        return [
            {"type": "diagram", "description": f"{topic} şeması"},
            {"type": "graph", "description": f"{topic} grafiği"},
            {"type": "animation", "description": f"{topic} animasyonu"}
        ]
    
    def _extract_mistakes(self, text: str) -> List[str]:
        """Extract common mistakes"""
        return [
            "Formülü yanlış hatırlama",
            "Birimleri karıştırma",
            "İşaret hatası"
        ]
    
    def _extract_tips(self, text: str) -> List[str]:
        """Extract YKS tips"""
        return [
            "Bu tip sorular genelde 2-3 dakikada çözülmeli",
            "Formülü ezberleme, mantığını anla",
            "Grafik sorularına dikkat et"
        ]
    
    def _get_concept_template(self) -> str:
        """Get concept explanation template"""
        return """
        # {topic} Nedir?
        
        ## Tanım
        {definition}
        
        ## Neden Önemli?
        {importance}
        
        ## Temel Kavramlar
        {concepts}
        
        ## YKS'de Nasıl Çıkar?
        {yks_info}
        """
    
    def _get_problem_template(self) -> str:
        """Get problem solving template"""
        return """
        ## Problem: {problem}
        
        ### Verilenler
        {given}
        
        ### İstenen
        {asked}
        
        ### Çözüm Adımları
        {steps}
        
        ### Sonuç
        {result}
        """
    
    def _get_visual_template(self) -> str:
        """Get visual learning template"""
        return """
        ## Görsel Öğrenme: {topic}
        
        ### Şema
        {diagram}
        
        ### Grafik
        {graph}
        
        ### Özet Görsel
        {summary_visual}
        """
    
    def _get_summary_template(self) -> str:
        """Get summary template"""
        return """
        ## {topic} Özet
        
        ### Ana Noktalar
        {key_points}
        
        ### Formüller
        {formulas}
        
        ### Hatırlatıcılar
        {reminders}
        """