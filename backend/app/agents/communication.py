"""
Inter-agent communication protocol for YKS Genius multi-agent system
Provides structured message passing and collaboration between agents
"""

from typing import Dict, List, Any, Optional, TypedDict, Union
from datetime import datetime, timezone
from enum import Enum
import json
import logging
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

class MessageType(Enum):
    """Types of messages agents can exchange"""
    REQUEST = "request"              # Agent requesting help from another
    RESPONSE = "response"            # Response to a request
    BROADCAST = "broadcast"          # Message to all agents
    NOTIFICATION = "notification"    # Informational message
    ERROR = "error"                 # Error message
    HANDOFF = "handoff"             # Transferring control to another agent

class CollaborationType(Enum):
    """Types of collaboration between agents"""
    SEQUENTIAL = "sequential"        # One agent after another
    PARALLEL = "parallel"           # Multiple agents simultaneously
    HIERARCHICAL = "hierarchical"   # Parent-child relationship
    CONSENSUS = "consensus"         # Multiple agents reach agreement

@dataclass
class AgentMessage:
    """Structured message format for inter-agent communication"""
    sender: str                     # Agent name sending the message
    recipient: str                  # Target agent or "broadcast"
    message_type: MessageType       # Type of message
    content: Dict[str, Any]        # Message payload
    timestamp: str                  # ISO format timestamp
    message_id: str                # Unique message identifier
    correlation_id: Optional[str] = None  # For request-response tracking
    priority: int = 5              # 1-10, higher is more urgent
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        data['message_type'] = self.message_type.value
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AgentMessage":
        """Create from dictionary"""
        data['message_type'] = MessageType(data['message_type'])
        return cls(**data)

class CommunicationProtocol:
    """
    Manages inter-agent communication with message queuing,
    routing, and collaboration patterns
    """
    
    def __init__(self):
        self.message_queue: List[AgentMessage] = []
        self.message_history: List[AgentMessage] = []
        self.active_collaborations: Dict[str, Dict[str, Any]] = {}
        self.agent_capabilities: Dict[str, List[str]] = {}
        
    def register_agent(self, agent_name: str, capabilities: List[str]):
        """Register an agent and its capabilities"""
        self.agent_capabilities[agent_name] = capabilities
        logger.info(f"Registered agent {agent_name} with capabilities: {capabilities}")
        
    def send_message(
        self,
        sender: str,
        recipient: str,
        message_type: MessageType,
        content: Dict[str, Any],
        priority: int = 5,
        correlation_id: Optional[str] = None
    ) -> AgentMessage:
        """Send a message from one agent to another"""
        import uuid
        
        message = AgentMessage(
            sender=sender,
            recipient=recipient,
            message_type=message_type,
            content=content,
            timestamp=datetime.now(timezone.utc).isoformat(),
            message_id=str(uuid.uuid4()),
            correlation_id=correlation_id,
            priority=priority
        )
        
        # Add to queue sorted by priority
        self.message_queue.append(message)
        self.message_queue.sort(key=lambda m: m.priority, reverse=True)
        
        # Keep history
        self.message_history.append(message)
        
        logger.debug(f"Message sent from {sender} to {recipient}: {message_type.value}")
        
        return message
    
    def broadcast_message(
        self,
        sender: str,
        message_type: MessageType,
        content: Dict[str, Any],
        priority: int = 5
    ) -> AgentMessage:
        """Broadcast a message to all agents"""
        return self.send_message(
            sender=sender,
            recipient="broadcast",
            message_type=message_type,
            content=content,
            priority=priority
        )
    
    def get_messages_for_agent(self, agent_name: str) -> List[AgentMessage]:
        """Get all pending messages for a specific agent"""
        messages = []
        remaining = []
        
        for msg in self.message_queue:
            if msg.recipient == agent_name or msg.recipient == "broadcast":
                messages.append(msg)
            else:
                remaining.append(msg)
        
        self.message_queue = remaining
        return messages
    
    def initiate_collaboration(
        self,
        initiator: str,
        participants: List[str],
        collaboration_type: CollaborationType,
        context: Dict[str, Any]
    ) -> str:
        """Initiate a collaboration between multiple agents"""
        import uuid
        
        collaboration_id = str(uuid.uuid4())
        
        self.active_collaborations[collaboration_id] = {
            "initiator": initiator,
            "participants": participants,
            "type": collaboration_type,
            "context": context,
            "status": "active",
            "started_at": datetime.now(timezone.utc).isoformat(),
            "results": {}
        }
        
        # Notify participants
        for participant in participants:
            self.send_message(
                sender=initiator,
                recipient=participant,
                message_type=MessageType.REQUEST,
                content={
                    "collaboration_id": collaboration_id,
                    "type": collaboration_type.value,
                    "context": context
                },
                priority=8
            )
        
        logger.info(f"Initiated {collaboration_type.value} collaboration {collaboration_id}")
        
        return collaboration_id
    
    def update_collaboration(
        self,
        collaboration_id: str,
        agent_name: str,
        result: Dict[str, Any]
    ):
        """Update collaboration with agent's result"""
        if collaboration_id in self.active_collaborations:
            self.active_collaborations[collaboration_id]["results"][agent_name] = result
            
            # Check if all participants have responded
            collab = self.active_collaborations[collaboration_id]
            if len(collab["results"]) == len(collab["participants"]):
                collab["status"] = "completed"
                collab["completed_at"] = datetime.now(timezone.utc).isoformat()
                logger.info(f"Collaboration {collaboration_id} completed")
    
    def get_collaboration_results(self, collaboration_id: str) -> Optional[Dict[str, Any]]:
        """Get results from a collaboration"""
        if collaboration_id in self.active_collaborations:
            return self.active_collaborations[collaboration_id]["results"]
        return None
    
    def find_capable_agents(self, capability: str) -> List[str]:
        """Find agents with a specific capability"""
        capable_agents = []
        for agent, capabilities in self.agent_capabilities.items():
            if capability in capabilities:
                capable_agents.append(agent)
        return capable_agents
    
    def create_handoff(
        self,
        from_agent: str,
        to_agent: str,
        context: Dict[str, Any],
        reason: str
    ) -> AgentMessage:
        """Create a handoff message to transfer control to another agent"""
        return self.send_message(
            sender=from_agent,
            recipient=to_agent,
            message_type=MessageType.HANDOFF,
            content={
                "reason": reason,
                "context": context,
                "previous_agent": from_agent
            },
            priority=9  # High priority for handoffs
        )
    
    def get_message_history(
        self,
        agent_name: Optional[str] = None,
        message_type: Optional[MessageType] = None,
        limit: int = 100
    ) -> List[AgentMessage]:
        """Get message history with optional filters"""
        history = self.message_history
        
        if agent_name:
            history = [m for m in history 
                      if m.sender == agent_name or m.recipient == agent_name]
        
        if message_type:
            history = [m for m in history if m.message_type == message_type]
        
        return history[-limit:]

# Singleton instance for the communication protocol
communication_protocol = CommunicationProtocol()