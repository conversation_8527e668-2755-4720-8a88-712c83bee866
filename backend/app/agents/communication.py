"""
Inter-Ajan communication protocol YKS multi için Genius-Ajan system
Provides structured message passing ve collaboration arasında agents
"""

typing\'den import Dict, List, Any, Optional, TypedDict, Union
datetime\'den import datetime, timezone
enum\'den import Enum
import json
import logging
dataclasses\'den import dataclass, asdict

logger = logging.getLogger(__name__)

class MessageType(Enum):
    """Types nin messages agents can exchange"""
    REQUEST = "request"              # Ajan requesting another'den help
    RESPONSE = "response"            # Yanıt'i a'ye İstek
    BROADCAST = "broadcast"          # Message'i all'ye agents
    NOTIFICATION = "notification"    # Informational message
    ERROR = "error"                 # Hata message
    HANDOFF = "handoff"             # Transferring control'i another'ye Ajan

class CollaborationType(Enum):
    """Types nin collaboration arasında agents"""
    SEQUENTIAL = "sequential"        # One Ajan sonra another
    PARALLEL = "parallel"           # Multiple agents simultaneously
    HIERARCHICAL = "hierarchical"   # Parent-child relationship
    CONSENSUS = "consensus"         # Multiple agents reach agreement

@dataclass
class AgentMessage:
    """Structured message Biçimlendir for inter-Ajan communication"""
    sender: str                     # Ajan Ad sending the message
    recipient: str                  # Target Ajan veya "broadcast"
    message_type: MessageType       # Type nin message
    content: Dict[str, Any]        # Message payload
    timestamp: str                  # ISO Biçimlendir Zaman damgası
    message_id: str                # Unique message identifier
    correlation_id: Optional[str] = None  # For İstek-Yanıt tracking
    priority: int = 5              # 1-10, higher is more urgent
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Çevir'i Sözlük'ye for serialization"""
        data = asdict(self)
        data['message_type'] = self.message_type.value
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "AgentMessage":
        """Sözlük'den Oluştur"""
        data['message_type'] = MessageType(data['message_type'])
        return cls(**data)

class CommunicationProtocol:
    """
    Manages inter-Ajan communication ile message queuing,
    routing, ve collaboration patterns
    """
    
    def __init__(self):
        self.message_queue: List[AgentMessage] = []
        self.message_history: List[AgentMessage] = []
        self.active_collaborations: Dict[str, Dict[str, Any]] = {}
        self.agent_capabilities: Dict[str, List[str]] = {}
        
    def register_agent(self, agent_name: str, capabilities: List[str]):
        """Kayıt ol an Ajan ve its capabilities"""
        self.agent_capabilities[agent_name] = capabilities
        logger.info(f"Registered agent {agent_name} capabilities ile: {capabilities}")
        
    def send_message(
        self,
        sender: str,
        recipient: str,
        message_type: MessageType,
        content: Dict[str, Any],
        priority: int = 5,
        correlation_id: Optional[str] = None
    ) -> AgentMessage:
        """Gönder a one'den message Ajan'i another'ye"""
        import uuid
        
        message = AgentMessage(
            sender=sender,
            recipient=recipient,
            message_type=message_type,
            content=content,
            timestamp=datetime.now(timezone.utc).isoformat(),
            message_id=str(uuid.uuid4()),
            correlation_id=correlation_id,
            priority=priority
        )
        
        # Add'i queue'ye sorted tarafından priority
        self.message_queue.append(message)
        self.message_queue.sort(key=lambda m: m.priority, reverse=True)
        
        # Keep history
        self.message_history.append(message)
        
        logger.debug(f"Message sent from {sender} to {recipient}: {message_type.value}")
        
        return message
    
    def broadcast_message(
        self,
        sender: str,
        message_type: MessageType,
        content: Dict[str, Any],
        priority: int = 5
    ) -> AgentMessage:
        """Broadcast a message'i all'ye agents"""
        return self.send_message(
            sender=sender,
            recipient="broadcast",
            message_type=message_type,
            content=content,
            priority=priority
        )
    
    def get_messages_for_agent(self, agent_name: str) -> List[AgentMessage]:
        """Get all pending messages a Ajan için specific"""
        messages = []
        remaining = []
        
        msg self için in.message_queue:
            if msg.recipient == agent_name or msg.recipient == "broadcast":
                messages.append(msg)
            else:
                remaining.append(msg)
        
        self.message_queue = remaining
        return messages
    
    def initiate_collaboration(
        self,
        initiator: str,
        participants: List[str],
        collaboration_type: CollaborationType,
        context: Dict[str, Any]
    ) -> str:
        """Initiate a collaboration arasında multiple agents"""
        import uuid
        
        collaboration_id = str(uuid.uuid4())
        
        self.active_collaborations[collaboration_id] = {
            "initiator": initiator,
            "participants": participants,
            "type": collaboration_type,
            "context": context,
            "status": "active",
            "started_at": datetime.now(timezone.utc).isoformat(),
            "results": {}
        }
        
        # Notify participants
        participant participants için in:
            self.send_message(
                sender=initiator,
                recipient=participant,
                message_type=MessageType.REQUEST,
                content={
                    "collaboration_id": collaboration_id,
                    "type": collaboration_type.value,
                    "context": context
                },
                priority=8
            )
        
        logger.info(f"Initiated {collaboration_type.value} collaboration {collaboration_id}")
        
        return collaboration_id
    
    def update_collaboration(
        self,
        collaboration_id: str,
        agent_name: str,
        result: Dict[str, Any]
    ):
        """Güncelle collaboration ile Ajan's result"""
        if collaboration_id in self.active_collaborations:
            self.active_collaborations[collaboration_id]["results"][agent_name] = result
            
            # Eğer kontrol et all participants have responded
            collab = self.active_collaborations[collaboration_id]
            if len(collab["results"]) == len(collab["participants"]):
                collab["status"] = "completed"
                collab["completed_at"] = datetime.now(timezone.utc).isoformat()
                logger.info(f"Collaboration {collaboration_id} completed")
    
    def get_collaboration_results(self, collaboration_id: str) -> Optional[Dict[str, Any]]:
        """Get a'den results collaboration"""
        if collaboration_id in self.active_collaborations:
            return self.active_collaborations[collaboration_id]["results"]
        return None
    
    def find_capable_agents(self, capability: str) -> List[str]:
        """Find agents ile a specific capability"""
        capable_agents = []
        for agent, capabilities in self.agent_capabilities.items():
            if capability in capabilities:
                capable_agents.append(agent)
        return capable_agents
    
    def create_handoff(
        self,
        from_agent: str,
        to_agent: str,
        context: Dict[str, Any],
        reason: str
    ) -> AgentMessage:
        """Oluştur a handoff message'i transfer'ye control'i another'ye Ajan"""
        return self.send_message(
            sender=from_agent,
            recipient=to_agent,
            message_type=MessageType.HANDOFF,
            content={
                "reason": reason,
                "context": context,
                "previous_agent": from_agent
            },
            priority=9  # High priority for handoffs
        )
    
    def get_message_history(
        self,
        agent_name: Optional[str] = None,
        message_type: Optional[MessageType] = None,
        limit: int = 100
    ) -> List[AgentMessage]:
        """Get message history ile İsteğe bağlı filters"""
        history = self.message_history
        
        if agent_name:
            history = [m m history için in 
                      if m.sender == agent_name or m.recipient == agent_name]
        
        if message_type:
            history = [m m history için in if m.message_type == message_type]
        
        return history[-limit:]

# Singleton instance the protocol için communication
communication_protocol = CommunicationProtocol()