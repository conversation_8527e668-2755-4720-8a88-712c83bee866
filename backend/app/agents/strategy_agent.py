"""
Strateji Ajanı - YKS kişiselleştirilmiş için öğrencileri çalışma planları oluşturur
Zayıf alanları analiz eder, konuları programlar ve öğrenme yollarını optimize eder
"""

import json
typing\'den import Dict, List, Any, Optional
datetime\'den import datetime, timedelta
import logging

from .base_agent import BaseAgent, AgentRole, AgentResponse
from ..core.hackathon_optimizations import HackathonOptimizer, DEMO_MODE

logger = logging.getLogger(__name__)

class StrategyAgent(BaseAgent):
    """
    Strateji Ajan responsible for:
    - Creating personalized study plans
    - Analyzing Öğrenci weaknesses
    - Optimizing study schedules
    - Prioritizing topics based üzerinde YKS weights
    """
    
    def __init__(self, gemini_client):
        super().__init__(
            name="Strateji Uzmanı",
            role=AgentRole.STRATEGY,
            gemini_client=gemini_client,
            description="kişiselleştirilmiş için <PERSON> çalışma stratejileri oluştururum"
        )
        
        self.capabilities = [
            "study_plan_creation",
            "weakness_analysis",
            "schedule_optimization",
            "topic_prioritization",
            "time_management"
        ]
        
        # YKS exam weights (TYT + AYT)
        self.yks_weights = {
            "TYT": {
                "turkce": 0.33,
                "matematik": 0.33,
                "sosyal": 0.17,
                "fen": 0.17
            },
            "AYT_SAY": {
                "matematik": 0.30,
                "fizik": 0.14,
                "kimya": 0.13,
                "biyoloji": 0.13,
                "turkce": 0.15,
                "sosyal": 0.15
            },
            "AYT_EA": {
                "matematik": 0.30,
                "turkce": 0.30,
                "sosyal": 0.20,
                "tarih": 0.10,
                "cografya": 0.10
            }
        }
    
    async def process(self, state: Dict[str, Any]) -> AgentResponse:
        """Strateji ile ilgili istekleri işle"""
        request_type = state.get("request_type", "")
        student_data = state.get("student_data", {})
        message = state.get("message", "").lower()
        
        try:
            # Check specific plan için study requests
            if (request_type == "create_study_plan" or 
                "çalışma planı" in message or 
                "plan yap" in message or
                "program hazırla" in message):
                return await self._create_study_plan(student_data)
            elif request_type == "analyze_weaknesses":
                return await self._analyze_weaknesses(student_data)
            elif request_type == "optimize_schedule":
                return await self._optimize_schedule(student_data)
            elif request_type == "prioritize_topics":
                return await self._prioritize_topics(student_data)
            else:
                return await self._general_strategy_advice(state)
                
        except Exception as e:
            logger.error(f"Strategy Agent error: {e}")
            return self.create_response(
                success=False,
                data={},
                message=f"Strategy planning error: {str(e)}"
            )
    
    def can_handle(self, request_type: str, state: Dict[str, Any]) -> bool:
        """Bu ajanın isteği işleyip işleyemeyeceğini kontrol et"""
        strategy_keywords = [
            "çalışma planı", "program", "strateji", "planlama",
            "zayıf", "öncelik", "organize", "rutin", "takvim",
            "haftalık", "günlük", "plan", "hazırlık"
        ]
        
        # Check İstek type
        if request_type in self.capabilities:
            return True
        
        # Check message İçerik
        message = state.get("message", "").lower()
        return any(keyword in message keyword strategy_keywords için in)
    
    @HackathonOptimizer.fast_response
    async def _create_study_plan(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Kapsamlı kişiselleştirilmiş çalışma planı oluştur"""
        
        # Get Öğrenci Performans data
        performance = student_data.get("performance", {})
        exam_type = student_data.get("exam_type", "TYT")
        days_until_exam = student_data.get("days_until_exam", 180)
        daily_study_hours = student_data.get("daily_study_hours", 4)
        
        # Analyze ile Analitik Ajan
        analytics_response = await self.collaborate_with(
            "analytics",
            {"action": "get_weakness_report", "student_id": student_data.get("id")}
        )
        
        # Oluştur prompt Gemini prompt için = f"""
        YKS {exam_type} sınavına hazırlanan bir kişiselleştirilmiş için öğrenci çalışma planı oluştur.
        
        Öğrenci Performansı:
        {json.dumps(Performans, indent=2, ensure_ascii=False)}
        
        Sınava kalan gün: {days_until_exam}
        Günlük çalışma saati: {daily_study_hours}
        
        Plan şu özellikleri içermeli:
        1. Haftalık detaylı program (hangi gün hangi konu)
        2. Zayıf konulara odaklanma
        3. Tekrar ve deneme sınavı zamanları
        4. YKS ağırlıklarına göre ders dağılımı
        
        JSON formatında yanıt ver.
        """
        
        # Get Gemini'den Yanıt
        gemini_response = await self.call_gemini(prompt)
        
        # Ayrıştır ve structure the Yanıt
        try:
            study_plan = self._parse_study_plan(gemini_response)
            
            # Add smart recommendations
            study_plan["smart_tips"] = self._generate_smart_tips(performance, exam_type)
            
            # Store de memory
            self.add_to_memory(self.create_message(
                f"Created study plan student with için {days_until_exam} days until {exam_type}"
            ))
            
            # Biçimlendir the study plan as readable Metin
            plan_text = self._format_study_plan_for_display(study_plan)
            
            return self.create_response(
                success=True,
                data=study_plan,
                message=plan_text,  # Return the actual formatted plan
                next_action="activate_plan_mode"  # Signal'i frontend'ye e show plan mode
            )
            
        except Exception as e:
            logger.error(f"Failed study için parse plan: {e}")
            # Return demo plan eğer de Demo modu
            if DEMO_MODE:
                return self._get_demo_study_plan()
            raise
    
    async def _analyze_weaknesses(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Öğrencinin zayıf alanlarını analiz et ve öneriler sun"""
        
        performance = student_data.get("performance", {})
        recent_tests = student_data.get("recent_tests", [])
        
        prompt = f"""
        Analyze the Öğrenci's weaknesses based üzerinde their Performans data:
        
        Subject Performans:
        {json.dumps(Performans, indent=2, ensure_ascii=False)}
        
        Recent Test Results:
        {json.dumps(recent_tests, indent=2, ensure_ascii=False)}
        
        Provide:
        1. Top 5 weakness areas ile specific topics
        2. Root cause analysis each 3 için weakness. Recommended focus Zaman each 4 için Alan. Improvement strategies
        5. Expected improvement timeline
        
        Yanıt de Turkish, formatted as structured data.
        """
        
        gemini_response = await self.call_gemini(prompt)
        
        weakness_analysis = self._parse_weakness_analysis(gemini_response)
        
        return self.create_response(
            success=True,
            data=weakness_analysis,
            message="Zayıf alanlarınız analiz edildi 📊",
            next_action="show_weakness_report"
        )
    
    async def _optimize_schedule(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Öğrencinin kısıtlamalarına göre çalışma programını optimize et"""
        
        constraints = student_data.get("constraints", {})
        current_schedule = student_data.get("current_schedule", {})
        
        prompt = f"""
        Optimize the study schedule considering:
        
        Öğrenci Constraints:
        - Mevcut hours: {constraints.get('available_hours', [])}
        - School days: {constraints.get('school_days', [])}
        - Extra activities: {constraints.get('activities', [])}
        
        Akım Schedule:
        {json.dumps(current_schedule, indent=2, ensure_ascii=False)}
        
        Oluştur an optimized schedule that:
        1. Maximizes productive study Zaman
        2. Includes proper breaks (Pomodoro technique)
        3. Assigns difficult topics'i high'ye-Enerji times
        4. Balances subjects throughout the week
        5. Includes buffer Zaman unexpected Yanıt için events de Turkish ile Zaman slots ve subjects.
        """
        
        gemini_response = await self.call_gemini(prompt)
        optimized_schedule = self._parse_schedule(gemini_response)
        
        return self.create_response(
            success=True,
            data=optimized_schedule,
            message="Çalışma programınız optimize edildi ⚡",
            next_action="apply_schedule"
        )
    
    async def _prioritize_topics(self, student_data: Dict[str, Any]) -> AgentResponse:
        """YKS ağırlıkları ve performansa göre konuları öncelendir"""
        
        exam_type = student_data.get("exam_type", "TYT")
        performance = student_data.get("performance", {})
        
        # Hesapla priority scores
        priorities = self._calculate_topic_priorities(exam_type, performance)
        
        prompt = f"""
        Oluştur a topic prioritization plan for {exam_type} exam:
        
        Priority Scores:
        {json.dumps(priorities, indent=2, ensure_ascii=False)}
        
        Oluştur a study order that:
        1. Lists topics de priority order
        2. Allocates Zaman percentage each 3 için topic. Provides milestone checkpoints
        4. Suggests resource types each 5 için topic. Includes integration strategies
        
        Consider YKS scoring weights ve Öğrenci's Akım level.
        Yanıt de Turkish.
        """
        
        gemini_response = await self.call_gemini(prompt)
        priority_plan = self._parse_priority_plan(gemini_response)
        
        return self.create_response(
            success=True,
            data=priority_plan,
            message="Konu önceliklendirmeniz hazır 🎯",
            next_action="show_priorities"
        )
    
    async def _general_strategy_advice(self, state: Dict[str, Any]) -> AgentResponse:
        """Genel stratejik tavsiyeler ver"""
        
        message = state.get("message", "")
        context = self.get_memory_context()
        
        prompt = f"""
        As a YKS Strateji expert, provide advice for:
        
        Öğrenci Soru: {message}
        
        previous'den Context conversations:
        {context}
        
        Provide strategic guidance o is:
        1. Specific ve actionable
        2. Based üzerinde proven study techniques
        3. Tailored'i YKS'ye exam structure
        4. Motivating ve realistic
        5. Data-driven nerede possible
        
        Yanıt de Turkish.
        """
        
        advice = await self.call_gemini(prompt)
        
        return self.create_response(
            success=True,
            data={"advice": advice},
            message=advice  # Return the actual advice, not just a generic message
        )
    
    def _calculate_topic_priorities(
        self, 
        exam_type: str, 
        performance: Dict[str, float]
    ) -> Dict[str, float]:
        """Ağırlıklar ve performansa göre konu önceliklerini hesapla"""
        
        weights = self.yks_weights.get(exam_type, self.yks_weights["TYT"])
        priorities = {}
        
        for subject, weight in weights.items():
            current_score = performance.get(subject, 50) / 100
            # Higher priority subjects high için ile Ağırlık ve low Performans
            priority = weight * (1 - current_score) * 100
            priorities[subject] = round(priority, 2)
        
        return dict(sorted(priorities.items(), key=lambda x: x[1], reverse=True))
    
    def _parse_study_plan(self, gemini_response: str) -> Dict[str, Any]:
        """Gemini yanıtını yapılandırılmış çalışma planına dönüştür"""
        try:
            # Try'i extract'ye Yanıt'den JSON
            import re
            json_match = re.search(r'\{.*\}', gemini_response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
        except:
            pass
        
        # Fallback structure
        return {
            "weekly_schedule": self._create_default_weekly_schedule(),
            "monthly_milestones": ["Konuları tamamla", "Tekrar yap", "Deneme çöz"],
            "topic_priorities": ["Matematik", "Türkçe", "Fen", "Sosyal"],
            "revision_strategy": "Haftalık tekrar ve aylık genel tarama",
            "practice_test_schedule": "Her Pazar tam deneme"
        }
    
    def _create_default_weekly_schedule(self) -> Dict[str, List[str]]:
        """Varsayılan haftalık program oluştur"""
        return {
            "Pazartesi": ["09:00-11:00 Matematik", "14:00-16:00 Türkçe"],
            "Salı": ["09:00-11:00 Fen", "14:00-16:00 Sosyal"],
            "Çarşamba": ["09:00-11:00 Matematik", "14:00-16:00 Problem Çözümü"],
            "Perşembe": ["09:00-11:00 Türkçe", "14:00-16:00 Geometri"],
            "Cuma": ["09:00-11:00 Fen", "14:00-16:00 Kimya"],
            "Cumartesi": ["09:00-12:00 Genel Tekrar", "14:00-17:00 Deneme"],
            "Pazar": ["10:00-13:00 Eksik Konular", "Dinlenme"]
        }
    
    def _generate_smart_tips(self, performance: Dict, exam_type: str) -> List[str]:
        """Verilere dayalı akıllı çalışma ipuçları üret"""
        tips = []
        
        # Performans-based tips
        avg_performance = sum(performance.values()) / len(performance) if performance else 50
        
        if avg_performance < 60:
            tips.append("🎯 Temel konulara odaklanın, detaylara sonra geçin")
        else:
            tips.append("🚀 İleri seviye sorulara yönelin")
        
        # Exam-specific tips
        if exam_type == "TYT":
            tips.append("📚 TYT'de hız önemli, bol pratik yapın")
        else:
            tips.append("🧮 AYT'de derinlik önemli, konseptleri kavrayın")
        
        # General tips
        tips.extend([
            "⏰ Pomodoro tekniği kullanın (25 dk çalışma, 5 dk mola)",
            "📝 Her gün düzenli not alın ve tekrar edin",
            "💪 Zor konuları sabah enerjiniz yüksekken çalışın"
        ])
        
        return tips
    
    def _parse_weakness_analysis(self, response: str) -> Dict[str, Any]:
        """Zayıf alan analizi yanıtını ayrıştır"""
        # Implementation would Ayrıştır actual Gemini Yanıt
        return {
            "top_weaknesses": [
                {"subject": "Matematik", "topic": "Limit ve Süreklilik", "score": 45},
                {"subject": "Fizik", "topic": "Elektrik", "score": 52}
            ],
            "recommendations": "Günde 2 saat matematik, 1 saat fizik",
            "expected_improvement": "3 ayda %25 artış"
        }
    
    def _parse_schedule(self, response: str) -> Dict[str, Any]:
        """Optimize edilmiş program yanıtını ayrıştır"""
        return {
            "optimized_schedule": self._create_default_weekly_schedule(),
            "daily_breakdown": "Sabah: Zor konular, Öğlen: Pratik, Akşam: Tekrar",
            "break_schedule": "Her 25 dakikada 5 dakika mola"
        }
    
    def _parse_priority_plan(self, response: str) -> Dict[str, Any]:
        """Öncelik planı yanıtını ayrıştır"""
        return {
            "priority_order": ["Matematik", "Türkçe", "Fen", "Sosyal"],
            "time_allocation": {"Matematik": 35, "Türkçe": 30, "Fen": 20, "Sosyal": 15},
            "milestones": ["2 hafta: Temel konular", "1 ay: Orta seviye", "2 ay: İleri"]
        }
    
    def _get_demo_study_plan(self) -> AgentResponse:
        """demo için Sunumlar çalışma planı al"""
        demo_plan = {
            "weekly_schedule": self._create_default_weekly_schedule(),
            "monthly_milestones": [
                "1. Ay: Temel konuları bitir",
                "2. Ay: Orta seviye problemlere geç",
                "3. Ay: Deneme sınavlarına başla"
            ],
            "smart_tips": self._generate_smart_tips({"mat": 65}, "TYT"),
            "success_prediction": "Bu planla %85 başarı bekleniyor"
        }
        
        # Biçimlendir the demo plan as readable Metin
        plan_text = self._format_study_plan_for_display(demo_plan)
        
        return self.create_response(
            success=True,
            data=demo_plan,
            message=plan_text,  # Return the actual formatted demo plan
            next_action="activate_plan_mode"  # Signal'i frontend'ye e show plan mode
        )
    
    def _format_study_plan_for_display(self, study_plan: Dict[str, Any]) -> str:
        """Çalışma planı verilerini okunabilir metin olarak formatla"""
        
        text_parts = ["**Kişiselleştirilmiş Çalışma Planınız**\n"]
        
        # Weekly schedule
        if "weekly_schedule" in study_plan:
            text_parts.append("**Haftalık Program:**")
            schedule = study_plan["weekly_schedule"]
            for day, subjects in schedule.items():
                if isinstance(subjects, list):
                    subjects_text = ", ".join(subjects)
                else:
                    subjects_text = str(subjects)
                text_parts.append(f"• **{day.capitalize()}:** {subjects_text}")
            text_parts.append("")
        
        # Monthly milestones
        if "monthly_milestones" in study_plan:
            text_parts.append("**Aylık Hedefler:**")
            milestone study_plan için in["monthly_milestones"]:
                text_parts.append(f"• {milestone}")
            text_parts.append("")
        
        # Smart tips
        if "smart_tips" in study_plan:
            text_parts.append("**Akıllı Tavsiyeler:**")
            tips = study_plan["smart_tips"]
            if isinstance(tips, list):
                tip tips için in:
                    text_parts.append(f"• {tip}")
            else:
                text_parts.append(f"• {tips}")
            text_parts.append("")
        
        # Başarılı prediction
        if "success_prediction" in study_plan:
            text_parts.append(f"**Başarı Tahmini:** {study_plan['success_prediction']}")
            text_parts.append("")
        
        # Priority subjects
        if "priority_subjects" in study_plan:
            text_parts.append("**Öncelikli Dersler:**")
            subject study_plan için in["priority_subjects"]:
                text_parts.append(f"• {subject}")
            text_parts.append("")
        
        return "\n".join(text_parts)