"""
YKS Genius çok ajanlı sistem için Temel Ajan sınıfı
Tüm uzman ajanlar için ortak işlevsellik sağlar
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, TypedDict
from datetime import datetime, timezone
import logging
from enum import Enum

from ..core.gemini_client import GeminiOptimizer
from ..core.hackathon_optimizations import HackathonOptimizer, DEMO_MODE
from .communication import communication_protocol, MessageType, AgentMessage

logger = logging.getLogger(__name__)

class AgentRole(Enum):
    """Defines the role of each agent in the system"""
    STRATEGY = "strategy"
    CONTENT = "content"
    PRACTICE = "practice"
    ANALYTICS = "analytics"
    MENTOR = "mentor"
    ROUTER = "router"

class AgentMessage(TypedDict):
    """Standard message format for inter-agent communication"""
    role: str
    content: str
    timestamp: str
    metadata: Optional[Dict[str, Any]]
    agent_name: str

class AgentResponse(TypedDict):
    """Standard response format from agents"""
    success: bool
    data: Any
    message: str
    next_action: Optional[str]
    metadata: Optional[Dict[str, Any]]

class BaseAgent(ABC):
    """
    Abstract base class for all YKS Genius agents
    Provides common functionality and interface
    """
    
    def __init__(
        self, 
        name: str, 
        role: AgentRole,
        gemini_client: Optional[GeminiOptimizer] = None,
        description: str = ""
    ):
        self.name = name
        self.role = role
        self.description = description
        self.gemini_client = gemini_client
        self.capabilities: List[str] = []
        self.memory: List[AgentMessage] = []
        self.optimizer = HackathonOptimizer()
        
        # Register with communication protocol
        communication_protocol.register_agent(self.name, self.capabilities)
        
        logger.info(f"Initialized {self.name} agent with role {self.role.value}")
    
    @abstractmethod
    async def process(self, state: Dict[str, Any]) -> AgentResponse:
        """
        Main processing method that each agent must implement
        
        Args:
            state: Current conversation/session state
            
        Returns:
            AgentResponse with results
        """
        pass
    
    @abstractmethod
    def can_handle(self, request_type: str, state: Dict[str, Any]) -> bool:
        """
        Determines if this agent can handle the given request
        
        Args:
            request_type: Type of request
            state: Current state
            
        Returns:
            bool: True if agent can handle request
        """
        pass
    
    async def call_gemini(self, prompt: str, context: Optional[str] = None) -> str:
        """
        Call Gemini API with hackathon optimizations
        
        Args:
            prompt: The prompt to send to Gemini
            context: Additional context
            
        Returns:
            str: Response from Gemini
        """
        if not self.gemini_client:
            raise ValueError(f"{self.name} agent has no Gemini client configured")
        
        # Add Turkish language system prompt
        turkish_system_prompt = f"""
        Sen {self.name}, YKS sınavına hazırlanan öğrencilere yardımcı olan uzman bir AI asistanısın.
        Rolün: {self.role.value}
        
        KURALLAR:
        1. Her zaman Türkçe yanıt ver
        2. Öğrencilere "sen" diye hitap et
        3. Samimi ve motive edici ol
        4. YKS'ye özel tavsiyeler ver
        5. Kısa, net ve anlaşılır ol
        6. Profesyonel dil kullan
        """
        
        full_prompt = f"{turkish_system_prompt}\n\n{prompt}"
        if context:
            full_prompt = f"{full_prompt}\n\nKontext: {context}"
        
        # Use hackathon optimizations
        @self.optimizer.cache_aggressive(ttl=3600)
        @self.optimizer.fast_response
        async def _gemini_call():
            return await self.gemini_client.generate(full_prompt, context=None)
        
        try:
            response = await _gemini_call()
            return response
        except Exception as e:
            logger.error(f"Gemini call failed for {self.name}: {e}")
            if DEMO_MODE:
                # Return demo fallback
                return self._get_demo_fallback(prompt)
            raise
    
    def _get_demo_fallback(self, prompt: str) -> str:
        """Get fallback response for demo mode"""
        fallbacks = {
            "study_plan": "Senin için kişiselleştirilmiş YKS çalışma planını hazırladım! Zayıf olduğun konulara odaklanacağız...",
            "question": "Seviyene uygun bir soru hazırlıyorum...",
            "explanation": "Bu konuyu sana detaylıca açıklayayım...",
            "progress": "İlerleme analizin harika görünüyor! Önemli alanlarda gelişme var...",
            "motivation": "Harika gidiyorsun! Bu tempoda devam et, başaracaksın!",
            "çalışma": "Çalışma planın için önerilerim hazır!",
            "plan": "Senin için özel bir plan hazırladım!"
        }
        
        prompt_lower = prompt.lower()
        for key, response in fallbacks.items():
            if key in prompt_lower:
                return response
        
        return "Merhaba! Ben YKS Genius, senin kişisel YKS koçun. Sana nasıl yardımcı olabilirim?"
    
    def add_to_memory(self, message: AgentMessage):
        """Add message to agent's memory"""
        self.memory.append(message)
        
        # Limit memory size for performance
        if len(self.memory) > 100:
            self.memory = self.memory[-50:]
    
    def get_memory_context(self, limit: int = 10) -> str:
        """Get recent memory as context string"""
        recent_memory = self.memory[-limit:]
        context_parts = []
        
        for msg in recent_memory:
            context_parts.append(
                f"{msg['agent_name']} ({msg['timestamp']}): {msg['content']}"
            )
        
        return "\n".join(context_parts)
    
    def create_message(self, content: str, metadata: Optional[Dict] = None) -> AgentMessage:
        """Create a standardized agent message"""
        return AgentMessage(
            role=self.role.value,
            content=content,
            timestamp=datetime.now(timezone.utc).isoformat(),
            metadata=metadata or {},
            agent_name=self.name
        )
    
    def create_response(
        self, 
        success: bool, 
        data: Any, 
        message: str = "",
        next_action: Optional[str] = None,
        metadata: Optional[Dict] = None
    ) -> AgentResponse:
        """Create a standardized agent response"""
        return AgentResponse(
            success=success,
            data=data,
            message=message,
            next_action=next_action,
            metadata=metadata or {}
        )
    
    # Communication Protocol Methods
    async def send_message_to_agent(
        self,
        recipient: str,
        message_type: MessageType,
        content: Dict[str, Any],
        priority: int = 5
    ) -> AgentMessage:
        """Send a message to another agent"""
        return communication_protocol.send_message(
            sender=self.name,
            recipient=recipient,
            message_type=message_type,
            content=content,
            priority=priority
        )
    
    async def broadcast_message(
        self,
        message_type: MessageType,
        content: Dict[str, Any],
        priority: int = 5
    ) -> AgentMessage:
        """Broadcast a message to all agents"""
        return communication_protocol.broadcast_message(
            sender=self.name,
            message_type=message_type,
            content=content,
            priority=priority
        )
    
    async def get_pending_messages(self) -> List[AgentMessage]:
        """Get all pending messages for this agent"""
        return communication_protocol.get_messages_for_agent(self.name)
    
    async def request_collaboration(
        self,
        partners: List[str],
        context: Dict[str, Any]
    ) -> str:
        """Request collaboration with other agents"""
        from .communication import CollaborationType
        
        return communication_protocol.initiate_collaboration(
            initiator=self.name,
            participants=partners,
            collaboration_type=CollaborationType.SEQUENTIAL,
            context=context
        )
    
    async def handoff_to_agent(
        self,
        target_agent: str,
        context: Dict[str, Any],
        reason: str
    ) -> AgentMessage:
        """Hand off control to another agent"""
        return communication_protocol.create_handoff(
            from_agent=self.name,
            to_agent=target_agent,
            context=context,
            reason=reason
        )
    
    async def collaborate_with(
        self, 
        agent_name: str, 
        request: Dict[str, Any]
    ) -> AgentResponse:
        """
        Request collaboration from another agent
        This will be implemented by the orchestrator
        """
        logger.info(f"{self.name} requesting collaboration from {agent_name}")
        # The orchestrator will handle routing this request
        return self.create_response(
            success=True,
            data={"collaboration_requested": agent_name, "request": request},
            message=f"Collaboration request sent to {agent_name}"
        )
    
    def __repr__(self):
        return f"<{self.__class__.__name__}(name={self.name}, role={self.role.value})>"