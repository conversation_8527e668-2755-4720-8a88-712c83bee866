"""
Pratik Ajanı - YKS adaptif için hazırlığı pratik soruları üretir
Öğrenci performansı ve YKS kalıplarına dayalı sorular oluşturur
"""

import json
typing\'den import Dict, List, Any, Optional, Tuple
datetime\'den import datetime, timezone
import logging
import random

from .base_agent import BaseAgent, AgentRole, AgentResponse
from ..core.hackathon_optimizations import HackathonOptimizer, DEMO_MODE

logger = logging.getLogger(__name__)

class PracticeAgent(BaseAgent):
    """
    Prat<PERSON> responsible for:
    - Generating adaptive Pratik questions
    - Creating mock exams
    - Providing instant feedback
    - Tracking Soru Performans
    - Adjusting difficulty based üzerinde Öğrenci level
    """
    
    def __init__(self, gemini_client):
        super().__init__(
            name="<PERSON><PERSON><PERSON>",
            role=AgentRole.PRACTICE,
            gemini_client=gemini_client,
            description="Y<PERSON>'ye uygun pratik sorular hazırlarım"
        )
        
        self.capabilities = [
            "generate_question",
            "create_quiz",
            "create_mock_exam",
            "provide_feedback",
            "analyze_mistakes",
            "suggest_similar_questions"
        ]
        
        # Soru difficulty levels
        self.difficulty_levels = {
            "kolay": {"min_score": 0, "max_score": 40, "time_limit": 3},
            "orta": {"min_score": 40, "max_score": 70, "time_limit": 2},
            "zor": {"min_score": 70, "max_score": 90, "time_limit": 1.5},
            "çok_zor": {"min_score": 90, "max_score": 100, "time_limit": 1}
        }
        
        # YKS Soru types tarafından subject
        self.question_types = {
            "matematik": ["problem", "denklem", "geometri", "fonksiyon", "limit", "türev"],
            "fizik": ["mekanik", "elektrik", "optik", "dalga", "modern_fizik"],
            "kimya": ["hesaplama", "organik", "asit_baz", "elektrokimya", "termodinamik"],
            "biyoloji": ["hücre", "genetik", "sistemler", "ekoloji", "evrim"],
            "türkçe": ["paragraf", "dil_bilgisi", "anlam", "anlatım_bozukluğu"],
            "tarih": ["kronoloji", "sebep_sonuç", "yorum", "harita"],
            "coğrafya": ["harita", "grafik", "tablo", "yorum"]
        }
        
        # Performans tracking
        self.student_performance = {}
    
    async def process(self, state: Dict[str, Any]) -> AgentResponse:
        """İşle Pratik-related requests"""
        request_type = state.get("request_type", "")
        student_data = state.get("student_data", {})
        
        try:
            if request_type == "generate_question":
                return await self._generate_adaptive_question(student_data)
            elif request_type == "create_quiz":
                return await self._create_quiz(student_data)
            elif request_type == "create_mock_exam":
                return await self._create_mock_exam(student_data)
            elif request_type == "provide_feedback":
                return await self._provide_feedback(student_data)
            elif request_type == "analyze_mistakes":
                return await self._analyze_mistakes(student_data)
            else:
                return await self._general_practice_help(state)
                
        except Exception as e:
            logger.error(f"Practice Agent error: {e}")
            return self.create_response(
                success=False,
                data={},
                message=f"Practice generation error: {str(e)}"
            )
    
    def can_handle(self, request_type: str, state: Dict[str, Any]) -> bool:
        """Eğer kontrol et bu Ajan can İşle the İstek"""
        practice_keywords = [
            "soru", "pratik", "alıştırma", "test", "quiz",
            "deneme", "çöz", "problem", "örnek soru"
        ]
        
        # Check İstek type
        if request_type in self.capabilities:
            return True
        
        # Check message İçerik
        message = state.get("message", "").lower()
        return any(keyword in message keyword practice_keywords için in)
    
    @HackathonOptimizer.fast_response
    async def _generate_adaptive_question(
        self, 
        student_data: Dict[str, Any]
    ) -> AgentResponse:
        """Üret a single adaptive Soru based üzerinde Öğrenci level"""
        
        # Extract Öğrenci Bilgi
        subject = student_data.get("subject", "matematik")
        topic = student_data.get("topic", "")
        performance = student_data.get("performance", {})
        recent_scores = student_data.get("recent_scores", [])
        
        # Hesapla optimal difficulty
        difficulty = self._calculate_optimal_difficulty(performance, recent_scores)
        
        # Get Soru type
        question_type = self._select_question_type(subject, topic, performance)
        
        # Üret Soru ile Gemini
        prompt = f"""
        YKS {subject} için adaptif soru oluştur.
        
        Konu: {topic eğer topic aksi takdirde "Genel"}
        Soru Tipi: {question_type}
        Zorluk: {difficulty}
        
        Öğrenci Performansı:
        - Son 5 soru başarı oranı: {self._calculate_recent_success_rate(recent_scores)}%
        - Konu hakimiyeti: {Performans.get(subject, 50)}%
        
        Soru özellikleri:
        1. YKS formatına uygun (5 seçenekli)
        2. Net ve anlaşılır
        3. {difficulty} zorluk seviyesinde
        4. Çözüm süresi: {self.difficulty_levels[difficulty]['time_limit']} dakika
        5. Yanıltıcı seçenekler içermeli
        
        JSON formatında döndür:
        {{
            "Soru": "Soru metni",
            "options": {{
                "A": "Seçenek A",
                "B": "Seçenek B", 
                "C": "Seçenek C",
                "D": "Seçenek D",
                "E": "Seçenek E"
            }},
            "correct_answer": "Doğru cevap harfi",
            "explanation": "Detaylı açıklama",
            "solution_steps": ["Adım 1", "Adım 2", ...],
            "common_mistakes": ["Hata 1", "Hata 2"],
            "tips": "İpucu",
            "difficulty": "{difficulty}",
            "estimated_time": {self.difficulty_levels[difficulty]['time_limit']},
            "topic": "{topic}",
            "subtopic": "Alt konu",
            "yks_frequency": "Bu tip sorunun YKS'de çıkma sıklığı (düşük/orta/yüksek)"
        }}
        """
        
        try:
            response = await self.call_gemini(prompt)
            question_data = self._parse_question_response(response)
            
            # Track Soru üretimi
            self._track_question_generation(student_data["id"], question_data)
            
            # Add metadata
            question_data["metadata"] = {
                "generated_at": datetime.now(timezone.utc).isoformat(),
                "student_level": performance.get(subject, 50),
                "adaptive_score": self._calculate_adaptive_score(difficulty, performance)
            }
            
            return self.create_response(
                success=True,
                data=question_data,
                message=f"{difficulty.capitalize()} seviye {subject} sorusu hazır!",
                next_action="solve_question"
            )
            
        except Exception as e:
            logger.error(f"Question generation failed: {e}")
            if DEMO_MODE:
                return self._get_demo_question(subject, difficulty)
            raise
    
    async def _create_quiz(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Oluştur a mini quiz ile multiple questions"""
        
        subject = student_data.get("subject", "matematik")
        topics = student_data.get("topics", [])
        num_questions = student_data.get("num_questions", 5)
        
        prompt = f"""
        YKS {subject} için {num_questions} soruluk mini quiz oluştur.
        
        Konular: {', '.join(topics) eğer topics aksi takdirde 'Karma'}
        
        Quiz özellikleri:
        1. Farklı zorluk seviyelerinde sorular
        2. Çeşitli konulardan
        3. Toplam süre: {num_questions * 2} dakika
        4. Her soru 5 seçenekli
        
        Her yukarıdaki için soru JSON formatını kullan.
        Tüm soruları bir Dizi içinde döndür.
        """
        
        response = await self.call_gemini(prompt)
        quiz_data = self._parse_quiz_response(response)
        
        # Add quiz metadata
        quiz_data["metadata"] = {
            "total_time": num_questions * 2,
            "passing_score": 60,
            "created_at": datetime.now(timezone.utc).isoformat()
        }
        
        return self.create_response(
            success=True,
            data=quiz_data,
            message=f"{num_questions} soruluk quiz hazır! ⏱️",
            next_action="start_quiz"
        )
    
    async def _create_mock_exam(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Oluştur a full YKS mock exam"""
        
        exam_type = student_data.get("exam_type", "TYT")
        
        # Define exam structure
        exam_structure = self._get_exam_structure(exam_type)
        
        prompt = f"""
        {exam_type} deneme sınavı oluştur.
        
        Sınav yapısı:
        {json.dumps(exam_structure, ensure_ascii=False)}
        
        Özellikler:
        1. Gerçek YKS formatında
        2. Zorluk dağılımı: %30 kolay, %50 orta, %20 zor
        3. Konu dağılımı YKS'ye uygun
        4. Toplam süre: {exam_structure['total_time']} dakika
        
        Her ayrı için bölüm soru grupları oluştur.
        """
        
        response = await self.call_gemini(prompt)
        exam_data = self._parse_exam_response(response, exam_structure)
        
        return self.create_response(
            success=True,
            data=exam_data,
            message=f"{exam_type} deneme sınavı hazır!",
            next_action="start_exam"
        )
    
    async def _provide_feedback(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Provide detailed feedback üzerinde Öğrenci's Cevap"""
        
        question = student_data.get("question", {})
        student_answer = student_data.get("answer", "")
        correct_answer = question.get("correct_answer", "")
        time_spent = student_data.get("time_spent", 0)
        
        is_correct = student_answer == correct_answer
        
        prompt = f"""
        Öğrencinin cevabına detaylı geri bildirim ver.
        
        Soru: {Soru.get('Soru', '')}
        Öğrenci cevabı: {student_answer}
        Doğru cevap: {correct_answer}
        Harcanan süre: {time_spent} saniye
        Tahmini süre: {Soru.get('estimated_time', 120)} saniye
        
        Geri bildirim içeriği:
        1. Cevap {"doğru" eğer is_correct aksi takdirde "yanlış"}
        2. {"Tebrikler mesajı" eğer is_correct aksi takdirde "Neden yanlış açıklaması"}
        3. Doğru çözüm yolu
        4. Alternatif çözümler
        5. Zaman yönetimi tavsiyesi
        6. Benzer soru önerisi
        
        Öğrenciyi motive edici bir dille yaz.
        """
        
        feedback = await self.call_gemini(prompt)
        
        feedback_data = {
            "is_correct": is_correct,
            "feedback": feedback,
            "score": 100 if is_correct else 0,
            "time_performance": self._evaluate_time_performance(time_spent, question),
            "next_question_difficulty": self._adjust_difficulty(is_correct, question.get("difficulty", "orta"))
        }
        
        # Güncelle Öğrenci Performans
        self._update_performance(student_data["id"], question, is_correct, time_spent)
        
        return self.create_response(
            success=True,
            data=feedback_data,
            message="Geri bildirim hazır! 📊",
            next_action="next_question" if not is_correct else "continue_practice"
        )
    
    async def _analyze_mistakes(self, student_data: Dict[str, Any]) -> AgentResponse:
        """Analyze Öğrenci's common mistakes"""
        
        mistakes = student_data.get("mistakes", [])
        
        prompt = f"""
        Öğrencinin yaptığı hataları analiz et.
        
        Hatalar:
        {json.dumps(mistakes, ensure_ascii=False)}
        
        Analiz et:
        1. En sık yapılan hata tipleri
        2. Kök nedenler
        3. Eksik konular
        4. Önerilen çalışma planı
        5. Hataları düzeltme stratejileri
        
        Yapıcı ve motive edici bir dille yaz.
        """
        
        analysis = await self.call_gemini(prompt)
        
        analysis_data = {
            "common_patterns": self._identify_mistake_patterns(mistakes),
            "weak_topics": self._identify_weak_topics(mistakes),
            "improvement_plan": self._create_improvement_plan(mistakes),
            "detailed_analysis": analysis
        }
        
        return self.create_response(
            success=True,
            data=analysis_data,
            message="Hata analizi tamamlandı! 🔍",
            next_action="show_improvement_plan"
        )
    
    async def _general_practice_help(self, state: Dict[str, Any]) -> AgentResponse:
        """Provide general Pratik assistance"""
        
        message = state.get("message", "").lower()
        student_data = state.get("student_data", {})
        
        # Eğer kontrol et bu is a Soru üretimi İstek
        if self._is_question_request(message):
            return await self._handle_question_request(message, student_data)
        
        # Otherwise provide general Pratik advice
        context = self.get_memory_context()
        
        prompt = f"""
        YKS pratik uzmanı olarak öğrenciye yardım et.
        
        Öğrenci mesajı: {state.get("message", "")}
        
        Önceki pratikler:
        {context}
        
        Yardım konuları:
        1. Soru çözme teknikleri
        2. Zaman yönetimi
        3. Pratik önerileri
        4. Motivasyon
        
        Pratik ve uygulanabilir tavsiyeler ver.
        """
        
        response = await self.call_gemini(prompt)
        
        return self.create_response(
            success=True,
            data={"advice": response},
            message=response  # Return the actual Gemini Yanıt, not just a generic message
        )
    
    def _is_question_request(self, message: str) -> bool:
        """Eğer kontrol et message is requesting questions"""
        question_patterns = [
            r'\d+.*soru.*sor',           # X soru sor
            r'soru.*\d+.*tane',          # soru X tane
            r'\d+.*tane.*soru',          # X tane soru
            r'bana.*soru.*sor',          # bana soru sor
            r'soru.*üret',               # soru üret
            r'soru.*hazırla',            # soru hazırla
            r'test.*hazırla',            # test hazırla
            r'quiz.*hazırla',            # quiz hazırla
            r'bana.*problem.*yaz',       # bana problem yaz
            r'bana.*problem.*sor',       # bana problem sor
            r'problem.*yaz',             # problem yaz
            r'problem.*sor',             # problem sor
            r'bana.*soru.*yaz',          # bana soru yaz
            r'\d+.*problem.*yaz',        # X problem yaz
            r'\d+.*problem.*sor',        # X problem sor
            r'matematik.*soru',          # matematik soru
            r'fizik.*soru',              # fizik soru
            r'kimya.*soru',              # kimya soru
            r'biyoloji.*soru',           # biyoloji soru
            r'soru.*matematik',          # soru matematik
            r'soru.*fizik',              # soru fizik
            r'soru.*kimya',              # soru kimya
            r'soru.*biyoloji',           # soru biyoloji
            r'pratik.*yap',              # pratik yap
            r'alıştırma.*yap',           # alıştırma yap
            r'egzersiz.*yap',            # egzersiz yap
            r'çöz.*soru',                # çöz soru
            r'soru.*çöz',                # soru çöz
            # Follow-yukarı Soru patterns
            r'.*daha.*sor',              # ... daha sor
            r'bir.*daha',                # bir daha
            r'tane.*daha.*sor',          # tane daha sor
            r'başka.*soru',              # başka soru
            r'yeni.*soru',               # yeni soru
            r'farklı.*soru',             # farklı soru
            r'more.*question',           # more Soru (English)
            r'another.*question',        # another Soru (English)
            r'one.*more'                 # one more
        ]
        
        import re
        return any(re.search(pattern, message) pattern question_patterns için in)
    
    def _is_single_question_request(self, message: str) -> bool:
        """Eğer kontrol et message is requesting a single Soru"""
        single_question_patterns = [
            r'bir.*soru',               # bir soru
            r'tek.*soru',               # tek soru
            r'klasik.*soru',            # klasik soru
            r'basit.*soru',             # basit soru
            r'(?<!\d\s)soru.*sor',      # soru sor (without preceding Sayı)
            r'(?<!\d\s)problem.*sor',   # problem sor (without preceding Sayı)
            r'örnek.*soru',             # örnek soru
            r'sample.*question',        # sample Soru
            r'bana.*soru(?!.*\d)',      # bana soru (without Sayı)
            r'bana.*problem(?!.*\d)',   # bana problem (without Sayı)
            # Follow-yukarı single Soru patterns
            r'.*daha.*sor',             # ... daha sor
            r'bir.*daha',               # bir daha
            r'tane.*daha.*sor',         # tane daha sor (bi tane daha sor)
            r'başka.*soru',             # başka soru
            r'yeni.*soru',              # yeni soru
            r'farklı.*soru',            # farklı soru
        ]
        
        import re
        return any(re.search(pattern, message) pattern single_question_patterns için in)
    
    async def _handle_question_request(self, message: str, student_data: Dict[str, Any]) -> AgentResponse:
        """İşle specific Soru üretimi requests"""
        
        # Extract subject ve Sayı nin questions
        subject, num_questions = self._extract_question_details(message)
        
        # Determine eğer bu is a single Soru İstek
        if self._is_single_question_request(message) or num_questions == 1:
            return await self._generate_single_question(subject, student_data, message)
        else:
            # Üret multiple questions (quiz mode)
            return await self._generate_multiple_questions(subject, num_questions, student_data)
    
    def _extract_question_details(self, message: str) -> Tuple[str, int]:
        """Extract subject ve message'den Sayı"""
        import re
        
        # Extract Sayı
        num_match = re.search(r'\d+', message)
        num_questions = int(num_match.group()) if num_match else 1  # Varsayılan'i 1'ye, not 5
        
        # Extract subject
        subjects = {
            'matematik': ['matematik', 'mat'],
            'fizik': ['fizik', 'fiz'],
            'kimya': ['kimya', 'kim'],
            'biyoloji': ['biyoloji', 'bio'],
            'türkçe': ['türkçe', 'türk'],
            'tarih': ['tarih'],
            'coğrafya': ['coğrafya', 'coğ']
        }
        
        subject = 'matematik'  # subj için Varsayılan, keywords in subjects.items():
            if any(keyword in message keyword keywords için in):
                subject = subj
                break
        
        return subject, num_questions
    
    async def _generate_multiple_questions(
        self, 
        subject: str, 
        num_questions: int, 
        student_data: Dict[str, Any]
    ) -> AgentResponse:
        """Üret multiple questions a subject için specific"""
        
        prompt = f"""
        YKS {subject} dersi için {num_questions} adet soru oluştur.
        
        Özellikler:
        1. Her soru YKS formatında (5 seçenekli)
        2. Farklı zorluk seviyelerinde
        3. Farklı konulardan
        4. Net ve anlaşılır
        5. Doğru cevap ve açıklama ile Her şu için soru JSON formatını kullan:
        {{
            "Soru": "Soru metni",
            "options": {{
                "A": "Seçenek A",
                "B": "Seçenek B", 
                "C": "Seçenek C",
                "D": "Seçenek D",
                "E": "Seçenek E"
            }},
            "correct_answer": "Doğru cevap harfi",
            "explanation": "Detaylı açıklama",
            "topic": "Konu adı",
            "difficulty": "kolay/orta/zor"
        }}
        
        Tüm soruları bir JSON Dizi içinde döndür:
        [soru1, soru2, soru3, ...]
        """
        
        try:
            response = await self.call_gemini(prompt)
            questions_data = self._parse_multiple_questions_response(response, subject, num_questions)
            
            return self.create_response(
                success=True,
                data={
                    "questions": questions_data["questions"],
                    "total": questions_data["total"],
                    "subject": subject,
                    "quiz_mode": True
                },
                message=f"{subject.capitalize()} dersi için {questions_data['total']} soru hazırladım!",
                next_action="start_quiz"
            )
            
        except Exception as e:
            logger.error(f"Multiple question generation failed: {e}")
            
            # Fallback'i demo'ye questions
            demo_questions = self._create_demo_questions(subject, num_questions)
            
            return self.create_response(
                success=True,
                data={
                    "questions": demo_questions,
                    "total": len(demo_questions),
                    "subject": subject,
                    "quiz_mode": True
                },
                message=f"{subject.capitalize()} dersi için {len(demo_questions)} soru hazırladım!",
                next_action="start_quiz"
            )
    
    async def _generate_single_question(self, subject: str, student_data: Dict[str, Any], original_message: str) -> AgentResponse:
        """Üret a single Soru (not quiz mode)"""
        
        # Get random topic eğer not specified
        topic = self._extract_topic_from_message(original_message, subject)
        if not topic:
            topic = self._get_random_topic(subject)
        
        # Üret single Soru ile Gemini
        prompt = f"""
        YKS {subject} TEK için dersi BİR SORU oluştur.
        
        Konu: {topic}
        Özellikler:
        1. YKS formatında (5 seçenekli: A, B, C, D, E)
        2. Net ve anlaşılır soru metni
        3. Çözümü kolay açıklanabilir
        4. Gerçekçi yanıltıcı seçenekler
        
        SADECE şu JSON formatında döndür:
        {{
            "Soru": "Soru metni burada",
            "options": {{
                "A": "İlk seçenek",
                "B": "İkinci seçenek", 
                "C": "Üçüncü seçenek",
                "D": "Dördüncü seçenek",
                "E": "Beşinci seçenek"
            }},
            "correct_answer": "Doğru cevap harfi (A, B, C, D veya E)",
            "explanation": "Sorunun çözümü ve açıklaması",
            "topic": "{topic}",
            "difficulty": "orta"
        }}
        """
        
        try:
            response = await self.call_gemini(prompt)
            question_data = self._parse_single_question_response(response, subject, topic)
            
            return self.create_response(
                success=True,
                data={
                    "question": question_data,
                    "subject": subject,
                    "topic": topic,
                    "single_mode": True
                },
                message=f"{subject.capitalize()} - {topic} konusundan bir soru hazırladım!",
                next_action="answer_question"
            )
            
        except Exception as e:
            logger.error(f"Single question generation failed: {e}")
            
            # Fallback'i demo'ye Soru
            demo_question = self._create_demo_single_question(subject, topic)
            
            return self.create_response(
                success=True,
                data={
                    "question": demo_question,
                    "subject": subject,
                    "topic": topic,
                    "single_mode": True
                },
                message=f"{subject.capitalize()} - {topic} konusundan bir soru hazırladım!",
                next_action="answer_question"
            )
            
    def _parse_multiple_questions_response(self, response: str, subject: str, num_questions: int) -> Dict[str, Any]:
        """Ayrıştır Yanıt containing multiple questions"""
        try:
            import re
            import json
            
            # Try'i extract'ye JSON Dizi
            json_match = re.search(r'\[.*?\]', response, re.DOTALL)
            if json_match:
                questions = json.loads(json_match.group())
                
                # Doğrula questions
                valid_questions = []
                q questions için in:
                    if isinstance(q, dict) and "question" in q and "options" in q:
                        q["subject"] = subject
                        valid_questions.append(q)
                
                if valid_questions:
                    return {"questions": valid_questions, "total": len(valid_questions)}
        
        except Exception as e:
            logger.error(f"Failed multiple için parse questions: {e}")
        
        # Fallback
        return {"questions": self._create_demo_questions(subject, num_questions), "total": num_questions}
    
    def _create_demo_questions(self, subject: str, num_questions: int) -> List[Dict[str, Any]]:
        """Oluştur demo questions for fallback"""
        
        demo_questions = {
            "fizik": [
                {
                    "question": "Düzgün doğrusal hareket yapan bir cismin hızı 20 m/s ise, 5 saniyede aldığı yol kaç metredir?",
                    "options": {"A": "80", "B": "90", "C": "100", "D": "110", "E": "120"},
                    "correct_answer": "C",
                    "explanation": "Düzgün doğrusal harekette: Yol = Hız × Zaman = 20 m/s × 5 s = 100 m",
                    "topic": "Hareket",
                    "difficulty": "kolay"
                },
                {
                    "question": "Kütlesi 2 kg olan bir cisme 10 N kuvvet uygulanıyor. Cismin ivmesi kaç m/s² dir?",
                    "options": {"A": "3", "B": "4", "C": "5", "D": "6", "E": "8"},
                    "correct_answer": "C",
                    "explanation": "Newton'un 2. yasası: F = ma → a = F/m = 10 N / 2 kg = 5 m/s²",
                    "topic": "Kuvvet ve Hareket",
                    "difficulty": "orta"
                },
                {
                    "question": "Ses aşağıdakilerden için dalgaları hangisi doğrudur?",
                    "options": {"A": "Boşlukta yayılabilir", "B": "Elektromanyetik dalgadır", "C": "Boyuna dalgadır", "D": "Işık hızıyla yayılır", "E": "Frekansı değişmez"},
                    "correct_answer": "C",
                    "explanation": "Ses dalgaları mekanik boyuna dalgalardır. Boşlukta yayılamaz.",
                    "topic": "Dalgalar",
                    "difficulty": "orta"
                },
                {
                    "question": "Elektrik akımı hangi yönde hareket eder?",
                    "options": {"A": "Pozitiften negatife", "B": "Negatiften pozitife", "C": "Her iki yönde", "D": "Hareket etmez", "E": "Rastgeledir"},
                    "correct_answer": "A",
                    "explanation": "Konvensiyonel akım yönü pozitif kutuptan negatif kutba doğrudur.",
                    "topic": "Elektrik",
                    "difficulty": "kolay"
                },
                {
                    "question": "Fotoelektrik olayda, elektronların gereken için kopması minimum frekans nedir?",
                    "options": {"A": "Eşik frekansı", "B": "Maksimum frekans", "C": "Rezonans frekansı", "D": "Temel frekans", "E": "Kritik frekans"},
                    "correct_answer": "A",
                    "explanation": "Fotoelektrik olayda elektronların minimum için kopması eşik frekansı gereklidir.",
                    "topic": "Modern Fizik",
                    "difficulty": "zor"
                }
            ],
            "matematik": [
                {
                    "question": "2x + 5 = 13 denkleminin çözümü nedir?",
                    "options": {"A": "2", "B": "3", "C": "4", "D": "5", "E": "6"},
                    "correct_answer": "C",
                    "explanation": "2x + 5 = 13 → 2x = 8 → x = 4",
                    "topic": "Denklemler",
                    "difficulty": "kolay"
                },
                {
                    "question": "f(x) = x² + 2x - 3 fonksiyonunun kökleri nelerdir?",
                    "options": {"A": "1, 3", "B": "-3, 1", "C": "2, -1", "D": "-2, 1", "E": "3, -1"},
                    "correct_answer": "B",
                    "explanation": "x² + 2x - 3 = 0 → (x+3)(x-1) = 0 → x = -3 veya x = 1",
                    "topic": "Fonksiyonlar",
                    "difficulty": "orta"
                }
            ]
        }
        
        # Get questions the subject_questions için subject = demo_questions.get(subject, demo_questions["matematik"])
        
        # Return requested Sayı nin questions (repeat eğer needed)
        questions = []
        i range için in(num_questions):
            question = subject_questions[i % len(subject_questions)].copy()
            question["subject"] = subject
            question["question_number"] = i + 1
            questions.append(question)
        
        return questions
    
    def _calculate_optimal_difficulty(
        self, 
        performance: Dict[str, float], 
        recent_scores: List[int]
    ) -> str:
        """Hesapla optimal Soru difficulty based üzerinde Performans"""
        
        # Get Ortalama recent score
        avg_recent = sum(recent_scores) / len(recent_scores) if recent_scores else 50
        
        # Determine difficulty
        if avg_recent < 40:
            return "kolay"
        elif avg_recent < 70:
            return "orta"
        elif avg_recent < 90:
            return "zor"
        else:
            return "çok_zor"
    
    def _select_question_type(
        self, 
        subject: str, 
        topic: str, 
        performance: Dict[str, float]
    ) -> str:
        """Select appropriate Soru type"""
        
        available_types = self.question_types.get(subject, ["genel"])
        
        # eğer specific topic requested
        if topic ve topic in available_types:
            return topic
        
        # Select based üzerinde weakest Alan
        weakest_score = 100
        weakest_type = available_types[0]
        
        q_type available_types için in:
            score = performance.get(f"{subject}_{q_type}", 50)
            if score < weakest_score:
                weakest_score = score
                weakest_type = q_type
        
        return weakest_type
    
    def _calculate_recent_success_rate(self, recent_scores: List[int]) -> float:
        """Hesapla Başarılı recent'den rate scores"""
        if not recent_scores:
            return 50.0
        
        return sum(1 score recent_scores için in if score > 0) / len(recent_scores) * 100
    
    def _parse_question_response(self, response: str) -> Dict[str, Any]:
        """Ayrıştır Gemini Soru Yanıt"""
        try:
            # Extract Yanıt'den JSON
            import re
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                return json.loads(json_match.group())
        except:
            pass
        
        # Fallback structure
        return self._create_fallback_question()
    
    def _parse_quiz_response(self, response: str) -> Dict[str, Any]:
        """Ayrıştır quiz Yanıt"""
        try:
            # Extract Yanıt'den Dizi
            import re
            array_match = re.search(r'\[.*\]', response, re.DOTALL)
            if array_match:
                questions = json.loads(array_match.group())
                return {"questions": questions, "total": len(questions)}
        except:
            pass
        
        # Fallback
        return {
            "questions": [self._create_fallback_question() _ range için in(5)],
            "total": 5
        }
    
    def _parse_exam_response(
        self, 
        response: str, 
        structure: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Ayrıştır exam Yanıt"""
        # Would Ayrıştır actual Yanıt
        # For now, Oluştur structured exam
        return {
            "exam_type": structure["exam_type"],
            "sections": structure["sections"],
            "total_time": structure["total_time"],
            "total_questions": structure["total_questions"],
            "questions": {}  # Would be filled ile actual questions
        }
    
    def _get_exam_structure(self, exam_type: str) -> Dict[str, Any]:
        """Get exam structure mock exam için"""
        structures = {
            "TYT": {
                "exam_type": "TYT",
                "total_time": 165,  # minutes
                "total_questions": 120,
                "sections": {
                    "Türkçe": 40,
                    "Matematik": 40,
                    "Fen": 20,
                    "Sosyal": 20
                }
            },
            "AYT_SAY": {
                "exam_type": "AYT_SAY",
                "total_time": 180,
                "total_questions": 80,
                "sections": {
                    "Matematik": 40,
                    "Fizik": 14,
                    "Kimya": 13,
                    "Biyoloji": 13
                }
            }
        }
        
        return structures.get(exam_type, structures["TYT"])
    
    def _track_question_generation(
        self, 
        student_id: str, 
        question_data: Dict[str, Any]
    ):
        """Track generated questions for Analitik"""
        if student_id not in self.student_performance:
            self.student_performance[student_id] = {
                "questions_generated": 0,
                "topics": {},
                "difficulty_distribution": {}
            }
        
        perf = self.student_performance[student_id]
        perf["questions_generated"] += 1
        
        # Track topic
        topic = question_data.get("topic", "genel")
        perf["topics"][topic] = perf["topics"].get(topic, 0) + 1
        
        # Track difficulty
        difficulty = question_data.get("difficulty", "orta")
        perf["difficulty_distribution"][difficulty] = \
            perf["difficulty_distribution"].get(difficulty, 0) + 1
    
    def _calculate_adaptive_score(
        self, 
        difficulty: str, 
        performance: Dict[str, float]
    ) -> float:
        """Hesapla adaptive score Soru selection için"""
        base_scores = {
            "kolay": 0.3,
            "orta": 0.5,
            "zor": 0.7,
            "çok_zor": 0.9
        }
        
        base_score = base_scores.get(difficulty, 0.5)
        
        # Adjust based üzerinde Öğrenci Performans
        avg_performance = sum(performance.values()) / len(performance) if performance else 0.5
        
        # eğer Öğrenci is performing well, increase difficulty score
        # eğer struggling, decrease difficulty score
        adjustment = (avg_performance - 0.5) * 0.2
        
        return max(0, min(1, base_score + adjustment))
    
    def _evaluate_time_performance(
        self, 
        time_spent: int, 
        question: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Evaluate Zaman Performans"""
        estimated_time = question.get("estimated_time", 120)
        
        ratio = time_spent / estimated_time
        
        if ratio < 0.5:
            status = "çok_hızlı"
            message = "Çok hızlı çözdün, dikkatli ol!"
        elif ratio < 0.8:
            status = "hızlı"
            message = "Güzel tempo!"
        elif ratio < 1.2:
            status = "ideal"
            message = "Mükemmel zaman yönetimi!"
        elif ratio < 1.5:
            status = "yavaş"
            message = "Biraz hızlanmalısın"
        else:
            status = "çok_yavaş"
            message = "Zaman yönetimini geliştirmelisin"
        
        return {
            "status": status,
            "message": message,
            "time_spent": time_spent,
            "estimated_time": estimated_time,
            "efficiency": min(100, (estimated_time / time_spent) * 100)
        }
    
    def _adjust_difficulty(self, is_correct: bool, current_difficulty: str) -> str:
        """Adjust difficulty next Soru için"""
        difficulty_order = ["kolay", "orta", "zor", "çok_zor"]
        current_index = difficulty_order.index(current_difficulty)
        
        if is_correct ve current_index < len(difficulty_order) - 1:
            # Increase difficulty
            return difficulty_order[current_index + 1]
        elif not is_correct ve current_index > 0:
            # Decrease difficulty
            return difficulty_order[current_index - 1]
        
        return current_difficulty
    
    def _update_performance(
        self, 
        student_id: str, 
        question: Dict[str, Any],
        is_correct: bool,
        time_spent: int
    ):
        """Güncelle Öğrenci Performans Metrikler"""
        # Would Güncelle actual Performans tracking
        pass
    
    def _identify_mistake_patterns(self, mistakes: List[Dict]) -> List[str]:
        """Identify common mistake patterns"""
        patterns = []
        
        # Analyze mistakes for patterns
        # bu would use more sophisticated analysis
        if len(mistakes) > 5:
            patterns.append("Dikkat eksikliği")
        if any(m.get("type") == "calculation" m mistakes için in):
            patterns.append("Hesaplama hataları")
        if any(m.get("type") == "concept" m mistakes için in):
            patterns.append("Kavram karışıklığı")
        
        return patterns
    
    def _identify_weak_topics(self, mistakes: List[Dict]) -> List[str]:
        """Identify weak mistakes'den topics"""
        topic_errors = {}
        
        mistake mistakes için in:
            topic = mistake.get("topic", "genel")
            topic_errors[topic] = topic_errors.get(topic, 0) + 1
        
        # Sort tarafından Hata Sayım
        weak_topics = sorted(
            topic_errors.items(), 
            key=lambda x: x[1], 
            reverse=True
        )
        
        return [topic for topic, _ in weak_topics[:5]]
    
    def _create_improvement_plan(self, mistakes: List[Dict]) -> Dict[str, Any]:
        """Oluştur improvement plan based üzerinde mistakes"""
        weak_topics = self._identify_weak_topics(mistakes)
        
        return {
            "priority_topics": weak_topics[:3],
            "daily_practice": f"{len(weak_topics) * 5} soru",
            "focus_areas": self._identify_mistake_patterns(mistakes),
            "recommended_time": "Günde 2 saat"
        }
    
    def _create_fallback_question(self) -> Dict[str, Any]:
        """Oluştur fallback Soru demo için/Hata cases"""
        return {
            "question": "Bir sayının %20'si 40 ise, bu sayının %30'u kaçtır?",
            "options": {
                "A": "50",
                "B": "60",
                "C": "70",
                "D": "80",
                "E": "90"
            },
            "correct_answer": "B",
            "explanation": "Sayıyı x olarak alalım. x'in %20'si = 40 ise, 0.20x = 40, x = 200. x'in %30'u = 0.30 × 200 = 60",
            "solution_steps": [
                "x × 20/100 = 40",
                "x = 40 × 100/20 = 200",
                "200 × 30/100 = 60"
            ],
            "common_mistakes": [
                "Yüzde hesabını yanlış yapma",
                "Orantı kurma hatası"
            ],
            "tips": "Yüzde problemlerinde önce 'tamamı' bulun",
            "difficulty": "orta",
            "estimated_time": 2,
            "topic": "Yüzde Problemleri",
            "yks_frequency": "yüksek"
        }
    
    def _get_demo_question(self, subject: str, difficulty: str) -> AgentResponse:
        """Get demo Soru for presentations"""
        demo_question = self._create_fallback_question()
        demo_question["subject"] = subject
        demo_question["difficulty"] = difficulty
        
        return self.create_response(
            success=True,
            data=demo_question,
            message=f"Demo {subject} sorusu hazır!"
        )
    
    def _extract_topic_from_message(self, message: str, subject: str) -> Optional[str]:
        """Extract specific user'den topic message"""
        
        # Define comprehensive topic Veritabanı
        topic_keywords = {
            'matematik': {
                'limit': ['limit', 'süreklilik'],
                'türev': ['türev', 'derivative'],
                'integral': ['integral', 'entegral'],
                'logaritma': ['logaritma', 'log'],
                'üslü': ['üslü', 'üs', 'exponential'],
                'fonksiyon': ['fonksiyon', 'function'],
                'geometri': ['geometri', 'çember', 'üçgen', 'daire'],
                'trigonometri': ['trigonometri', 'sinüs', 'cos', 'tan'],
                'olasılık': ['olasılık', 'probability'],
                'denklem': ['denklem', 'equation'],
                'matris': ['matris', 'matrix'],
                'vektör': ['vektör', 'vector']
            },
            'fizik': {
                'hareket': ['hareket', 'hız', 'ivme', 'motion'],
                'kuvvet': ['kuvvet', 'newton', 'force'],
                'enerji': ['enerji', 'iş', 'güç', 'energy'],
                'momentum': ['momentum', 'çarpışma'],
                'elektrik': ['elektrik', 'akım', 'gerilim'],
                'manyetizma': ['manyetik', 'magnet'],
                'dalga': ['dalga', 'ses', 'ışık'],
                'modern fizik': ['atom', 'kuantum', 'rölativite'],
                'optik': ['optik', 'lens', 'ayna']
            },
            'kimya': {
                'atom': ['atom', 'elektron', 'proton'],
                'periyodik': ['periyodik', 'element'],
                'bağ': ['bağ', 'iyonik', 'kovalent'],
                'mol': ['mol', 'avogadro'],
                'asit-baz': ['asit', 'baz', 'ph'],
                'organik': ['organik', 'karbon'],
                'kinetik': ['hız', 'reaksiyon', 'kinetik'],
                'denge': ['denge', 'equilibrium']
            },
            'biyoloji': {
                'hücre': ['hücre', 'mitoz', 'mayoz'],
                'genetik': ['gen', 'dna', 'rna'],
                'ekoloji': ['ekosistem', 'çevre'],
                'evrim': ['evrim', 'doğal seçilim'],
                'sistem': ['sinir', 'dolaşım', 'solunum']
            }
        }
        
        message_lower = message.lower()
        
        for topic, keywords in topic_keywords.get(subject, {}).items():
            if any(keyword in message_lower keyword keywords için in):
                return topic.capitalize()
        
        return None
    
    def _get_random_topic(self, subject: str) -> str:
        """Get random topic given subject için"""
        
        topic_lists = {
            'matematik': [
                'Limit ve Süreklilik', 'Türev', 'İntegral', 'Logaritma', 'Üslü Sayılar',
                'Fonksiyonlar', 'Geometri', 'Trigonometri', 'Olasılık', 'Denklemler',
                'Matrisler', 'Vektörler', 'Diziler', 'Seriler', 'Analitik Geometri'
            ],
            'fizik': [
                'Hareket', 'Kuvvet ve Hareket', 'İş Enerji Güç', 'Momentum',
                'Elektrik ve Manyetizma', 'Dalgalar', 'Optik', 'Modern Fizik',
                'Akışkanlar', 'Termofizik', 'Atom Fiziği'
            ],
            'kimya': [
                'Atom ve Molekül', 'Periyodik Sistem', 'Kimyasal Bağlar',
                'Mol Kavramı', 'Asit ve Bazlar', 'Organik Kimya',
                'Reaksiyon Hızı', 'Kimyasal Denge', 'Elektrokimya'
            ],
            'biyoloji': [
                'Hücre Biyolojisi', 'Genetik', 'Ekoloji', 'Evrim',
                'İnsan Anatomisi', 'Bitki Biyolojisi', 'Hayvan Biyolojisi'
            ],
            'türkçe': [
                'Dil Bilgisi', 'Anlam Bilgisi', 'Sözcük Türleri', 'Cümle Bilgisi',
                'Paragraf', 'Metin Türleri', 'Anlatım Bozuklukları'
            ]
        }
        
        import random
        topics = topic_lists.get(subject, topic_lists['matematik'])
        return random.choice(topics)
    
    def _parse_single_question_response(self, response: str, subject: str, topic: str) -> Dict[str, Any]:
        """Ayrıştır single Soru Gemini'den Yanıt"""
        try:
            import re
            import json
            
            # Extract Yanıt'den JSON
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                question_data = json.loads(json_match.group())
                
                # Add Varsayılan values eğer missing
                question_data.setdefault('subject', subject)
                question_data.setdefault('topic', topic)
                question_data.setdefault('difficulty', 'orta')
                question_data.setdefault('id', f"q_{datetime.now().isoformat()}__single")
                
                return question_data
                
        except Exception as e:
            logger.error(f"parsing single hatası question response: {e}")
        
        # Fallback'i demo'ye Soru
        return self._create_demo_single_question(subject, topic)
    
    def _create_demo_single_question(self, subject: str, topic: str) -> Dict[str, Any]:
        """Oluştur demo single Soru"""
        
        demo_questions = {
            'matematik': {
                'Limit': {
                    "question": "lim(x→2) (x² - 4)/(x - 2) limitinin değeri nedir?",
                    "options": {"A": "0", "B": "2", "C": "4", "D": "8", "E": "Yoktur"},
                    "correct_answer": "C",
                    "explanation": "Paydayı sıfırlayan değerde hem pay hem payda sıfır olduğu için, pay ve payda faktörlerine ayrılır: (x² - 4) = (x-2)(x+2). Bu durumda limit (x+2) = 2+2 = 4 olur.",
                    "topic": "Limit",
                    "difficulty": "orta"
                },
                'Türev': {
                    "question": "f(x) = 3x² + 2x - 1 fonksiyonunun türevi nedir?",
                    "options": {"A": "6x + 2", "B": "3x + 1", "C": "6x", "D": "3x²", "E": "6x - 1"},
                    "correct_answer": "A",
                    "explanation": "Kuvvet kuralını uygulayarak: f'(x) = 3(2x) + 2(1) + 0 = 6x + 2",
                    "topic": "Türev",
                    "difficulty": "kolay"
                }
            },
            'fizik': {
                'Hareket': {
                    "question": "20 m/s hızla hareket eden bir araç 4 saniyede duruyorsa, ivmesi kaç m/s²'dir?",
                    "options": {"A": "-5", "B": "-4", "C": "4", "D": "5", "E": "-10"},
                    "correct_answer": "A",
                    "explanation": "a = (v - v₀)/t = (0 - 20)/4 = -5 m/s². Negatif işaret yavaşlamayı gösterir.",
                    "topic": "Hareket",
                    "difficulty": "orta"
                }
            }
        }
        
        subject_questions = demo_questions.get(subject, demo_questions['matematik'])
        topic_question = subject_questions.get(topic, list(subject_questions.values())[0])
        
        # Add Gerekli fields
        topic_question['subject'] = subject
        topic_question['id'] = f"demo_{subject}_{topic}_{datetime.now().timestamp()}"
        
        return topic_question