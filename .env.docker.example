# YKS Genius Docker Environment Configuration
# Copy this file to .env.docker and customize the values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=yks_genius
POSTGRES_USER=yks_user
POSTGRES_PASSWORD=yks_password
DATABASE_URL=postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=yks_redis_password
REDIS_URL=redis://:${REDIS_PASSWORD}@${REDIS_HOST}:${REDIS_PORT}/0

# ChromaDB Configuration
CHROMA_HOST=localhost
CHROMA_PORT=8001
CHROMA_URL=http://${CHROMA_HOST}:${CHROMA_PORT}

# =============================================================================
# PRODUCTION OVERRIDES (for your own server deployment)
# =============================================================================

# Replace localhost with your server IP/domain for production
# POSTGRES_HOST=your-server-ip.com
# REDIS_HOST=your-server-ip.com
# CHROMA_HOST=your-server-ip.com

# Strong passwords for production
# POSTGRES_PASSWORD=your_strong_postgres_password_here
# REDIS_PASSWORD=your_strong_redis_password_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Gemini API (Required)
GEMINI_API_KEY=your_gemini_api_key_here

# Application Security
SECRET_KEY=your_super_secret_key_minimum_32_characters_long
JWT_SECRET_KEY=your_jwt_secret_key_for_authentication

# Environment
ENVIRONMENT=development  # development, staging, production
DEBUG=true

# CORS Origins (adjust for your frontend URL)
CORS_ORIGINS=["http://localhost:3000","http://localhost:5173","http://localhost:5174"]

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================

# Redis Memory Limit (adjust based on available RAM)
REDIS_MAX_MEMORY=256mb

# Database Connection Pool
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# Gemini API Rate Limiting
GEMINI_RATE_LIMIT_PER_MINUTE=30
GEMINI_RATE_LIMIT_PER_DAY=1400  # Leave some buffer from 1500 limit

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Logging Level
LOG_LEVEL=INFO  # DEBUG, INFO, WARNING, ERROR

# Health Check Intervals (seconds)
DB_HEALTH_CHECK_INTERVAL=30
REDIS_HEALTH_CHECK_INTERVAL=30

# =============================================================================
# DEVELOPMENT TOOLS (only for development profile)
# =============================================================================

# pgAdmin Configuration
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin

# Redis Commander (no additional config needed)

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Backup Schedule (cron format)
BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM

# Backup Retention (days)
BACKUP_RETENTION_DAYS=7

# =============================================================================
# DEPLOYMENT SPECIFIC
# =============================================================================

# For Render.com backend deployment, use these instead of localhost:
# DATABASE_URL=******************************************************/yks_genius
# REDIS_URL=redis://:yks_redis_password@YOUR_SERVER_IP:6379/0
# CHROMA_HOST=YOUR_SERVER_IP

# Frontend URLs (for CORS)
# FRONTEND_URL=https://your-frontend.vercel.app
# BACKEND_URL=https://your-backend.onrender.com

# WebSocket URL for frontend
# VITE_WS_URL=wss://your-backend.onrender.com

# =============================================================================
# SECURITY NOTES
# =============================================================================

# 1. Never commit this file with real credentials to version control
# 2. Use strong passwords in production (minimum 16 characters)
# 3. Rotate secrets regularly
# 4. Use environment-specific values
# 5. Consider using secrets management for production

# =============================================================================
# QUICK SETUP COMMANDS
# =============================================================================

# 1. Copy this file: cp .env.docker.example .env.docker
# 2. Edit the values above, especially GEMINI_API_KEY
# 3. Deploy databases: ./scripts/deploy-databases.sh dev
# 4. Start your application with the environment variables