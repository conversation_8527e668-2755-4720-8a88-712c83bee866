# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
backend/venv/
backend/env/
backend/.venv
backend/.env
backend/*.db
backend/*.sqlite
backend/*.sqlite3
backend/chroma_db/
backend/logs/
backend/*.log
backend/dist/
backend/build/
backend/*.egg-info/

# Node
frontend/node_modules/
frontend/dist/
frontend/dist-ssr/
frontend/*.local
frontend/.env
frontend/.env.local
frontend/.env.*.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/
.hypothesis/
coverage/
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Temporary files
*.tmp
*.temp
*.bak
*.swp
*~

# Documentation build
docs/_build/
docs/_static/
docs/_templates/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/


PLAN.md
DAILY_TODOS.md
DOCKER_SETUP_SUMMARY.md
DETAILED_IMPLEMENTATION_PLAN.md
info.txt
strategy.txt
notes.txt

# Frontend
frontend/node_modules/
frontend/dist/
frontend/dist-ssr/
frontend/*.local
frontend/.env
frontend/.env.local
docs

DEMO_CHECKLIST.md
DEMO_PRESENTATION.md
HACKATHON_README.md
HOW_TO_RUN.md
QUICK_START_GUIDE.md
quick_start.sh
scripts/check-databases.sh
scripts/deploy-databases.sh
scripts/generate-keys.py
scripts/setup.sh

