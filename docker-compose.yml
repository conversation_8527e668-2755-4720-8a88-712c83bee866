# YKS Genius Veritabanı Stack'i
# Yerel geli<PERSON>rm<PERSON> ve self-hosted dağıtım için
version: '3.8'

services:
  # PostgreSQL - Ana Veritabanı
  postgres:
    image: postgres:15-alpine
    container_name: yks-postgres
    environment:
      POSTGRES_DB: yks_genius
      POSTGRES_USER: yks_user
      POSTGRES_PASSWORD: yks_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - yks-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U yks_user -d yks_genius"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis - Önbellekleme ve Oturumlar
  redis:
    image: redis:7-alpine
    container_name: yks-redis
    command: redis-server --appendonly yes --requirepass yks_redis_password
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./backend/config/redis.conf:/etc/redis/redis.conf
    networks:
      - yks-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ChromaDB - RAG için Vektör Veritabanı
  chromadb:
    image: chromadb/chroma:latest
    container_name: yks-chromadb
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
      - PERSIST_DIRECTORY=/chroma/chroma
    ports:
      - "8001:8000"
    volumes:
      - chromadb_data:/chroma/chroma
    networks:
      - yks-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Commander - Redis GUI (Sadece Geliştirme)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: yks-redis-commander
    environment:
      - REDIS_HOSTS=redis:yks-redis:6379:0:yks_redis_password
    ports:
      - "8081:8081"
    networks:
      - yks-network
    depends_on:
      - redis
    profiles:
      - dev
    restart: unless-stopped

  # pgAdmin - PostgreSQL GUI (Sadece Geliştirme)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: yks-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8080:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - yks-network
    depends_on:
      - postgres
    profiles:
      - dev
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  chromadb_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  yks-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16