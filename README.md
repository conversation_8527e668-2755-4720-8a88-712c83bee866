# YKS Genius - Multi-Agent <PERSON><PERSON><PERSON><PERSON> Hazırlık Sistemi

## <PERSON><PERSON>

YKS Genius, BTK Akademi AI Hackathon 2025 için geliştirdiğim yapay zeka destekli bir YKS hazırlık sistemidir. Google Gemini API'yi kullanarak öğrencilere kişiselleştirilmiş çalışma planları, adaptif sorular ve gerçek zamanlı mentorluk desteği sunuyor.

### Temel Özellikler

- Multi-Agent Sistem: 5 farklı AI ajanı ile kapsamlı destek
- Kişiselleştirilmiş Çalışma Planları: Her öğrenciye özel strateji belirleme
- Adaptif Soru Sistemi: Öğrenci performansına göre zorluk ayarlaması
- Gerçek Zamanlı İletişim: WebSocket ile anlık etkileşim
- Performans Analizi: Detaylı ilerleme takibi ve raporlama

## AI Ajanları

1. Strateji Ajanı: Çalışma planları oluşturma ve hedef belirle<PERSON>
2. İçerik Ajanı: <PERSON><PERSON> anlatımları ve detaylı açıklamalar
3. Pratik Ajanı: Adaptif sorular ve çözüm önerileri
4. Analitik Ajanı: Performans takibi ve ilerleme raporları
5. Mentor Ajanı: Motivasyon ve psikolojik destek

## Kullanılan Teknolojiler

### Backend
- FastAPI: Modern web framework
- LangGraph: Multi-agent sistem orkestrasyon
- LangChain: AI agent framework
- Google Gemini: LLM API
- ChromaDB: Vektör veritabanı
- SQLAlchemy: ORM
- WebSockets: Gerçek zamanlı iletişim

### Frontend
- React 18: UI framework
- Vite: Build tool
- Tailwind CSS: Styling
- Socket.io: WebSocket client
- React Query: Data fetching
- Recharts: Veri görselleştirme
- Zustand: State management

## Kurulum

### Gereksinimler
- Python 3.11+
- Node.js 18+
- Google Gemini API Key

### Backend Kurulumu

```bash
# Backend klasörüne git
cd backend

# Virtual environment oluştur
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Poetry ile bağımlılıkları yükle
pip install poetry
poetry install

# Environment değişkenlerini ayarla
cp .env.example .env
# .env dosyasını düzenle ve GEMINI_API_KEY ekle

# Sunucuyu başlat
python run.py
```

### Frontend Kurulumu

```bash
# Frontend klasörüne git
cd frontend

# Bağımlılıkları yükle
npm install

# Environment değişkenlerini ayarla
cp .env.example .env

# Development sunucusunu başlat
npm run dev
```

## API Dokümantasyonu

Backend çalıştıktan sonra:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Proje Yapısı

```
yks-genius/
├── backend/
│   ├── app/
│   │   ├── agents/         # AI ajanları
│   │   ├── api/           # REST ve WebSocket endpoints
│   │   ├── core/          # Çekirdek servisler
│   │   ├── models/        # Veri modelleri
│   │   └── main.py        # FastAPI uygulaması
│   └── data/              # YKS müfredat verileri
├── frontend/
│   ├── src/
│   │   ├── components/    # React bileşenleri
│   │   ├── hooks/         # Custom hooks
│   │   ├── services/      # API servisleri
│   │   └── App.jsx        # Ana uygulama
│   └── public/            # Statik dosyalar
└── docs/                  # Dokümantasyon
```

## Konfigürasyon

### Backend Environment Değişkenleri

```env
GEMINI_API_KEY=your-api-key
DATABASE_URL=sqlite:///./yks_genius.db
SECRET_KEY=your-secret-key
```

### Frontend Environment Değişkenleri

```env
VITE_API_URL=http://localhost:8000
VITE_WS_URL=ws://localhost:8000
```

## Test

```bash
# Backend testleri
cd backend
pytest

# Frontend testleri
cd frontend
npm test
```

## Performans

- Yanıt Süresi: <200ms (ortalama)
- Eşzamanlı Kullanıcı: 100+ WebSocket bağlantısı
- Gemini API Optimizasyonu: Önbellekleme ve batch işleme
- Uptime Hedefi: %99.9

## Deployment

### Backend (Render.com)
1. Render.com'da yeni Web Service oluştur
2. GitHub repo'sunu bağla
3. Environment değişkenlerini ayarla
4. Deploy et

### Frontend (Vercel)
1. Vercel'de yeni proje oluştur
2. GitHub repo'sunu bağla
3. Build ayarlarını yapılandır
4. Deploy et

## Geliştirici

Bireysel olarak BTK Akademi AI Hackathon 2025 için geliştirdim.

## Lisans

Bu proje BTK Akademi AI Hackathon 2025 kapsamında geliştirilmiştir.

## Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/yeni-ozellik`)
3. Değişikliklerinizi commit edin (`git commit -m 'Yeni özellik ekle'`)
4. Branch'e push yapın (`git push origin feature/yeni-ozellik`)
5. Pull Request açın

## İletişim

Sorularınız veya önerileriniz için issue açabilirsiniz.

---

YKS Genius - 2.25 milyon öğrenciye ücretsiz, kişiselleştirilmiş YKS hazırlık desteği sunmayı hedefliyor.